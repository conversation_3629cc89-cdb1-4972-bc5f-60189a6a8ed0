ts-jest[ts-compiler] (WARN) Using hybrid module kind (Node16/18/Next) is only supported in "isolatedModules: true". Please set "isolatedModules: true" in your tsconfig.json.
ts-jest[ts-compiler] (WARN) Using hybrid module kind (Node16/18/Next) is only supported in "isolatedModules: true". Please set "isolatedModules: true" in your tsconfig.json.
ts-jest[ts-compiler] (WARN) Using hybrid module kind (Node16/18/Next) is only supported in "isolatedModules: true". Please set "isolatedModules: true" in your tsconfig.json.
  console.log
    Trivial test running

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:32:17)

  console.log
    Analyzer test starting

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:37:17)

  console.log
    CodeAnalyzer initialized for root: D:\Code\cyzer\tests\fixtures\sample-project, using queries from: D:\Code\cyzer\queries

      at new CodeAnalyzer (src/core/CodeAnalyzer.ts:24:17)

  console.log
    Analyzing 1 files...

      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:56:17)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at src/moduleA.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { utilFunc } from './utils/helper.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:40:25)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at src/moduleA.js Raw node: "(import_statement (import_clause (namespace_import (identifier))) source: (string (string_fragment)))" 
    Code: import * as D from './data.ts';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:40:25)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at src/moduleA.js Raw node: "(import_statement (import_clause (identifier)) source: (string (string_fragment)))" 
    Code: import defaultExport from './moduleB.ts';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:40:25)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at src/moduleA.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (number))))" 
    Code: export const varA = 10;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:40:25)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at src/moduleA.js:7. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:40:25)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type arguments at src/moduleA.js:11. Children: [ 'member_expression' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:40:25)

  console.log
    Building relations graph...

      at CodeAnalyzer.buildRelations (src/core/CodeAnalyzer.ts:177:17)

  console.log
    Relations graph built: 1 nodes, 0 edges.

      at CodeAnalyzer.buildRelations (src/core/CodeAnalyzer.ts:266:17)

  console.log
    Analysis Summary: {
      totalFilesAnalyzed: 1,
      filesWithErrors: 0,
      elementsByType: { import: 3, function: 4, variable: 1 },
      totalElements: 8,
      languages: { javascript: 1 }
    }

      at CodeAnalyzer.summarizeResults (src/core/CodeAnalyzer.ts:292:17)

  console.log
    Analysis complete.

      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:96:17)

  console.log
    Analyzed files: [ 'src/moduleA.js' ]

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:44:17)

  console.log
    Detected elements: [
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 2,
          "startCol": 13,
          "endLine": 2,
          "endCol": 58,
          "startPos": 13,
          "endPos": 58
        },
        "filePath": "src/moduleA.js",
        "isExported": false,
        "importedNames": [],
        "source": "./utils/helper.js",
        "isDefault": false,
        "isNamespace": false
      },
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 3,
          "startCol": 13,
          "endLine": 3,
          "endCol": 44,
          "startPos": 71,
          "endPos": 102
        },
        "filePath": "src/moduleA.js",
        "isExported": false,
        "importedNames": [],
        "source": "./data.ts",
        "isDefault": false,
        "isNamespace": false
      },
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 4,
          "startCol": 13,
          "endLine": 4,
          "endCol": 54,
          "startPos": 115,
          "endPos": 156
        },
        "filePath": "src/moduleA.js",
        "isExported": false,
        "importedNames": [],
        "source": "./moduleB.ts",
        "isDefault": false,
        "isNamespace": false
      },
      {
        "type": "function",
        "name": "funcA",
        "location": {
          "startLine": 6,
          "startCol": 20,
          "endLine": 6,
          "endCol": 74,
          "startPos": 177,
          "endPos": 231
        },
        "filePath": "src/moduleA.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "function",
        "name": "funcA",
        "location": {
          "startLine": 6,
          "startCol": 20,
          "endLine": 6,
          "endCol": 74,
          "startPos": 177,
          "endPos": 231
        },
        "filePath": "src/moduleA.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "function",
        "name": "log",
        "location": {
          "startLine": 6,
          "startCol": 20,
          "endLine": 6,
          "endCol": 74,
          "startPos": 177,
          "endPos": 231
        },
        "filePath": "src/moduleA.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "function",
        "name": "utilFunc",
        "location": {
          "startLine": 6,
          "startCol": 20,
          "endLine": 6,
          "endCol": 74,
          "startPos": 177,
          "endPos": 231
        },
        "filePath": "src/moduleA.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "variable",
        "name": "varA",
        "location": {
          "startLine": 7,
          "startCol": 20,
          "endLine": 7,
          "endCol": 36,
          "startPos": 251,
          "endPos": 267
        },
        "filePath": "src/moduleA.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      }
    ]

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:46:17)

  console.log
    CodeAnalyzer initialized for root: D:\Code\cyzer\test-files, using queries from: D:\Code\cyzer\queries

      at new CodeAnalyzer (src/core/CodeAnalyzer.ts:24:17)

  console.log
    Analyzing 7 files...

      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:56:17)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at advanced.ts Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (arrow_function parameters: (formal_parameters) body: (statement_block)))))" 
    Code: export const arrow = () => {};

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 1)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at advanced.ts:9. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 1)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/index.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { A, aFunc, fromB, callB } from './a.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/index.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { B, bFunc, fromC, callC } from './b.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/index.js Raw node: "(import_statement (import_clause (identifier) (named_imports (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import C, { cFunc } from './c.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/index.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (object (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier)))))" 
    Code: export const summary = { A, B, C, aFunc, bFunc, cFunc, fromB, fromC, callB, callC };

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/index.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (number))))" 
    Code: export const B = 2;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/b.js:2. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/b.js Raw node: "(import_statement (import_clause (identifier) (named_imports (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import C, { cFunc } from './c.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (identifier))))" 
    Code: export const fromC = C;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/b.js:5. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (call_expression function: (identifier) arguments: (arguments)))))" 
    Code: export const callC = cFunc();

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/b.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (number))))" 
    Code: export const A = 1;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/a.js:2. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/a.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { B, bFunc } from './b.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (identifier))))" 
    Code: export const fromB = B;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/a.js:5. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (call_expression function: (identifier) arguments: (arguments)))))" 
    Code: export const callB = bFunc();

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/a.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:55:31)

  console.log
    Building relations graph...

      at CodeAnalyzer.buildRelations (src/core/CodeAnalyzer.ts:177:17)

  console.log
    Relations graph built: 7 nodes, 0 edges.

      at CodeAnalyzer.buildRelations (src/core/CodeAnalyzer.ts:266:17)

  console.log
    Analysis Summary: {
      totalFilesAnalyzed: 7,
      filesWithErrors: 0,
      elementsByType: {
        function: 16,
        class: 3,
        method: 4,
        interface: 1,
        type: 1,
        enum: 1,
        variable: 12,
        import: 5
      },
      totalElements: 43,
      languages: { javascript: 6, typescript: 1 }
    }

      at CodeAnalyzer.summarizeResults (src/core/CodeAnalyzer.ts:292:17)

  console.log
    Analysis complete.

      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:96:17)

  console.error
    advanced.js elements: [
      {
        "type": "function",
        "name": "exportedFunc",
        "location": {
          "startLine": 1,
          "startCol": 8,
          "endLine": 1,
          "endCol": 34,
          "startPos": 7,
          "endPos": 33
        },
        "filePath": "advanced.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "function",
        "name": "exportedFunc",
        "location": {
          "startLine": 1,
          "startCol": 8,
          "endLine": 1,
          "endCol": 34,
          "startPos": 7,
          "endPos": 33
        },
        "filePath": "advanced.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "function",
        "name": "localFunc",
        "location": {
          "startLine": 2,
          "startCol": 1,
          "endLine": 2,
          "endCol": 24,
          "startPos": 34,
          "endPos": 57
        },
        "filePath": "advanced.js",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "function",
        "name": "arrowFunc",
        "location": {
          "startLine": 3,
          "startCol": 7,
          "endLine": 3,
          "endCol": 27,
          "startPos": 64,
          "endPos": 84
        },
        "filePath": "advanced.js",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": true
      },
      {
        "type": "variable",
        "name": "arrowFunc",
        "location": {
          "startLine": 3,
          "startCol": 1,
          "endLine": 3,
          "endCol": 28,
          "startPos": 58,
          "endPos": 85
        },
        "filePath": "advanced.js",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "class",
        "name": "LocalClass",
        "location": {
          "startLine": 4,
          "startCol": 1,
          "endLine": 6,
          "endCol": 2,
          "startPos": 86,
          "endPos": 120
        },
        "filePath": "advanced.js",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "isAbstract": false,
        "methods": [
          {
            "type": "method",
            "name": "method",
            "location": {
              "startLine": 5,
              "startCol": 3,
              "endLine": 5,
              "endCol": 14,
              "startPos": 107,
              "endPos": 118
            },
            "filePath": "advanced.js",
            "isExported": false,
            "isAsync": false,
            "isGenerator": false,
            "isArrow": false,
            "parameters": []
          }
        ],
        "properties": []
      },
      {
        "type": "method",
        "name": "method",
        "location": {
          "startLine": 5,
          "startCol": 3,
          "endLine": 5,
          "endCol": 14,
          "startPos": 107,
          "endPos": 118
        },
        "filePath": "advanced.js",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "class",
        "name": "ExportedClass",
        "location": {
          "startLine": 7,
          "startCol": 16,
          "endLine": 7,
          "endCol": 38,
          "startPos": 136,
          "endPos": 158
        },
        "filePath": "advanced.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "isAbstract": false,
        "methods": [],
        "properties": []
      }
    ]

    [0m [90m 57 |[39m         expect(fileResult)[33m.[39mtoBeDefined()[33m;[39m
     [90m 58 |[39m         [36mconst[39m elements [33m=[39m fileResult[33m![39m[33m.[39melements[33m;[39m
    [31m[1m>[22m[39m[90m 59 |[39m         console[33m.[39merror([32m'advanced.js elements:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(elements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m    |[39m                 [31m[1m^[22m[39m
     [90m 60 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'exportedFunc'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mFunction[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
     [90m 61 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'localFunc'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mFunction[39m))[33m.[39mtoBeTruthy()[33m;[39m
     [90m 62 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'arrowFunc'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mFunction[39m))[33m.[39mtoBeTruthy()[33m;[39m[0m

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:59:17)

  console.log
    CodeAnalyzer initialized for root: D:\Code\cyzer\test-files, using queries from: D:\Code\cyzer\queries

      at new CodeAnalyzer (src/core/CodeAnalyzer.ts:24:17)

  console.log
    Analyzing 7 files...

      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:56:17)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at advanced.ts Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (arrow_function parameters: (formal_parameters) body: (statement_block)))))" 
    Code: export const arrow = () => {};

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 1)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at advanced.ts:9. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 1)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/index.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { A, aFunc, fromB, callB } from './a.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/index.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { B, bFunc, fromC, callC } from './b.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/index.js Raw node: "(import_statement (import_clause (identifier) (named_imports (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import C, { cFunc } from './c.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/index.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (object (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier)))))" 
    Code: export const summary = { A, B, C, aFunc, bFunc, cFunc, fromB, fromC, callB, callC };

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/index.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (number))))" 
    Code: export const B = 2;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/b.js:2. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/b.js Raw node: "(import_statement (import_clause (identifier) (named_imports (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import C, { cFunc } from './c.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (identifier))))" 
    Code: export const fromC = C;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/b.js:5. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (call_expression function: (identifier) arguments: (arguments)))))" 
    Code: export const callC = cFunc();

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/b.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 5)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (number))))" 
    Code: export const A = 1;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/a.js:2. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at mini-repo/a.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { B, bFunc } from './b.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (identifier))))" 
    Code: export const fromB = B;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/a.js:5. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at mini-repo/a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (call_expression function: (identifier) arguments: (arguments)))))" 
    Code: export const callB = bFunc();

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at mini-repo/a.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 6)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:70:31)

  console.log
    Building relations graph...

      at CodeAnalyzer.buildRelations (src/core/CodeAnalyzer.ts:177:17)

  console.log
    Relations graph built: 7 nodes, 0 edges.

      at CodeAnalyzer.buildRelations (src/core/CodeAnalyzer.ts:266:17)

  console.log
    Analysis Summary: {
      totalFilesAnalyzed: 7,
      filesWithErrors: 0,
      elementsByType: {
        function: 16,
        class: 3,
        method: 4,
        interface: 1,
        type: 1,
        enum: 1,
        variable: 12,
        import: 5
      },
      totalElements: 43,
      languages: { javascript: 6, typescript: 1 }
    }

      at CodeAnalyzer.summarizeResults (src/core/CodeAnalyzer.ts:292:17)

  console.log
    Analysis complete.

      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:96:17)

  console.error
    advanced.ts elements: [
      {
        "type": "class",
        "name": "MyClass",
        "location": {
          "startLine": 1,
          "startCol": 16,
          "endLine": 5,
          "endCol": 2,
          "startPos": 15,
          "endPos": 120
        },
        "filePath": "advanced.ts",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "isAbstract": false,
        "methods": [
          {
            "type": "method",
            "name": "staticMethod",
            "location": {
              "startLine": 2,
              "startCol": 5,
              "endLine": 2,
              "endCol": 29,
              "startPos": 35,
              "endPos": 59
            },
            "filePath": "advanced.ts",
            "isExported": false,
            "isAsync": false,
            "isGenerator": false,
            "isArrow": false,
            "parameters": []
          },
          {
            "type": "method",
            "name": "value",
            "location": {
              "startLine": 3,
              "startCol": 5,
              "endLine": 3,
              "endCol": 31,
              "startPos": 64,
              "endPos": 90
            },
            "filePath": "advanced.ts",
            "isExported": false,
            "isAsync": false,
            "isGenerator": false,
            "isArrow": false,
            "parameters": []
          },
          {
            "type": "method",
            "name": "value",
            "location": {
              "startLine": 4,
              "startCol": 5,
              "endLine": 4,
              "endCol": 28,
              "startPos": 95,
              "endPos": 118
            },
            "filePath": "advanced.ts",
            "isExported": false,
            "isAsync": false,
            "isGenerator": false,
            "isArrow": false,
            "parameters": [
              {
                "name": "v",
                "type": "number"
              }
            ]
          }
        ],
        "properties": []
      },
      {
        "type": "method",
        "name": "staticMethod",
        "location": {
          "startLine": 2,
          "startCol": 5,
          "endLine": 2,
          "endCol": 29,
          "startPos": 35,
          "endPos": 59
        },
        "filePath": "advanced.ts",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "method",
        "name": "value",
        "location": {
          "startLine": 3,
          "startCol": 5,
          "endLine": 3,
          "endCol": 31,
          "startPos": 64,
          "endPos": 90
        },
        "filePath": "advanced.ts",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "method",
        "name": "value",
        "location": {
          "startLine": 4,
          "startCol": 5,
          "endLine": 4,
          "endCol": 28,
          "startPos": 95,
          "endPos": 118
        },
        "filePath": "advanced.ts",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": [
          {
            "name": "v",
            "type": "number"
          }
        ]
      },
      {
        "type": "interface",
        "name": "MyInterface",
        "location": {
          "startLine": 6,
          "startCol": 8,
          "endLine": 6,
          "endCol": 45,
          "startPos": 128,
          "endPos": 165
        },
        "filePath": "advanced.ts",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "type",
        "name": "MyType",
        "location": {
          "startLine": 7,
          "startCol": 8,
          "endLine": 7,
          "endCol": 38,
          "startPos": 173,
          "endPos": 203
        },
        "filePath": "advanced.ts",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "enum",
        "name": "MyEnum",
        "location": {
          "startLine": 8,
          "startCol": 8,
          "endLine": 8,
          "endCol": 31,
          "startPos": 211,
          "endPos": 234
        },
        "filePath": "advanced.ts",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "variable",
        "name": "arrow",
        "location": {
          "startLine": 9,
          "startCol": 8,
          "endLine": 9,
          "endCol": 31,
          "startPos": 242,
          "endPos": 265
        },
        "filePath": "advanced.ts",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "function",
        "name": "arrow",
        "location": {
          "startLine": 9,
          "startCol": 14,
          "endLine": 9,
          "endCol": 30,
          "startPos": 248,
          "endPos": 264
        },
        "filePath": "advanced.ts",
        "isExported": false,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": true
      },
      {
        "type": "variable",
        "name": "arrow",
        "location": {
          "startLine": 9,
          "startCol": 8,
          "endLine": 9,
          "endCol": 31,
          "startPos": 242,
          "endPos": 265
        },
        "filePath": "advanced.ts",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "function",
        "name": "generatorFunc",
        "location": {
          "startLine": 10,
          "startCol": 8,
          "endLine": 10,
          "endCol": 46,
          "startPos": 273,
          "endPos": 311
        },
        "filePath": "advanced.ts",
        "isExported": true,
        "isAsync": false,
        "isGenerator": true,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "function",
        "name": "generatorFunc",
        "location": {
          "startLine": 10,
          "startCol": 8,
          "endLine": 10,
          "endCol": 46,
          "startPos": 273,
          "endPos": 311
        },
        "filePath": "advanced.ts",
        "isExported": true,
        "isAsync": false,
        "isGenerator": true,
        "isArrow": false,
        "parameters": []
      }
    ]

    [0m [90m 72 |[39m         expect(fileResult)[33m.[39mtoBeDefined()[33m;[39m
     [90m 73 |[39m         [36mconst[39m elements [33m=[39m fileResult[33m![39m[33m.[39melements[33m;[39m
    [31m[1m>[22m[39m[90m 74 |[39m         console[33m.[39merror([32m'advanced.ts elements:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(elements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m    |[39m                 [31m[1m^[22m[39m
     [90m 75 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'MyClass'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mClass[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
     [90m 76 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'MyInterface'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mInterface[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
     [90m 77 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'MyType'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mType[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m[0m

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:74:17)

  console.log
    CodeAnalyzer initialized for root: D:\Code\cyzer\test-files\mini-repo, using queries from: D:\Code\cyzer\queries

      at new CodeAnalyzer (src/core/CodeAnalyzer.ts:24:17)

  console.log
    Analyzing 4 files...

      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:56:17)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at index.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { A, aFunc, fromB, callB } from './a.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at index.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { B, bFunc, fromC, callC } from './b.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at index.js Raw node: "(import_statement (import_clause (identifier) (named_imports (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import C, { cFunc } from './c.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at index.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (object (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier) (shorthand_property_identifier)))))" 
    Code: export const summary = { A, B, C, aFunc, bFunc, cFunc, fromB, fromC, callB, callC };

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at index.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 0)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (number))))" 
    Code: export const B = 2;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 2)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at b.js:2. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 2)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at b.js Raw node: "(import_statement (import_clause (identifier) (named_imports (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import C, { cFunc } from './c.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 2)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (identifier))))" 
    Code: export const fromC = C;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 2)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at b.js:5. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 2)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at b.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (call_expression function: (identifier) arguments: (arguments)))))" 
    Code: export const callC = cFunc();

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 2)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at b.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 2)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (number))))" 
    Code: export const A = 1;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at a.js:2. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: import_statement at a.js Raw node: "(import_statement (import_clause (named_imports (import_specifier name: (identifier)) (import_specifier name: (identifier)))) source: (string (string_fragment)))" 
    Code: import { B, bFunc } from './b.js';

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (identifier))))" 
    Code: export const fromB = B;

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at a.js:5. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.error
    [DEBUG] createBaseElement: Handling node type: export_statement at a.js Raw node: "(export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) value: (call_expression function: (identifier) arguments: (arguments)))))" 
    Code: export const callB = bFunc();

    [0m [90m 252 |[39m         [90m// --- Improve Logging for import/export statements ---[39m
     [90m 253 |[39m         [36mif[39m (definitionNode[33m.[39mtype [33m===[39m [32m'import_statement'[39m [33m||[39m definitionNode[33m.[39mtype [33m===[39m [32m'export_statement'[39m) {
    [31m[1m>[22m[39m[90m 254 |[39m             console[33m.[39merror([32m'[DEBUG] createBaseElement: Handling node type:'[39m[33m,[39m definitionNode[33m.[39mtype[33m,[39m [32m'at'[39m[33m,[39m filePath[33m,[39m [32m'Raw node:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(definitionNode[33m.[39mtoString())[33m,[39m [32m'\nCode:'[39m[33m,[39m getNodeText(definitionNode[33m,[39m code))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 255 |[39m         }
     [90m 256 |[39m
     [90m 257 |[39m         [90m// Special handling for import_statement: always create an ImportElement, even without a name[39m[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:254:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.warn
    [DEBUG] Skipping element: Could not determine name for node type export_statement at a.js:6. Children: [ 'lexical_declaration' ]

    [0m [90m 328 |[39m         }
     [90m 329 |[39m         [36mif[39m ([33m![39mnameNode) {
    [31m[1m>[22m[39m[90m 330 |[39m             console[33m.[39mwarn([32m`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`[39m[33m,[39m definitionNode[33m.[39mnamedChildren[33m.[39mmap(child [33m=>[39m child[33m.[39mtype))[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 331 |[39m             [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 332 |[39m         }
     [90m 333 |[39m         [36mreturn[39m {[0m

      at TreeSitterParser.createBaseElement (src/parsers/TreeSitterParser.ts:330:21)
      at TreeSitterParser.processCaptures (src/parsers/TreeSitterParser.ts:224:32)
      at TreeSitterParser.parse (src/parsers/TreeSitterParser.ts:124:43)
      at src/core/CodeAnalyzer.ts:62:48
          at async Promise.all (index 3)
      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:82:25)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:86:31)

  console.log
    Building relations graph...

      at CodeAnalyzer.buildRelations (src/core/CodeAnalyzer.ts:177:17)

  console.log
    Relations graph built: 4 nodes, 0 edges.

      at CodeAnalyzer.buildRelations (src/core/CodeAnalyzer.ts:266:17)

  console.log
    Analysis Summary: {
      totalFilesAnalyzed: 4,
      filesWithErrors: 0,
      elementsByType: { import: 5, variable: 9, function: 6 },
      totalElements: 20,
      languages: { javascript: 4 }
    }

      at CodeAnalyzer.summarizeResults (src/core/CodeAnalyzer.ts:292:17)

  console.log
    Analysis complete.

      at CodeAnalyzer.analyzeProject (src/core/CodeAnalyzer.ts:96:17)

  console.error
    mini-repo a.js elements: [
      {
        "type": "variable",
        "name": "A",
        "location": {
          "startLine": 2,
          "startCol": 8,
          "endLine": 2,
          "endCol": 20,
          "startPos": 15,
          "endPos": 27
        },
        "filePath": "a.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "function",
        "name": "aFunc",
        "location": {
          "startLine": 3,
          "startCol": 8,
          "endLine": 3,
          "endCol": 38,
          "startPos": 35,
          "endPos": 65
        },
        "filePath": "a.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "function",
        "name": "aFunc",
        "location": {
          "startLine": 3,
          "startCol": 8,
          "endLine": 3,
          "endCol": 38,
          "startPos": 35,
          "endPos": 65
        },
        "filePath": "a.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false,
        "parameters": []
      },
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 4,
          "startCol": 1,
          "endLine": 4,
          "endCol": 35,
          "startPos": 66,
          "endPos": 100
        },
        "filePath": "a.js",
        "isExported": false,
        "importedNames": [],
        "source": "./b.js",
        "isDefault": false,
        "isNamespace": false
      },
      {
        "type": "variable",
        "name": "fromB",
        "location": {
          "startLine": 5,
          "startCol": 8,
          "endLine": 5,
          "endCol": 24,
          "startPos": 108,
          "endPos": 124
        },
        "filePath": "a.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "variable",
        "name": "callB",
        "location": {
          "startLine": 6,
          "startCol": 8,
          "endLine": 6,
          "endCol": 30,
          "startPos": 132,
          "endPos": 154
        },
        "filePath": "a.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      },
      {
        "type": "variable",
        "name": "callB",
        "location": {
          "startLine": 6,
          "startCol": 8,
          "endLine": 6,
          "endCol": 30,
          "startPos": 132,
          "endPos": 154
        },
        "filePath": "a.js",
        "isExported": true,
        "isAsync": false,
        "isGenerator": false,
        "isArrow": false
      }
    ]

    [0m [90m 93 |[39m         [90m// Check that imports/exports are detected[39m
     [90m 94 |[39m         [36mconst[39m aFile [33m=[39m files[33m.[39mfind(f [33m=>[39m f[33m.[39mfilePath[33m.[39mendsWith([32m'a.js'[39m))[33m![39m[33m;[39m
    [31m[1m>[22m[39m[90m 95 |[39m         console[33m.[39merror([32m'mini-repo a.js elements:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(aFile[33m.[39melements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m    |[39m                 [31m[1m^[22m[39m
     [90m 96 |[39m         [36mconst[39m importElements [33m=[39m aFile[33m.[39melements[33m.[39mfilter((e[33m:[39m any) [33m=>[39m e[33m.[39mtype [33m===[39m [32m'import'[39m)[33m;[39m
     [90m 97 |[39m         console[33m.[39merror([32m'mini-repo a.js imports:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(importElements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m 98 |[39m         expect(aFile[33m.[39melements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'A'[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m[0m

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:95:17)

  console.error
    mini-repo a.js imports: [
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 4,
          "startCol": 1,
          "endLine": 4,
          "endCol": 35,
          "startPos": 66,
          "endPos": 100
        },
        "filePath": "a.js",
        "isExported": false,
        "importedNames": [],
        "source": "./b.js",
        "isDefault": false,
        "isNamespace": false
      }
    ]

    [0m [90m  95 |[39m         console[33m.[39merror([32m'mini-repo a.js elements:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(aFile[33m.[39melements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m  96 |[39m         [36mconst[39m importElements [33m=[39m aFile[33m.[39melements[33m.[39mfilter((e[33m:[39m any) [33m=>[39m e[33m.[39mtype [33m===[39m [32m'import'[39m)[33m;[39m
    [31m[1m>[22m[39m[90m  97 |[39m         console[33m.[39merror([32m'mini-repo a.js imports:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(importElements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m     |[39m                 [31m[1m^[22m[39m
     [90m  98 |[39m         expect(aFile[33m.[39melements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'A'[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
     [90m  99 |[39m         expect(aFile[33m.[39melements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'aFunc'[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
     [90m 100 |[39m         [90m// Print all import elements in all files for better debugging[39m[0m

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:97:17)

  console.error
    mini-repo index.js imports: [
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 2,
          "startCol": 1,
          "endLine": 2,
          "endCol": 49,
          "startPos": 12,
          "endPos": 60
        },
        "filePath": "index.js",
        "isExported": false,
        "importedNames": [],
        "source": "./a.js",
        "isDefault": false,
        "isNamespace": false
      },
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 3,
          "startCol": 1,
          "endLine": 3,
          "endCol": 49,
          "startPos": 61,
          "endPos": 109
        },
        "filePath": "index.js",
        "isExported": false,
        "importedNames": [],
        "source": "./b.js",
        "isDefault": false,
        "isNamespace": false
      },
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 4,
          "startCol": 1,
          "endLine": 4,
          "endCol": 35,
          "startPos": 110,
          "endPos": 144
        },
        "filePath": "index.js",
        "isExported": false,
        "importedNames": [],
        "source": "./c.js",
        "isDefault": false,
        "isNamespace": false
      }
    ]

    [0m [90m 102 |[39m             [36mconst[39m importElements [33m=[39m f[33m.[39melements[33m.[39mfilter((e[33m:[39m any) [33m=>[39m e[33m.[39mtype [33m===[39m [32m'import'[39m)[33m;[39m
     [90m 103 |[39m             [36mif[39m (importElements[33m.[39mlength [33m>[39m [35m0[39m) {
    [31m[1m>[22m[39m[90m 104 |[39m                 console[33m.[39merror([32m`mini-repo ${f.filePath} imports:`[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(importElements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m     |[39m                         [31m[1m^[22m[39m
     [90m 105 |[39m             }
     [90m 106 |[39m         })[33m;[39m
     [90m 107 |[39m         [90m// Relations/edges: summary should exist in projectResult.relations[39m[0m

      at tests/CodeAnalyzer.test.ts:104:25
          at Array.forEach (<anonymous>)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:101:15)

  console.error
    mini-repo b.js imports: [
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 4,
          "startCol": 1,
          "endLine": 4,
          "endCol": 35,
          "startPos": 66,
          "endPos": 100
        },
        "filePath": "b.js",
        "isExported": false,
        "importedNames": [],
        "source": "./c.js",
        "isDefault": false,
        "isNamespace": false
      }
    ]

    [0m [90m 102 |[39m             [36mconst[39m importElements [33m=[39m f[33m.[39melements[33m.[39mfilter((e[33m:[39m any) [33m=>[39m e[33m.[39mtype [33m===[39m [32m'import'[39m)[33m;[39m
     [90m 103 |[39m             [36mif[39m (importElements[33m.[39mlength [33m>[39m [35m0[39m) {
    [31m[1m>[22m[39m[90m 104 |[39m                 console[33m.[39merror([32m`mini-repo ${f.filePath} imports:`[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(importElements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m     |[39m                         [31m[1m^[22m[39m
     [90m 105 |[39m             }
     [90m 106 |[39m         })[33m;[39m
     [90m 107 |[39m         [90m// Relations/edges: summary should exist in projectResult.relations[39m[0m

      at tests/CodeAnalyzer.test.ts:104:25
          at Array.forEach (<anonymous>)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:101:15)

  console.error
    mini-repo a.js imports: [
      {
        "type": "import",
        "name": "import",
        "location": {
          "startLine": 4,
          "startCol": 1,
          "endLine": 4,
          "endCol": 35,
          "startPos": 66,
          "endPos": 100
        },
        "filePath": "a.js",
        "isExported": false,
        "importedNames": [],
        "source": "./b.js",
        "isDefault": false,
        "isNamespace": false
      }
    ]

    [0m [90m 102 |[39m             [36mconst[39m importElements [33m=[39m f[33m.[39melements[33m.[39mfilter((e[33m:[39m any) [33m=>[39m e[33m.[39mtype [33m===[39m [32m'import'[39m)[33m;[39m
     [90m 103 |[39m             [36mif[39m (importElements[33m.[39mlength [33m>[39m [35m0[39m) {
    [31m[1m>[22m[39m[90m 104 |[39m                 console[33m.[39merror([32m`mini-repo ${f.filePath} imports:`[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(importElements[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m     |[39m                         [31m[1m^[22m[39m
     [90m 105 |[39m             }
     [90m 106 |[39m         })[33m;[39m
     [90m 107 |[39m         [90m// Relations/edges: summary should exist in projectResult.relations[39m[0m

      at tests/CodeAnalyzer.test.ts:104:25
          at Array.forEach (<anonymous>)
      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:101:15)

  console.error
    mini-repo relations: {
      "options": {
        "type": "directed",
        "multi": false,
        "allowSelfLoops": false
      },
      "attributes": {},
      "nodes": [
        {
          "key": "file:index.js",
          "attributes": {
            "type": "file",
            "filePath": "index.js",
            "language": "javascript",
            "errorCount": 0,
            "elementCount": 4,
            "label": "index.js"
          }
        },
        {
          "key": "file:c.js",
          "attributes": {
            "type": "file",
            "filePath": "c.js",
            "language": "javascript",
            "errorCount": 0,
            "elementCount": 2,
            "label": "c.js"
          }
        },
        {
          "key": "file:b.js",
          "attributes": {
            "type": "file",
            "filePath": "b.js",
            "language": "javascript",
            "errorCount": 0,
            "elementCount": 7,
            "label": "b.js"
          }
        },
        {
          "key": "file:a.js",
          "attributes": {
            "type": "file",
            "filePath": "a.js",
            "language": "javascript",
            "errorCount": 0,
            "elementCount": 7,
            "label": "a.js"
          }
        }
      ],
      "edges": []
    }

    [0m [90m 107 |[39m         [90m// Relations/edges: summary should exist in projectResult.relations[39m
     [90m 108 |[39m         expect(projectResult[33m.[39mrelations)[33m.[39mtoBeDefined()[33m;[39m
    [31m[1m>[22m[39m[90m 109 |[39m         console[33m.[39merror([32m'mini-repo relations:'[39m[33m,[39m [33mJSON[39m[33m.[39mstringify(projectResult[33m.[39mrelations[33m,[39m [36mnull[39m[33m,[39m [35m2[39m))[33m;[39m
     [90m     |[39m                 [31m[1m^[22m[39m
     [90m 110 |[39m         [36mif[39m (projectResult[33m.[39mrelations [33m&&[39m [33mArray[39m[33m.[39misArray(projectResult[33m.[39mrelations[33m.[39medges)) {
     [90m 111 |[39m             [36mconst[39m edge [33m=[39m projectResult[33m.[39mrelations[33m.[39medges[33m.[39mfind((edge[33m:[39m any) [33m=>[39m edge[33m.[39msource [33m&&[39m edge[33m.[39mtarget [33m&&[39m edge[33m.[39msource[33m.[39mincludes([32m'a.js'[39m) [33m&&[39m edge[33m.[39mtarget[33m.[39mincludes([32m'b.js'[39m))[33m;[39m
     [90m 112 |[39m             expect(edge)[33m.[39mtoBeDefined()[33m;[39m[0m

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:109:17)

FAIL tests/CodeAnalyzer.test.ts (7.172 s)
  CodeAnalyzer
    √ trivial test: jest should run this (13 ms)
    √ should detect exported functions in a simple JS file (181 ms)
    √ should detect elements in advanced.js (191 ms)
    × should detect elements in advanced.ts (168 ms)
    × should detect imports/exports and relations in mini-repo (153 ms)

  ● CodeAnalyzer › should detect elements in advanced.ts

    expect(received).toBeTruthy()

    Received: false

    [0m [90m 77 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'MyType'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mType[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
     [90m 78 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'MyEnum'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mEnum[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
    [31m[1m>[22m[39m[90m 79 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'arrow'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mFunction[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
     [90m    |[39m                                                                                                                      [31m[1m^[22m[39m
     [90m 80 |[39m         expect(elements[33m.[39msome((e[33m:[39m any) [33m=>[39m e[33m.[39mname [33m===[39m [32m'generatorFunc'[39m [33m&&[39m e[33m.[39mtype [33m===[39m [33mCodeElementType[39m[33m.[39m[33mFunction[39m [33m&&[39m e[33m.[39misExported))[33m.[39mtoBeTruthy()[33m;[39m
     [90m 81 |[39m     })[33m;[39m
     [90m 82 |[39m[0m

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:79:118)

  ● CodeAnalyzer › should detect imports/exports and relations in mini-repo

    projectResult.relations.edges is not an array: {"options":{"type":"directed","multi":false,"allowSelfLoops":false},"attributes":{},"nodes":[{"key":"file:index.js","attributes":{"type":"file","filePath":"index.js","language":"javascript","errorCount":0,"elementCount":4,"label":"index.js"}},{"key":"file:c.js","attributes":{"type":"file","filePath":"c.js","language":"javascript","errorCount":0,"elementCount":2,"label":"c.js"}},{"key":"file:b.js","attributes":{"type":"file","filePath":"b.js","language":"javascript","errorCount":0,"elementCount":7,"label":"b.js"}},{"key":"file:a.js","attributes":{"type":"file","filePath":"a.js","language":"javascript","errorCount":0,"elementCount":7,"label":"a.js"}}],"edges":[]}

    [0m [90m 112 |[39m             expect(edge)[33m.[39mtoBeDefined()[33m;[39m
     [90m 113 |[39m         } [36melse[39m {
    [31m[1m>[22m[39m[90m 114 |[39m             [36mthrow[39m [36mnew[39m [33mError[39m([32m'projectResult.relations.edges is not an array: '[39m [33m+[39m [33mJSON[39m[33m.[39mstringify(projectResult[33m.[39mrelations))[33m;[39m
     [90m     |[39m                   [31m[1m^[22m[39m
     [90m 115 |[39m         }
     [90m 116 |[39m     })[33m;[39m
     [90m 117 |[39m[0m

      at Object.<anonymous> (tests/CodeAnalyzer.test.ts:114:19)

----------------------|---------|----------|---------|---------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------
File                  | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                                                                                                                                               
----------------------|---------|----------|---------|---------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------
All files             |   76.29 |    67.25 |   96.29 |   76.29 |                                                                                                                                                                 
 core                 |    70.5 |    54.34 |     100 |    70.5 |                                                                                                                                                                 
  CodeAnalyzer.ts     |    70.5 |    54.34 |     100 |    70.5 | 33-35,65-66,73-74,77-79,89-92,109-110,125-127,132-134,141-143,148-157,163-169,196-199,206-208,219-239,241-243,249-262                                           
 parsers              |   72.83 |    71.77 |   94.44 |   72.83 |                                                                                                                                                                 
  TreeSitterParser.ts |   72.83 |    71.77 |   94.44 |   72.83 | 18-21,53-61,75-76,95-96,105-106,113-114,127-129,131-133,144-145,149-151,179-180,232-233,282-283,286-287,382-407,414-430,433-463,467-469,506-516,529-538,561-582 
 types                |     100 |      100 |     100 |     100 |                                                                                                                                                                 
  CodeElement.ts      |     100 |      100 |     100 |     100 |                                                                                                                                                                 
----------------------|---------|----------|---------|---------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------
Test Suites: 1 failed, 1 total
Tests:       2 failed, 3 passed, 5 total
Snapshots:   0 total
Time:        7.6 s, estimated 11 s
Ran all test suites.
