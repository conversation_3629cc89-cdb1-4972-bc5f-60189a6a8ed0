/**
 * NestJS Framework Plugin
 * 
 * Self-contained NestJS analysis that builds on TypeScript plugin.
 * Focuses on NestJS-specific patterns: DI, controllers, services, modules.
 */

import { 
  AnalysisPlugin, 
  PluginMetadata, 
  PluginCapabilities, 
  PluginAnalysisContext, 
  PluginAnalysisResult 
} from '../core/PluginSystem.js';
import { CodeElement, CodeElementType } from '../types/CodeElement.js';
import { TypeScriptPlugin } from '../typescript-plugin/TypeScriptPlugin.js';
import { ControllerAnalyzer } from './analyzers/ControllerAnalyzer.js';
import { ServiceAnalyzer } from './analyzers/ServiceAnalyzer.js';
import { ModuleAnalyzer } from './analyzers/ModuleAnalyzer.js';
import { DIAnalyzer } from './analyzers/DIAnalyzer.js';
import { NestJSTypes } from './types/NestJSTypes.js';

export class NestJSPlugin implements AnalysisPlugin {
  metadata: PluginMetadata = {
    name: 'nestjs-plugin',
    version: '1.0.0',
    description: 'NestJS framework analysis with DI, controllers, and modules',
    author: 'Cyzer Team',
    
    languages: ['typescript'],
    frameworks: ['nestjs'],
    fileExtensions: ['.ts'],
    
    capabilities: {
      syntaxAnalysis: true,
      semanticAnalysis: true,
      crossFileAnalysis: true,
      typeInference: true,
      dependencyTracking: true,     // ✅ DI analysis
      callGraphGeneration: true,
      frameworkPatterns: true,      // ✅ NestJS-specific patterns
      decoratorAnalysis: true,      // ✅ Uses decorators from TS plugin
      componentAnalysis: true,      // ✅ Controllers, services, modules
      incrementalAnalysis: false,
      largeCodebaseOptimized: true
    },
    
    priority: 300, // High priority for framework analysis
    dependencies: ['typescript-plugin'], // ✅ Builds on TypeScript plugin
    
    requiresTypeScript: true,
    requiresNodeModules: true,   // Needs @nestjs/* packages
    memoryIntensive: false
  };
  
  // Dependencies
  private typeScriptPlugin: TypeScriptPlugin;
  
  // NestJS-specific analyzers
  private controllerAnalyzer: ControllerAnalyzer;
  private serviceAnalyzer: ServiceAnalyzer;
  private moduleAnalyzer: ModuleAnalyzer;
  private diAnalyzer: DIAnalyzer;
  
  constructor() {
    this.typeScriptPlugin = new TypeScriptPlugin();
    this.controllerAnalyzer = new ControllerAnalyzer();
    this.serviceAnalyzer = new ServiceAnalyzer();
    this.moduleAnalyzer = new ModuleAnalyzer();
    this.diAnalyzer = new DIAnalyzer();
  }
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    // Initialize TypeScript plugin first
    await this.typeScriptPlugin.initialize(projectRoot, options);
    
    // Initialize NestJS analyzers
    await this.controllerAnalyzer.initialize(projectRoot);
    await this.serviceAnalyzer.initialize(projectRoot);
    await this.moduleAnalyzer.initialize(projectRoot);
    await this.diAnalyzer.initialize(projectRoot);
  }
  
  async cleanup(): Promise<void> {
    await this.typeScriptPlugin.cleanup();
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    // Handle TypeScript files in NestJS projects
    return language === 'typescript' && 
           (framework === 'nestjs' || this.detectNestJSFile(filePath));
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    const startTime = Date.now();
    
    try {
      // 1. Get TypeScript analysis (includes decorators)
      const tsResult = await this.typeScriptPlugin.analyze(context);
      
      // 2. Extract decorators from TypeScript result
      const decoratorElements = this.typeScriptPlugin.getDecoratorElements(tsResult);
      
      // 3. NestJS-specific analysis
      const nestjsAnalysis = await this.analyzeNestJSPatterns(
        context, 
        tsResult, 
        decoratorElements
      );
      
      // 4. Merge results
      const mergedElements = this.mergeWithNestJSEnhancements(
        tsResult.elements,
        nestjsAnalysis.elements
      );
      
      // 5. Share NestJS data
      const sharedData = new Map([
        ...(tsResult.sharedData || []),
        ['nestjs-analysis', nestjsAnalysis],
        ['nestjs-architecture', this.extractArchitecture(mergedElements)]
      ]);
      
      return {
        elements: mergedElements,
        errors: [
          ...(tsResult.errors || []),
          ...(nestjsAnalysis.errors || [])
        ],
        warnings: nestjsAnalysis.warnings || [],
        metadata: {
          framework: 'nestjs',
          architecture: {
            controllers: nestjsAnalysis.controllers?.length || 0,
            services: nestjsAnalysis.services?.length || 0,
            modules: nestjsAnalysis.modules?.length || 0,
            endpoints: nestjsAnalysis.endpoints?.length || 0
          },
          dependencyComplexity: this.calculateDIComplexity(nestjsAnalysis),
          hasGuards: nestjsAnalysis.hasGuards || false,
          hasInterceptors: nestjsAnalysis.hasInterceptors || false
        },
        analysisTime: Date.now() - startTime,
        memoryUsed: tsResult.memoryUsed,
        confidence: 0.95,
        sharedData
      };
      
    } catch (error) {
      return {
        elements: [],
        errors: [`NestJS analysis failed: ${error}`],
        warnings: [],
        metadata: {},
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0,
      };
    }
  }
  
  /**
   * Detect if file is part of NestJS project
   */
  private detectNestJSFile(filePath: string): boolean {
    // NestJS file naming conventions
    const nestjsPatterns = [
      '.controller.',
      '.service.',
      '.module.',
      '.guard.',
      '.interceptor.',
      '.pipe.',
      '.filter.',
      '.middleware.'
    ];
    
    return nestjsPatterns.some(pattern => filePath.includes(pattern));
  }
  
  /**
   * Analyze NestJS-specific patterns
   */
  private async analyzeNestJSPatterns(
    context: PluginAnalysisContext,
    tsResult: PluginAnalysisResult,
    decoratorElements: any[]
  ): Promise<{
    elements: CodeElement[];
    errors: string[];
    warnings: string[];
    controllers?: NestJSTypes.Controller[];
    services?: NestJSTypes.Service[];
    modules?: NestJSTypes.Module[];
    endpoints?: NestJSTypes.Endpoint[];
    hasGuards?: boolean;
    hasInterceptors?: boolean;
  }> {
    
    const elements: CodeElement[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Filter NestJS decorators
    const nestjsDecorators = decoratorElements.filter(d => 
      this.isNestJSDecorator(d.name || d.decoratorName)
    );
    
    // Analyze controllers
    const controllerAnalysis = await this.controllerAnalyzer.analyze(
      context, 
      nestjsDecorators,
      tsResult.elements
    );
    elements.push(...controllerAnalysis.elements);
    errors.push(...controllerAnalysis.errors);
    
    // Analyze services
    const serviceAnalysis = await this.serviceAnalyzer.analyze(
      context,
      nestjsDecorators,
      tsResult.elements
    );
    elements.push(...serviceAnalysis.elements);
    errors.push(...serviceAnalysis.errors);
    
    // Analyze modules
    const moduleAnalysis = await this.moduleAnalyzer.analyze(
      context,
      nestjsDecorators,
      tsResult.elements
    );
    elements.push(...moduleAnalysis.elements);
    errors.push(...moduleAnalysis.errors);
    
    // Analyze dependency injection
    const diAnalysis = await this.diAnalyzer.analyze(
      context,
      nestjsDecorators,
      tsResult.elements
    );
    elements.push(...diAnalysis.elements);
    errors.push(...diAnalysis.errors);
    
    // Detect patterns and issues
    const patternAnalysis = this.analyzePatterns(nestjsDecorators);
    warnings.push(...patternAnalysis.warnings);
    
    return {
      elements,
      errors,
      warnings,
      controllers: controllerAnalysis.controllers,
      services: serviceAnalysis.services,
      modules: moduleAnalysis.modules,
      endpoints: controllerAnalysis.endpoints,
      hasGuards: nestjsDecorators.some(d => d.decoratorName === 'UseGuards'),
      hasInterceptors: nestjsDecorators.some(d => d.decoratorName === 'UseInterceptors')
    };
  }
  
  /**
   * Check if decorator is NestJS-specific
   */
  private isNestJSDecorator(decoratorName: string): boolean {
    const nestjsDecorators = [
      // Core decorators
      'Controller', 'Injectable', 'Module',
      
      // HTTP decorators
      'Get', 'Post', 'Put', 'Delete', 'Patch', 'Options', 'Head',
      
      // Parameter decorators
      'Body', 'Query', 'Param', 'Headers', 'Req', 'Res',
      
      // DI decorators
      'Inject', 'Optional', 'Self', 'SkipSelf', 'Host',
      
      // Enhancement decorators
      'UseGuards', 'UseInterceptors', 'UsePipes', 'UseFilters',
      
      // Validation decorators (class-validator)
      'IsString', 'IsNumber', 'IsEmail', 'IsOptional', 'Min', 'Max',
      
      // Swagger decorators
      'ApiTags', 'ApiOperation', 'ApiResponse', 'ApiParam'
    ];
    
    return nestjsDecorators.includes(decoratorName);
  }
  
  /**
   * Merge TypeScript elements with NestJS enhancements
   */
  private mergeWithNestJSEnhancements(
    tsElements: CodeElement[],
    nestjsElements: CodeElement[]
  ): CodeElement[] {
    
    const elementMap = new Map<string, CodeElement>();
    
    // Add TypeScript elements as base
    tsElements.forEach(el => {
      const key = `${el.name}-${el.location.startLine}`;
      elementMap.set(key, el);
    });
    
    // Enhance with NestJS-specific information
    nestjsElements.forEach(nestjsEl => {
      const key = `${nestjsEl.name}-${nestjsEl.location.startLine}`;
      const existing = elementMap.get(key);
      
      if (existing) {
        // Enhance existing element with NestJS info
        elementMap.set(key, {
          ...existing,
          type: nestjsEl.type, // Override with NestJS-specific type
          metadata: {
            ...existing.metadata,
            ...nestjsEl.metadata,
            framework: 'nestjs'
          }
        });
      } else {
        // Add as new NestJS element
        elementMap.set(key, nestjsEl);
      }
    });
    
    return Array.from(elementMap.values());
  }
  
  /**
   * Analyze NestJS patterns and detect issues
   */
  private analyzePatterns(decorators: any[]): { warnings: string[] } {
    const warnings: string[] = [];
    
    // Check for common anti-patterns
    const hasControllers = decorators.some(d => d.decoratorName === 'Controller');
    const hasServices = decorators.some(d => d.decoratorName === 'Injectable');
    
    if (hasControllers && !hasServices) {
      warnings.push('Controllers found without Injectable services - consider service layer separation');
    }
    
    // Check for missing validation
    const hasEndpoints = decorators.some(d => 
      ['Get', 'Post', 'Put', 'Delete'].includes(d.decoratorName)
    );
    const hasValidation = decorators.some(d => 
      d.decoratorName?.startsWith('Is') || d.decoratorName === 'Body'
    );
    
    if (hasEndpoints && !hasValidation) {
      warnings.push('API endpoints found without validation decorators - consider adding input validation');
    }
    
    return { warnings };
  }
  
  /**
   * Extract architecture overview
   */
  private extractArchitecture(elements: CodeElement[]): any {
    const controllers = elements.filter(e => e.type === CodeElementType.NestJSController);
    const services = elements.filter(e => e.type === CodeElementType.NestJSService);
    const modules = elements.filter(e => e.type === CodeElementType.NestJSModule);
    
    return {
      controllers: controllers.map(c => ({ name: c.name, file: c.filePath })),
      services: services.map(s => ({ name: s.name, file: s.filePath })),
      modules: modules.map(m => ({ name: m.name, file: m.filePath })),
      totalElements: elements.length
    };
  }
  
  /**
   * Calculate dependency injection complexity
   */
  private calculateDIComplexity(analysis: any): 'low' | 'medium' | 'high' {
    const totalServices = analysis.services?.length || 0;
    const totalControllers = analysis.controllers?.length || 0;
    const totalElements = totalServices + totalControllers;
    
    if (totalElements < 5) return 'low';
    if (totalElements < 20) return 'medium';
    return 'high';
  }
}
