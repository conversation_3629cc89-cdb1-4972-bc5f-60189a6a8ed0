/**
 * NestJS Framework Plugin
 * 
 * Self-contained NestJS analysis that builds on TypeScript plugin.
 * Focuses on NestJS-specific patterns: DI, controllers, services, modules.
 */

import {
  AnalysisPlugin,
  PluginMetadata,
  PluginCapabilities,
  PluginAnalysisContext,
  PluginAnalysisResult
} from '../../core/PluginSystem.js';
import { CodeElement, CodeElementType } from '../../types/CodeElement.js';
import { TypeScriptPlugin } from '../typescript-plugin/TypeScriptPlugin.js';

export class NestJSPlugin implements AnalysisPlugin {
  metadata: PluginMetadata = {
    name: 'nestjs-plugin',
    version: '1.0.0',
    description: 'NestJS framework analysis with DI, controllers, and modules',
    author: 'Cyzer Team',
    
    languages: ['typescript'],
    frameworks: ['nestjs'],
    fileExtensions: ['.ts'],
    
    capabilities: {
      syntaxAnalysis: true,
      semanticAnalysis: true,
      crossFileAnalysis: true,
      typeInference: true,
      dependencyTracking: true,     // ✅ DI analysis
      callGraphGeneration: true,
      frameworkPatterns: true,      // ✅ NestJS-specific patterns
      decoratorAnalysis: true,      // ✅ Uses decorators from TS plugin
      componentAnalysis: true,      // ✅ Controllers, services, modules
      incrementalAnalysis: false,
      largeCodebaseOptimized: true
    },
    
    priority: 300, // High priority for framework analysis
    dependencies: ['typescript-plugin'], // ✅ Builds on TypeScript plugin
    
    requiresTypeScript: true,
    requiresNodeModules: true,   // Needs @nestjs/* packages
    memoryIntensive: false
  };
  
  // Dependencies
  private typeScriptPlugin: TypeScriptPlugin;

  constructor() {
    this.typeScriptPlugin = new TypeScriptPlugin();
  }
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    // Initialize TypeScript plugin first
    await this.typeScriptPlugin.initialize(projectRoot, options);
  }
  
  async cleanup(): Promise<void> {
    await this.typeScriptPlugin.cleanup();
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    // Handle TypeScript files in NestJS projects
    return language === 'typescript' && 
           (framework === 'nestjs' || this.detectNestJSFile(filePath));
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    const startTime = Date.now();
    
    try {
      // 1. Get TypeScript analysis (includes decorators)
      const tsResult = await this.typeScriptPlugin.analyze(context);

      // 2. Extract decorators from TypeScript result
      const decoratorElements = this.typeScriptPlugin.getDecoratorElements(tsResult);

      // 3. NestJS-specific analysis (basic implementation)
      const nestjsEnhancements = this.analyzeNestJSBasic(decoratorElements, context);

      // 4. Merge results
      const mergedElements = this.mergeWithNestJSEnhancements(
        tsResult.elements,
        nestjsEnhancements
      );

      // 5. Share NestJS data
      const sharedData = new Map([
        ...(tsResult.sharedData || []),
        ['nestjs-elements', nestjsEnhancements],
        ['nestjs-architecture', this.extractArchitecture(mergedElements)]
      ]);
      
      return {
        elements: mergedElements,
        errors: tsResult.errors || [],
        warnings: [],
        metadata: {
          framework: 'nestjs',
          architecture: {
            controllers: nestjsEnhancements.controllers.length,
            services: nestjsEnhancements.services.length,
            modules: nestjsEnhancements.modules.length,
            endpoints: nestjsEnhancements.endpoints.length
          },
          hasGuards: nestjsEnhancements.hasGuards,
          hasInterceptors: nestjsEnhancements.hasInterceptors
        },
        analysisTime: Date.now() - startTime,
        memoryUsed: tsResult.memoryUsed,
        confidence: 0.85, // Lower confidence for basic implementation
        sharedData
      };
      
    } catch (error) {
      return {
        elements: [],
        errors: [`NestJS analysis failed: ${error}`],
        warnings: [],
        metadata: {},
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0,
      };
    }
  }
  
  /**
   * Detect if file is part of NestJS project
   */
  private detectNestJSFile(filePath: string): boolean {
    // NestJS file naming conventions
    const nestjsPatterns = [
      '.controller.',
      '.service.',
      '.module.',
      '.guard.',
      '.interceptor.',
      '.pipe.',
      '.filter.',
      '.middleware.'
    ];
    
    return nestjsPatterns.some(pattern => filePath.includes(pattern));
  }
  
  /**
   * Basic NestJS analysis
   */
  private analyzeNestJSBasic(decoratorElements: CodeElement[], context: PluginAnalysisContext): {
    controllers: CodeElement[];
    services: CodeElement[];
    modules: CodeElement[];
    endpoints: CodeElement[];
    hasGuards: boolean;
    hasInterceptors: boolean;
  } {

    const controllers: CodeElement[] = [];
    const services: CodeElement[] = [];
    const modules: CodeElement[] = [];
    const endpoints: CodeElement[] = [];

    // Filter NestJS decorators
    const nestjsDecorators = decoratorElements.filter(d =>
      this.isNestJSDecorator(d.name?.replace('@', '') || d.metadata?.decoratorName)
    );

    // Categorize decorators
    for (const decorator of nestjsDecorators) {
      const decoratorName = decorator.name?.replace('@', '') || decorator.metadata?.decoratorName;

      if (decoratorName === 'Controller') {
        controllers.push({
          ...decorator,
          type: CodeElementType.NestJSController,
          metadata: {
            ...decorator.metadata,
            framework: 'nestjs',
            decoratorType: 'controller'
          }
        });
      } else if (decoratorName === 'Injectable') {
        services.push({
          ...decorator,
          type: CodeElementType.NestJSService,
          metadata: {
            ...decorator.metadata,
            framework: 'nestjs',
            decoratorType: 'service'
          }
        });
      } else if (decoratorName === 'Module') {
        modules.push({
          ...decorator,
          type: CodeElementType.NestJSModule,
          metadata: {
            ...decorator.metadata,
            framework: 'nestjs',
            decoratorType: 'module'
          }
        });
      } else if (['Get', 'Post', 'Put', 'Delete', 'Patch'].includes(decoratorName)) {
        endpoints.push({
          ...decorator,
          type: CodeElementType.NestJSEndpoint,
          metadata: {
            ...decorator.metadata,
            framework: 'nestjs',
            httpMethod: decoratorName.toUpperCase(),
            decoratorType: 'endpoint'
          }
        });
      }
    }

    const hasGuards = nestjsDecorators.some(d =>
      (d.name?.replace('@', '') || d.metadata?.decoratorName) === 'UseGuards'
    );
    const hasInterceptors = nestjsDecorators.some(d =>
      (d.name?.replace('@', '') || d.metadata?.decoratorName) === 'UseInterceptors'
    );

    return {
      controllers,
      services,
      modules,
      endpoints,
      hasGuards,
      hasInterceptors
    };
  }
  
  /**
   * Check if decorator is NestJS-specific
   */
  private isNestJSDecorator(decoratorName: string): boolean {
    const nestjsDecorators = [
      // Core decorators
      'Controller', 'Injectable', 'Module',
      
      // HTTP decorators
      'Get', 'Post', 'Put', 'Delete', 'Patch', 'Options', 'Head',
      
      // Parameter decorators
      'Body', 'Query', 'Param', 'Headers', 'Req', 'Res',
      
      // DI decorators
      'Inject', 'Optional', 'Self', 'SkipSelf', 'Host',
      
      // Enhancement decorators
      'UseGuards', 'UseInterceptors', 'UsePipes', 'UseFilters',
      
      // Validation decorators (class-validator)
      'IsString', 'IsNumber', 'IsEmail', 'IsOptional', 'Min', 'Max',
      
      // Swagger decorators
      'ApiTags', 'ApiOperation', 'ApiResponse', 'ApiParam'
    ];
    
    return nestjsDecorators.includes(decoratorName);
  }
  
  /**
   * Merge TypeScript elements with NestJS enhancements
   */
  private mergeWithNestJSEnhancements(
    tsElements: CodeElement[],
    nestjsEnhancements: {
      controllers: CodeElement[];
      services: CodeElement[];
      modules: CodeElement[];
      endpoints: CodeElement[];
      hasGuards: boolean;
      hasInterceptors: boolean;
    }
  ): CodeElement[] {

    // Combine all NestJS elements
    const nestjsElements = [
      ...nestjsEnhancements.controllers,
      ...nestjsEnhancements.services,
      ...nestjsEnhancements.modules,
      ...nestjsEnhancements.endpoints
    ];

    // Merge with TypeScript elements
    const allElements = [...tsElements, ...nestjsElements];

    // Remove duplicates based on name and location
    const elementMap = new Map<string, CodeElement>();

    for (const element of allElements) {
      const key = `${element.name}-${element.location.startLine}`;
      const existing = elementMap.get(key);

      if (existing) {
        // Enhance existing element
        elementMap.set(key, {
          ...existing,
          type: element.metadata?.framework === 'nestjs' ? element.type : existing.type,
          metadata: {
            ...existing.metadata,
            ...element.metadata
          }
        });
      } else {
        elementMap.set(key, element);
      }
    }

    return Array.from(elementMap.values());
  }
  
  /**
   * Extract architecture overview
   */
  private extractArchitecture(elements: CodeElement[]): any {
    const controllers = elements.filter(e => e.type === CodeElementType.NestJSController);
    const services = elements.filter(e => e.type === CodeElementType.NestJSService);
    const modules = elements.filter(e => e.type === CodeElementType.NestJSModule);
    const endpoints = elements.filter(e => e.type === CodeElementType.NestJSEndpoint);

    return {
      controllers: controllers.map(c => ({ name: c.name, file: c.filePath })),
      services: services.map(s => ({ name: s.name, file: s.filePath })),
      modules: modules.map(m => ({ name: m.name, file: m.filePath })),
      endpoints: endpoints.map(e => ({
        name: e.name,
        method: e.metadata?.httpMethod,
        file: e.filePath
      })),
      totalElements: elements.length
    };
  }
}
