
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/CodeAnalyzer.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">core</a> CodeAnalyzer.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.5% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>208/295</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.34% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>25/46</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>9/9</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.5% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>208/295</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">17x</span>
<span class="cline-any cline-yes">17x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">114x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">114x</span>
<span class="cline-any cline-yes">114x</span>
<span class="cline-any cline-yes">114x</span>
<span class="cline-any cline-yes">114x</span>
<span class="cline-any cline-yes">19x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span></td><td class="text"><pre class="prettyprint lang-js">import fs from 'fs/promises';
import path from 'path';
import { glob } from 'glob';
import { TreeSitterParser } from '../parsers/TreeSitterParser';
import {
    FileAnalysisResult, ProjectAnalysisResult, AnalysisOptions,
    CodeElement, CodeElementType, ImportElement, ExportElement
} from '../types/CodeElement';
import Graph from 'graphology';
import writeGraphML from 'graphology-graphml';
import ignore, { Ignore } from 'ignore';
&nbsp;
export class CodeAnalyzer {
    private parser: TreeSitterParser;
    private projectRoot: string;
    private fileExtensionMap: Map&lt;string, string[]&gt; = new Map(); // Cache resolved file paths by base path
    private queryDir: string;
&nbsp;
    constructor(projectRoot: string, queryDirectory?: string) {
        this.projectRoot = path.resolve(projectRoot);
        this.parser = new TreeSitterParser();
        // Default query dir relative to CWD, allow override
        this.queryDir = queryDirectory || path.resolve(process.cwd(), 'queries');
        console.log(`CodeAnalyzer initialized for root: ${this.projectRoot}, using queries from: ${this.queryDir}`);
    }
&nbsp;
    async initialize(queryDirOverride?: string) {
        const dirToUse = queryDirOverride || this.queryDir;
        try {
            await this.parser.initQueries(dirToUse);
            await this.buildFileExtensionMap(); // Pre-build map for faster resolution
        } <span class="branch-0 cbranch-no" title="branch not covered" >catch (error) {</span>
<span class="cstat-no" title="statement not covered" >            console.error(`Initialization failed: ${error}`);</span>
<span class="cstat-no" title="statement not covered" >            throw error; // Re-throw to indicate failure</span>
<span class="cstat-no" title="statement not covered" >        }</span>
    }
&nbsp;
    // Build a map for resolving module paths without extensions
    private async buildFileExtensionMap() {
        this.fileExtensionMap.clear();
        const allFiles = await this.findProjectFiles(true); // Find all files initially
        allFiles.forEach(filePath =&gt; {
            const parsedPath = path.parse(filePath);
            const basePath = path.join(parsedPath.dir, parsedPath.name); // Path without extension
            if (!this.fileExtensionMap.has(basePath)) {
                this.fileExtensionMap.set(basePath, []);
            }
            this.fileExtensionMap.get(basePath)?.push(filePath); // Store full path
        });
    }
&nbsp;
    async analyzeProject(options: AnalysisOptions = {}): Promise&lt;ProjectAnalysisResult&gt; {
        const results: ProjectAnalysisResult = { files: [], errors: [], projectRoot: this.projectRoot };
        const filePaths = await this.findProjectFiles(); // Get analyzable files respecting .gitignore
&nbsp;
        console.log(`Analyzing ${filePaths.length} files...`);
&nbsp;
        const analysisPromises = filePaths.map(async (relativePath) =&gt; {
            const absolutePath = path.join(this.projectRoot, relativePath);
            try {
                const content = await fs.readFile(absolutePath, 'utf-8');
                const fileResult = this.parser.parse(content, relativePath);
                // Optional: Filter elements based on options.targetElementTypes here
                if (options.targetElementTypes<span class="branch-0 cbranch-no" title="branch not covered" > &amp;&amp; options.targetElementTypes.length &gt; 0)</span> <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                   fileResult.elements = fileResult.elements.filter(el =&gt; options.targetElementTypes?.includes(el.type));</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // Optional: Remove fullText if not requested
                if (!options.includeElementContent) {
                    fileResult.elements.forEach(el =&gt; delete el.fullText);
                }
&nbsp;
                if (fileResult.errors &amp;&amp; fileResult.errors.length &gt; 0) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                    console.warn(`Errors analyzing ${relativePath}: ${fileResult.errors.join(', ')}`);</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                return fileResult;
            } <span class="branch-0 cbranch-no" title="branch not covered" >catch (error: any) {</span>
<span class="cstat-no" title="statement not covered" >                console.error(`Failed to read or parse file ${relativePath}:`, error);</span>
<span class="cstat-no" title="statement not covered" >                return { filePath: relativePath, elements: [], errors: [`Read/Parse failed: ${error.message}`] };</span>
<span class="cstat-no" title="statement not covered" >            }</span>
        });
&nbsp;
        results.files = await Promise.all(analysisPromises);
&nbsp;
        try {
            results.relations = this.buildRelations(results.files);
            // Convert graphology graph to a serializable format if needed for JSON output
            // results.relations = (results.relations as Graph).export();
        } <span class="branch-0 cbranch-no" title="branch not covered" >catch (relationError: any) {</span>
<span class="cstat-no" title="statement not covered" >             results.errors?.push(`Failed to build relations: ${relationError.message}`);</span>
<span class="cstat-no" title="statement not covered" >             console.error("Error building relations:", relationError);</span>
<span class="cstat-no" title="statement not covered" >             results.relations = null; // Indicate failure</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        results.summary = this.summarizeResults(results.files);
&nbsp;
        console.log(`Analysis complete.`);
        return results;
    }
&nbsp;
    // Modified to optionally skip ignore patterns for map building
    private async findProjectFiles(findAllForMap = false): Promise&lt;string[]&gt; {
        const pattern = '**/*.{js,jsx,ts,tsx,mjs,cjs}';
        const defaultIgnores = ['node_modules/**', 'dist/**', '.*/**', '*.d.ts'];
        const ig = ignore().add(defaultIgnores); // Start with default ignores
&nbsp;
        const gitignorePath = path.join(this.projectRoot, '.gitignore');
        try {
            const gitignoreContent = await fs.readFile(gitignorePath, 'utf-8');
<span class="cstat-no" title="statement not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >            ig.add(gitignoreContent);</span></span>
<span class="cstat-no" title="statement not covered" >            console.log(`Loaded .gitignore rules from ${gitignorePath}`);</span>
        } catch {
            // console.log("No .gitignore found or readable, using default ignores.");
        }
&nbsp;
        try {
            const files = await glob(pattern, {
                cwd: this.projectRoot,
                nodir: true,
                ignore: findAllForMap ? defaultIgnores : ig,
                dot: false,
            });
            // Normalize paths to use forward slashes for consistency
            return files.map(f =&gt; f.replace(/\\/g, '/'));
        } <span class="branch-0 cbranch-no" title="branch not covered" >catch (error) {</span>
<span class="cstat-no" title="statement not covered" >            console.error('Error finding project files:', error);</span>
<span class="cstat-no" title="statement not covered" >            return [];</span>
<span class="cstat-no" title="statement not covered" >        }</span>
    }
&nbsp;
    private resolveImportPath(importerPath: string, importSource: string): string | null {
        if (!importSource.startsWith('.')) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            // TODO: Handle node_modules or aliased paths later</span>
<span class="cstat-no" title="statement not covered" >            return null; // For now, only resolve relative paths</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        const importerDir = path.dirname(importerPath);
        let targetPath = path.normalize(path.join(importerDir, importSource)); // Normalize ./ and ../
&nbsp;
        // Check direct match first (including potential extension in importSource)
        if (this.fileExtensionMap.has(targetPath)<span class="branch-0 cbranch-no" title="branch not covered" > &amp;&amp; this.fileExtensionMap.get(targetPath)?.length === 1)</span> <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            // If targetPath itself exists in the map (e.g., import './file.js')</span>
<span class="cstat-no" title="statement not covered" >             return this.fileExtensionMap.get(targetPath)![0];</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // Try resolving without extension (most common case)
        const possibleFiles = this.fileExtensionMap.get(targetPath);
        if (possibleFiles<span class="branch-0 cbranch-no" title="branch not covered" > &amp;&amp; possibleFiles.length &gt; 0)</span> <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            // Prioritize .ts/.tsx over .js/.jsx if both exist? Or just take the first found?</span>
<span class="cstat-no" title="statement not covered" >             // Simple approach: take the first one found by glob (which might not be deterministic)</span>
<span class="cstat-no" title="statement not covered" >             // More robust: Prefer specific extensions if multiple match the base path.</span>
<span class="cstat-no" title="statement not covered" >             const preferredExt = ['.ts', '.tsx', '.js', '.jsx', '.mjs', '.cjs'];</span>
<span class="cstat-no" title="statement not covered" >             for (const ext of preferredExt) {</span>
<span class="cstat-no" title="statement not covered" >                 const match = possibleFiles.find(f =&gt; f === targetPath + ext);</span>
<span class="cstat-no" title="statement not covered" >                 if(match) return match;</span>
<span class="cstat-no" title="statement not covered" >             }</span>
<span class="cstat-no" title="statement not covered" >             return possibleFiles[0]; // Fallback to first found</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // Try resolving as index file (e.g., import './directory')
        const indexTargetPath = path.join(targetPath, 'index');
        const possibleIndexFiles = this.fileExtensionMap.get(indexTargetPath);
        if (possibleIndexFiles<span class="branch-0 cbranch-no" title="branch not covered" > &amp;&amp; possibleIndexFiles.length &gt; 0)</span> <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >             const preferredExt = ['.ts', '.tsx', '.js', '.jsx', '.mjs', '.cjs'];</span>
<span class="cstat-no" title="statement not covered" >              for (const ext of preferredExt) {</span>
<span class="cstat-no" title="statement not covered" >                 const match = possibleIndexFiles.find(f =&gt; f === indexTargetPath + ext);</span>
<span class="cstat-no" title="statement not covered" >                 if(match) return match;</span>
<span class="cstat-no" title="statement not covered" >             }</span>
<span class="cstat-no" title="statement not covered" >            return possibleIndexFiles[0]; // Fallback</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // console.warn(`Could not resolve relative import "${importSource}" from "${importerPath}"`);
        return null; // Resolution failed
    }
&nbsp;
&nbsp;
    private buildRelations(fileResults: FileAnalysisResult[]): Graph {
        console.log("Building relations graph...");
        const graph = new Graph({ multi: false, allowSelfLoops: false, type: 'directed' });
&nbsp;
        const fileNodeMap: Map&lt;string, string&gt; = new Map(); // filePath -&gt; nodeId
&nbsp;
        // 1. Add nodes for each analyzed file
        fileResults.forEach(fileResult =&gt; {
            const nodeId = `file:${fileResult.filePath}`;
            if (!graph.hasNode(nodeId)) {
                graph.addNode(nodeId, {
                    type: 'file',
                    filePath: fileResult.filePath,
                    language: fileResult.language,
                    errorCount: fileResult.errors?.length<span class="branch-0 cbranch-no" title="branch not covered" > ?? 0,</span>
                    elementCount: fileResult.elements.length,
                    label: fileResult.filePath // Add label for visualization
                });
                fileNodeMap.set(fileResult.filePath, nodeId);
            }<span class="branch-0 cbranch-no" title="branch not covered" > else {</span>
<span class="cstat-no" title="statement not covered" >                 // Update attributes if node somehow added twice (shouldn't happen with map)</span>
<span class="cstat-no" title="statement not covered" >                 graph.updateNodeAttribute(nodeId, 'errorCount', count =&gt; (count ?? 0) + (fileResult.errors?.length ?? 0));</span>
<span class="cstat-no" title="statement not covered" >                 graph.setNodeAttribute(nodeId, 'elementCount', fileResult.elements.length);</span>
<span class="cstat-no" title="statement not covered" >            }</span>
        });
&nbsp;
        // 2. Add edges based on imports
        fileResults.forEach(fileResult =&gt; {
            const sourceNodeId = fileNodeMap.get(fileResult.filePath);
            if (!sourceNodeId) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                 console.warn(`Source file node not found in graph: ${fileResult.filePath}`);</span>
<span class="cstat-no" title="statement not covered" >                 return; // Skip if source file node doesn't exist</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            const importDetails = new Map&lt;string, { count: number, names: string[] }&gt;(); // targetNodeId -&gt; import info
&nbsp;
            fileResult.elements.forEach(element =&gt; {
                if (element.type === CodeElementType.Import) {
                    const importElement = element as ImportElement;
                    // Attempt to resolve the imported path relative to the current file
                    const targetPath = this.resolveImportPath(fileResult.filePath, importElement.source);
&nbsp;
                    if (targetPath) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                        const targetNodeId = fileNodeMap.get(targetPath);</span>
<span class="cstat-no" title="statement not covered" >                        if (targetNodeId &amp;&amp; sourceNodeId !== targetNodeId) {</span>
<span class="cstat-no" title="statement not covered" >                             // Aggregate import details for the edge</span>
<span class="cstat-no" title="statement not covered" >                             if (!importDetails.has(targetNodeId)) {</span>
<span class="cstat-no" title="statement not covered" >                                importDetails.set(targetNodeId, { count: 0, names: [] });</span>
<span class="cstat-no" title="statement not covered" >                            }</span>
<span class="cstat-no" title="statement not covered" >                            const details = importDetails.get(targetNodeId)!;</span>
<span class="cstat-no" title="statement not covered" >                            details.count++;</span>
<span class="cstat-no" title="statement not covered" >                            // Fix checks for importedNames (array of objects)</span>
<span class="cstat-no" title="statement not covered" >                            let namesToAdd = importElement.importedNames;</span>
<span class="cstat-no" title="statement not covered" >                            if (importElement.isDefault &amp;&amp; !namesToAdd.some(n =&gt; n.name === 'default')) {</span>
<span class="cstat-no" title="statement not covered" >                                namesToAdd.push({ name: 'default' });</span>
<span class="cstat-no" title="statement not covered" >                            }</span>
<span class="cstat-no" title="statement not covered" >                            if (importElement.isNamespace &amp;&amp; !namesToAdd.some(n =&gt; n.name === '*')) {</span>
<span class="cstat-no" title="statement not covered" >                                namesToAdd.push({ name: '*' });</span>
<span class="cstat-no" title="statement not covered" >                            }</span>
<span class="cstat-no" title="statement not covered" >                            details.names.push(...namesToAdd.map(n =&gt; n.name));</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                        } else if (!targetNodeId) {</span>
<span class="cstat-no" title="statement not covered" >                             // console.warn(`Resolved import "${importElement.source}" from "${fileResult.filePath}" to "${targetPath}", but target file was not analyzed or found in graph.`);</span>
<span class="cstat-no" title="statement not covered" >                        }</span>
                    } else if (!importElement.source.startsWith('.')) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                        // TODO: Handle external / node_module imports - maybe add nodes for packages?</span>
<span class="cstat-no" title="statement not covered" >                        // console.log(`Skipping non-relative import: ${importElement.source} in ${fileResult.filePath}`);</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
                }
            });
&nbsp;
            // Add aggregated edges to the graph
             importDetails.forEach((details, targetNodeId) =&gt; {
<span class="cstat-no" title="statement not covered" >                 const uniqueNames = Array.from(new Set(details.names.filter(n =&gt; n))); // Filter out empty names just in case</span>
<span class="cstat-no" title="statement not covered" >                 if (!graph.hasEdge(sourceNodeId, targetNodeId)) {</span>
<span class="cstat-no" title="statement not covered" >                     graph.addDirectedEdge(sourceNodeId, targetNodeId, {</span>
<span class="cstat-no" title="statement not covered" >                        type: 'imports',</span>
<span class="cstat-no" title="statement not covered" >                        count: details.count, // Number of import statements between these files</span>
<span class="cstat-no" title="statement not covered" >                        importedNames: uniqueNames, // List of unique things imported</span>
<span class="cstat-no" title="statement not covered" >                        label: `imports (${details.count})` // Add label for visualization</span>
<span class="cstat-no" title="statement not covered" >                    });</span>
<span class="cstat-no" title="statement not covered" >                 } else {</span>
<span class="cstat-no" title="statement not covered" >                     // If edge exists (shouldn't with multi:false), update attributes?</span>
<span class="cstat-no" title="statement not covered" >                     graph.updateDirectedEdgeAttribute(sourceNodeId, targetNodeId, 'count', c =&gt; (c || 0) + details.count);</span>
<span class="cstat-no" title="statement not covered" >                     graph.updateDirectedEdgeAttribute(sourceNodeId, targetNodeId, 'importedNames', names =&gt; Array.from(new Set([...(names || []), ...uniqueNames])));</span>
<span class="cstat-no" title="statement not covered" >                     graph.updateDirectedEdgeAttribute(sourceNodeId, targetNodeId, 'label', label =&gt; `imports (${graph.getDirectedEdgeAttribute(sourceNodeId, targetNodeId, 'count')})`);</span>
<span class="cstat-no" title="statement not covered" >                 }</span>
             });
        });
&nbsp;
        console.log(`Relations graph built: ${graph.order} nodes, ${graph.size} edges.`);
        return graph; // Return the graph instance
    }
&nbsp;
    private summarizeResults(fileResults: FileAnalysisResult[]): { [key: string]: any } {
        const summary: { [key: string]: any } = {
            totalFilesAnalyzed: fileResults.length,
            filesWithErrors: fileResults.filter(f =&gt; f.errors &amp;&amp; f.errors.length &gt; 0).length,
            elementsByType: {},
            totalElements: 0,
            languages: {}
        };
&nbsp;
        for (const fileResult of fileResults) {
             // Count languages
             if (fileResult.language) {
                 summary.languages[fileResult.language] = (summary.languages[fileResult.language] || 0) + 1;
             }
&nbsp;
            for (const element of fileResult.elements) {
                summary.totalElements++;
                const typeName = element.type<span class="branch-0 cbranch-no" title="branch not covered" > || 'Unknown';</span> // Use type directly
                summary.elementsByType[typeName] = (summary.elementsByType[typeName] || 0) + 1;
            }
        }
&nbsp;
        console.log("Analysis Summary:", summary);
        return summary;
    }
}</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-29T07:20:16.520Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    