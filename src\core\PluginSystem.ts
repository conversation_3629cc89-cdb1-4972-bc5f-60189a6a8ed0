/**
 * Core Plugin System Architecture
 * 
 * This system allows for extensible language and framework analysis
 * with independent plugin dependencies and filtering strategies.
 */

import { CodeElement, FileAnalysisResult, AnalysisOptions } from '../types/CodeElement.js';

/**
 * Plugin capability flags - what the plugin can do
 */
export interface PluginCapabilities {
  // Core analysis capabilities
  syntaxAnalysis: boolean;      // Can parse syntax with tree-sitter
  semanticAnalysis: boolean;    // Can understand meaning/types
  crossFileAnalysis: boolean;   // Can analyze relationships between files
  
  // Advanced capabilities
  typeInference: boolean;       // Can infer/resolve types
  dependencyTracking: boolean;  // Can track dependencies/imports
  callGraphGeneration: boolean; // Can build function call graphs
  
  // Framework-specific capabilities
  frameworkPatterns: boolean;   // Understands framework-specific patterns
  decoratorAnalysis: boolean;   // Can analyze decorators/annotations
  componentAnalysis: boolean;   // Can analyze UI components
  
  // Performance characteristics
  incrementalAnalysis: boolean; // Supports incremental updates
  largeCodebaseOptimized: boolean; // Optimized for large codebases
}

/**
 * Plugin metadata and configuration
 */
export interface PluginMetadata {
  name: string;
  version: string;
  description: string;
  author: string;
  
  // What this plugin handles
  languages: string[];          // ['javascript', 'typescript']
  frameworks: string[];         // ['react', 'nestjs', 'express']
  fileExtensions: string[];     // ['.js', '.jsx', '.ts', '.tsx']
  
  // Plugin characteristics
  capabilities: PluginCapabilities;
  priority: number;             // Higher priority plugins run first
  dependencies: string[];       // Other plugins this depends on
  
  // Resource requirements
  requiresTypeScript: boolean;  // Needs TypeScript compiler API
  requiresNodeModules: boolean; // Needs access to node_modules
  memoryIntensive: boolean;     // High memory usage warning
}

/**
 * Plugin analysis context - what the plugin receives
 */
export interface PluginAnalysisContext {
  filePath: string;
  fileContent: string;
  language: string;
  framework?: string;
  
  // Project context
  projectRoot: string;
  tsConfigPath?: string;        // For TypeScript plugins
  packageJsonPath?: string;     // For dependency analysis
  
  // Analysis options
  options: AnalysisOptions;
  
  // Cross-plugin data sharing
  sharedData: Map<string, any>; // Plugins can share data
  
  // Performance constraints
  maxAnalysisTime?: number;     // Time limit in ms
  memoryLimit?: number;         // Memory limit in MB
}

/**
 * Plugin analysis result
 */
export interface PluginAnalysisResult {
  elements: CodeElement[];
  errors: string[];
  warnings: string[];
  
  // Plugin-specific metadata
  metadata: { [key: string]: any };
  
  // Performance metrics
  analysisTime: number;         // Time taken in ms
  memoryUsed: number;          // Memory used in MB
  
  // Confidence scoring
  confidence: number;           // 0-1, how confident the plugin is
  
  // Cross-plugin data
  sharedData?: Map<string, any>; // Data to share with other plugins
}

/**
 * Base plugin interface - all plugins must implement this
 */
export interface AnalysisPlugin {
  metadata: PluginMetadata;
  
  // Lifecycle methods
  initialize(projectRoot: string, options?: any): Promise<void>;
  cleanup(): Promise<void>;
  
  // Core analysis method
  analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult>;
  
  // Optional: Can this plugin handle this file?
  canHandle(filePath: string, language: string, framework?: string): boolean;
  
  // Optional: Post-processing after all plugins run
  postProcess?(allResults: PluginAnalysisResult[], context: PluginAnalysisContext): Promise<PluginAnalysisResult>;
}

/**
 * Plugin filtering strategy - how to filter results for this plugin's domain
 */
export interface PluginFilteringStrategy {
  // Extract relevant context for this plugin's domain
  extractRelevantContext(
    focusFile: string,
    allResults: FileAnalysisResult[],
    options: FilteringOptions
  ): any;
  
  // Score relevance of elements for this domain
  scoreRelevance(
    element: CodeElement,
    focusFile: string,
    context: any
  ): number; // 0-1 relevance score
  
  // Generate domain-specific summary
  generateSummary(
    relevantElements: CodeElement[],
    context: any
  ): any;
}

/**
 * Filtering options that can vary by language/framework
 */
export interface FilteringOptions {
  maxTokens: number;
  includeTypes: boolean;
  includeDependencies: boolean;
  includeCallGraph: boolean;
  
  // Language-specific options
  languageSpecific: { [key: string]: any };
  
  // Framework-specific options
  frameworkSpecific: { [key: string]: any };
}

/**
 * Plugin registry and management
 */
export class PluginRegistry {
  private plugins: Map<string, AnalysisPlugin> = new Map();
  private filteringStrategies: Map<string, PluginFilteringStrategy> = new Map();
  
  /**
   * Register a plugin
   */
  registerPlugin(plugin: AnalysisPlugin): void {
    this.plugins.set(plugin.metadata.name, plugin);
  }
  
  /**
   * Register a filtering strategy for a plugin
   */
  registerFilteringStrategy(pluginName: string, strategy: PluginFilteringStrategy): void {
    this.filteringStrategies.set(pluginName, strategy);
  }
  
  /**
   * Get plugins that can handle a specific file
   */
  getPluginsForFile(filePath: string, language: string, framework?: string): AnalysisPlugin[] {
    return Array.from(this.plugins.values())
      .filter(plugin => plugin.canHandle(filePath, language, framework))
      .sort((a, b) => b.metadata.priority - a.metadata.priority); // Higher priority first
  }
  
  /**
   * Get all registered plugins
   */
  getAllPlugins(): AnalysisPlugin[] {
    return Array.from(this.plugins.values());
  }
  
  /**
   * Get filtering strategy for a plugin
   */
  getFilteringStrategy(pluginName: string): PluginFilteringStrategy | undefined {
    return this.filteringStrategies.get(pluginName);
  }
  
  /**
   * Initialize all plugins
   */
  async initializeAll(projectRoot: string): Promise<void> {
    const initPromises = Array.from(this.plugins.values()).map(plugin => 
      plugin.initialize(projectRoot).catch(error => {
        console.warn(`Failed to initialize plugin ${plugin.metadata.name}:`, error);
      })
    );
    
    await Promise.all(initPromises);
  }
  
  /**
   * Cleanup all plugins
   */
  async cleanupAll(): Promise<void> {
    const cleanupPromises = Array.from(this.plugins.values()).map(plugin => 
      plugin.cleanup().catch(error => {
        console.warn(`Failed to cleanup plugin ${plugin.metadata.name}:`, error);
      })
    );
    
    await Promise.all(cleanupPromises);
  }
}

/**
 * Plugin-aware analysis engine
 */
export class PluginAnalysisEngine {
  private registry: PluginRegistry;
  
  constructor(registry: PluginRegistry) {
    this.registry = registry;
  }
  
  /**
   * Analyze a file using all applicable plugins
   */
  async analyzeFile(context: PluginAnalysisContext): Promise<FileAnalysisResult> {
    const applicablePlugins = this.registry.getPluginsForFile(
      context.filePath, 
      context.language, 
      context.framework
    );
    
    const results: PluginAnalysisResult[] = [];
    const sharedData = new Map<string, any>();
    
    // Run plugins in priority order
    for (const plugin of applicablePlugins) {
      try {
        const pluginContext = { ...context, sharedData };
        const result = await plugin.analyze(pluginContext);
        
        // Merge shared data
        if (result.sharedData) {
          for (const [key, value] of result.sharedData) {
            sharedData.set(key, value);
          }
        }
        
        results.push(result);
      } catch (error) {
        console.error(`Plugin ${plugin.metadata.name} failed:`, error);
      }
    }
    
    // Post-processing phase
    for (const plugin of applicablePlugins) {
      if (plugin.postProcess) {
        try {
          const postProcessContext = { ...context, sharedData };
          await plugin.postProcess(results, postProcessContext);
        } catch (error) {
          console.error(`Plugin ${plugin.metadata.name} post-processing failed:`, error);
        }
      }
    }
    
    // Merge all results
    return this.mergeResults(context.filePath, results);
  }
  
  private mergeResults(filePath: string, results: PluginAnalysisResult[]): FileAnalysisResult {
    const allElements: CodeElement[] = [];
    const allErrors: string[] = [];
    
    for (const result of results) {
      allElements.push(...result.elements);
      allErrors.push(...result.errors);
    }
    
    return {
      filePath,
      elements: allElements,
      errors: allErrors.length > 0 ? allErrors : undefined,
      language: results[0]?.metadata?.language || 'unknown'
    };
  }
}
