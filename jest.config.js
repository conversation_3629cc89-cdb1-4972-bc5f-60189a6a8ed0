/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  moduleNameMapper: {
    // Handle module aliases if needed in the future
    // '^@/(.*)$': '<rootDir>/src/$1',

    // Fix for ES modules in Node testing with Jest
    // If you encounter issues importing ESM modules like graphology, you might need adjustments here
    // Or ensure your tsconfig and package.json ("type": "module") are set up correctly.
    // For now, assuming nodenext resolution works.
  },
  // Transform files with ts-jest
  transform: {
    '^.+\.tsx?$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.json',
        // ESM support for ts-jest: https://kulshekhar.github.io/ts-jest/docs/guides/esm-support/
        useESM: false, // Switched from true to false for compatibility
      },
    ],
  },
  // If using ES Modules, <PERSON><PERSON> needs this experimental flag
  // Pass this via NODE_OPTIONS or configure differently if needed:
  // "test": "NODE_OPTIONS=--experimental-vm-modules jest" in package.json
  // For simplicity, we'll assume it's passed via package.json script

  // Detect open handles
  detectOpenHandles: true,
  // Collect coverage information
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageProvider: 'v8', // or 'babel'
};
