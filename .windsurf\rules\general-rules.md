---
trigger: manual
---

- Use web search and contxt7 mcp, if you encounter hard bugs, or when you not sure what the best way to implement somehting.
- If i stopped some terminal command DO NOT stop and ask me for instructions - it coud stuck, or it is wrong command, or maybe just stopped by mistake, or anything else. Check terminal and proceed. Run next commansd in new terminal session. I will stop you myself if i need to.
- Iterate with implementing changes gradually - make changes, test, fix errors, test, fix errors, until it works, then proceed with next step.
- Use "npx kill-port" to clean ports.
- If you use jest for testing, output result to json.
- Dont ask for my help or intervention, just do as you see it, and make summaries regularly, so i can give feedback later.
- **TypeScript First:** Prioritize converting remaining JavaScript files (`.jsx`) to TypeScript (`.tsx`). All new components must be written in TypeScript.
- **Strong Typing:** Ensure all React components are properly typed (e.g., using `React.FC<PropsInterface>`) and define explicit interfaces for props and complex data structures.
- **Feature-Sliced Design (FSD):**
    - Adhere to FSD principles for all new features and when refactoring existing code.
    - Due to current tooling limitations with nested directory creation, new FSD-structured files (e.g., `src/pages/my-page/ui/MyPage.tsx`, `src/shared/ui/MyComponent/MyComponent.tsx`) may temporarily be placed in fallback locations (e.g., `src/pages/MyPage.tsx`, `src/shared/ui/MyComponent.tsx`).
    - Always note these temporary locations in `cleanup-list.md` and plan for their eventual move to the correct FSD slice and subfolder (e.g., `/ui/`, `/model/`, `/lib/`).
- **`cleanup-list.md` Maintenance:**
    - Diligently update `cleanup-list.md` with every significant refactoring action. This includes:
        - Marking files for conversion/deletion (`[ ]`).
        - Marking completed conversions/deletions (`[x]`).
        - Noting the creation of new files, especially FSD-related ones and their (temporary) locations.
        - Adding comments about future FSD structural changes or unresolved issues (like failed directory deletions).
- **Import Path Management:** After renaming or moving any file, especially components or modules, perform a thorough search (e.g., using `grep_search`) to find and update all import paths that reference the old location.
- **Router Configuration:** When page components are refactored (renamed, moved, or their source files change), ensure `src/app/routing/router.tsx` is updated with the correct import paths and component references in route definitions. Add `TODO` comments for elements that are not yet FSD-compliant.
- **Directory Cleanup:** After refactoring components out of a directory, if the directory becomes empty, attempt to delete it. If automated deletion fails (e.g., "resource busy" errors), note this in `cleanup-list.md` for manual follow-up.
- **Code Consistency:** Strive for consistency in naming conventions (files, components, variables, types), coding style, and overall project structure as per FSD and established project patterns.