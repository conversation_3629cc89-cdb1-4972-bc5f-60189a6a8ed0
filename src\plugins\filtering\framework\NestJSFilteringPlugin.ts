/**
 * NestJS Filtering Plugin
 * 
 * Separate plugin for NestJS-specific context extraction and filtering.
 * Focuses on dependency injection relationships, API structure, and module organization.
 */

import { 
  AnalysisPlugin, 
  PluginMetadata, 
  PluginCapabilities, 
  PluginAnalysisContext, 
  PluginAnalysisResult,
  PluginFilteringStrategy,
  FilteringOptions
} from '../../core/PluginSystem.js';
import { CodeElement, CodeElementType, FileAnalysisResult } from '../../types/CodeElement.js';
import { FilteringEngine, FilteringLevel } from '../../core/FilteringEngine.js';

/**
 * NestJS-specific context structure
 */
export interface NestJSContext {
  // Architecture overview
  architecture: {
    controllers: Array<{
      name: string;
      file: string;
      routes: Array<{ method: string; path: string; handler: string; }>;
      guards: string[];
      interceptors: string[];
    }>;
    
    services: Array<{
      name: string;
      file: string;
      injectedDependencies: string[];
      providedBy: string[]; // Which modules provide this service
      usedBy: string[];     // Which controllers/services use this
    }>;
    
    modules: Array<{
      name: string;
      file: string;
      imports: string[];
      providers: string[];
      controllers: string[];
      exports: string[];
    }>;
  };
  
  // Dependency relationships
  dependencyGraph: {
    nodes: Array<{ id: string; type: 'controller' | 'service' | 'module'; name: string; }>;
    edges: Array<{ from: string; to: string; type: 'injects' | 'provides' | 'imports'; }>;
  };
  
  // API structure
  apiStructure: {
    baseRoutes: string[];
    endpoints: Array<{
      method: string;
      path: string;
      controller: string;
      handler: string;
      guards: string[];
      validation: boolean;
      documentation: boolean;
    }>;
    totalEndpoints: number;
    authenticationRequired: boolean;
  };
  
  // Code quality insights
  qualityInsights: {
    dependencyComplexity: 'low' | 'medium' | 'high';
    moduleOrganization: 'good' | 'needs-improvement' | 'poor';
    testCoverage: 'unknown' | 'low' | 'medium' | 'high';
    potentialIssues: string[];
    recommendations: string[];
  };
}

export class NestJSFilteringPlugin implements AnalysisPlugin, PluginFilteringStrategy {
  metadata: PluginMetadata = {
    name: 'nestjs-filtering',
    version: '1.0.0',
    description: 'NestJS-specific context extraction and filtering',
    author: 'Cyzer Team',
    
    languages: ['typescript'],
    frameworks: ['nestjs'],
    fileExtensions: ['.ts'],
    
    capabilities: {
      syntaxAnalysis: false,
      semanticAnalysis: false,
      crossFileAnalysis: true,      // ✅ Analyzes relationships across files
      typeInference: false,
      dependencyTracking: true,     // ✅ Tracks DI relationships
      callGraphGeneration: true,    // ✅ Builds service dependency graph
      frameworkPatterns: true,      // ✅ Understands NestJS patterns
      decoratorAnalysis: false,     // Uses results from decorator plugin
      componentAnalysis: true,      // ✅ Analyzes controllers, services, modules
      incrementalAnalysis: false,
      largeCodebaseOptimized: true
    },
    
    priority: 50, // Lower priority - filtering plugin
    dependencies: ['nestjs-framework'], // Depends on NestJS analysis plugin
    
    requiresTypeScript: false,
    requiresNodeModules: false,
    memoryIntensive: false
  };
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    // Filtering plugins typically don't need initialization
  }
  
  async cleanup(): Promise<void> {
    // No cleanup needed
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    // This is a filtering plugin - it processes results, not files
    return false;
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    // Filtering plugins don't analyze files directly
    return {
      elements: [],
      errors: [],
      warnings: [],
      metadata: { type: 'filtering-plugin' },
      analysisTime: 0,
      memoryUsed: 0,
      confidence: 1.0
    };
  }
  
  // PluginFilteringStrategy implementation
  
  /**
   * Extract NestJS-specific relevant context
   */
  extractRelevantContext(
    focusFile: string,
    allResults: FileAnalysisResult[],
    options: FilteringOptions
  ): NestJSContext {
    
    const focusFileResult = allResults.find(f => f.filePath === focusFile);
    if (!focusFileResult) {
      throw new Error(`Focus file ${focusFile} not found in analysis results`);
    }
    
    // Build NestJS context
    const architecture = this.buildArchitectureOverview(allResults);
    const dependencyGraph = this.buildDependencyGraph(allResults);
    const apiStructure = this.buildAPIStructure(allResults);
    const qualityInsights = this.analyzeQuality(allResults, focusFile);
    
    return {
      architecture,
      dependencyGraph,
      apiStructure,
      qualityInsights
    };
  }
  
  /**
   * Score relevance for NestJS context
   */
  scoreRelevance(
    element: CodeElement,
    focusFile: string,
    context: any
  ): number {
    let score = 0;
    
    // High relevance for NestJS-specific elements
    if (element.type === CodeElementType.NestJSController) score += 0.9;
    if (element.type === CodeElementType.NestJSService) score += 0.8;
    if (element.type === CodeElementType.NestJSModule) score += 0.7;
    if (element.type === CodeElementType.NestJSEndpoint) score += 0.8;
    if (element.type === CodeElementType.NestJSInjection) score += 0.6;
    
    // Higher relevance for elements with decorators
    if (element.metadata?.hasDecorators) score += 0.3;
    
    // Higher relevance for exported elements (likely part of public API)
    if (element.isExported) score += 0.2;
    
    // Boost relevance based on dependency relationships
    if (context.dependencyGraph) {
      const isInDependencyChain = this.isInDependencyChain(element, focusFile, context.dependencyGraph);
      if (isInDependencyChain) score += 0.4;
    }
    
    // Boost relevance for same module/directory
    if (this.isSameModule(element.filePath, focusFile)) score += 0.3;
    
    return Math.min(score, 1.0);
  }
  
  /**
   * Generate NestJS-specific summary
   */
  generateSummary(relevantElements: CodeElement[], context: any): any {
    const nestjsContext = context as NestJSContext;
    
    return {
      type: 'nestjs-summary',
      framework: 'NestJS',
      
      // Architecture summary
      architecture: {
        controllers: nestjsContext.architecture.controllers.length,
        services: nestjsContext.architecture.services.length,
        modules: nestjsContext.architecture.modules.length,
        totalFiles: relevantElements.length
      },
      
      // API summary
      api: {
        totalEndpoints: nestjsContext.apiStructure.totalEndpoints,
        baseRoutes: nestjsContext.apiStructure.baseRoutes.length,
        hasAuthentication: nestjsContext.apiStructure.authenticationRequired,
        hasValidation: nestjsContext.apiStructure.endpoints.some(e => e.validation)
      },
      
      // Dependency summary
      dependencies: {
        totalNodes: nestjsContext.dependencyGraph.nodes.length,
        totalConnections: nestjsContext.dependencyGraph.edges.length,
        complexity: nestjsContext.qualityInsights.dependencyComplexity
      },
      
      // Quality summary
      quality: {
        organization: nestjsContext.qualityInsights.moduleOrganization,
        issuesFound: nestjsContext.qualityInsights.potentialIssues.length,
        recommendations: nestjsContext.qualityInsights.recommendations.length
      }
    };
  }
  
  /**
   * Build architecture overview from analysis results
   */
  private buildArchitectureOverview(allResults: FileAnalysisResult[]) {
    const controllers: any[] = [];
    const services: any[] = [];
    const modules: any[] = [];
    
    for (const fileResult of allResults) {
      // Find controllers
      const controllerElements = fileResult.elements.filter(e => 
        e.type === CodeElementType.NestJSController ||
        e.metadata?.decorators?.some((d: any) => d.decoratorName === 'Controller')
      );
      
      for (const controller of controllerElements) {
        const routes = this.extractRoutes(fileResult.elements);
        const guards = this.extractGuards(fileResult.elements);
        const interceptors = this.extractInterceptors(fileResult.elements);
        
        controllers.push({
          name: controller.name,
          file: fileResult.filePath,
          routes,
          guards,
          interceptors
        });
      }
      
      // Find services
      const serviceElements = fileResult.elements.filter(e => 
        e.type === CodeElementType.NestJSService ||
        e.metadata?.decorators?.some((d: any) => d.decoratorName === 'Injectable')
      );
      
      for (const service of serviceElements) {
        const injectedDependencies = this.extractInjectedDependencies(fileResult.elements);
        
        services.push({
          name: service.name,
          file: fileResult.filePath,
          injectedDependencies,
          providedBy: [], // Would need cross-file analysis
          usedBy: []      // Would need cross-file analysis
        });
      }
      
      // Find modules
      const moduleElements = fileResult.elements.filter(e => 
        e.type === CodeElementType.NestJSModule ||
        e.metadata?.decorators?.some((d: any) => d.decoratorName === 'Module')
      );
      
      for (const module of moduleElements) {
        const moduleMetadata = this.extractModuleMetadata(fileResult.elements);
        
        modules.push({
          name: module.name,
          file: fileResult.filePath,
          ...moduleMetadata
        });
      }
    }
    
    return { controllers, services, modules };
  }
  
  /**
   * Build dependency graph
   */
  private buildDependencyGraph(allResults: FileAnalysisResult[]) {
    const nodes: any[] = [];
    const edges: any[] = [];
    
    // This would build a comprehensive dependency graph
    // For now, return basic structure
    
    return { nodes, edges };
  }
  
  /**
   * Build API structure
   */
  private buildAPIStructure(allResults: FileAnalysisResult[]) {
    const endpoints: any[] = [];
    const baseRoutes: string[] = [];
    
    for (const fileResult of allResults) {
      const controllerRoutes = this.extractRoutes(fileResult.elements);
      endpoints.push(...controllerRoutes);
      
      // Extract base routes from @Controller decorators
      const controllerDecorators = fileResult.elements.filter(e => 
        e.metadata?.decorators?.some((d: any) => d.decoratorName === 'Controller')
      );
      
      for (const controller of controllerDecorators) {
        const basePath = this.extractControllerBasePath(controller);
        if (basePath && !baseRoutes.includes(basePath)) {
          baseRoutes.push(basePath);
        }
      }
    }
    
    return {
      baseRoutes,
      endpoints,
      totalEndpoints: endpoints.length,
      authenticationRequired: endpoints.some(e => e.guards.length > 0)
    };
  }
  
  /**
   * Analyze code quality
   */
  private analyzeQuality(allResults: FileAnalysisResult[], focusFile: string) {
    const potentialIssues: string[] = [];
    const recommendations: string[] = [];
    
    // Analyze dependency complexity
    const totalServices = allResults.reduce((count, file) => 
      count + file.elements.filter(e => e.type === CodeElementType.NestJSService).length, 0
    );
    
    const dependencyComplexity = totalServices > 20 ? 'high' : totalServices > 10 ? 'medium' : 'low';
    
    // Check for common issues
    const hasControllers = allResults.some(file => 
      file.elements.some(e => e.type === CodeElementType.NestJSController)
    );
    const hasServices = allResults.some(file => 
      file.elements.some(e => e.type === CodeElementType.NestJSService)
    );
    
    if (hasControllers && !hasServices) {
      potentialIssues.push('Controllers found without services - consider extracting business logic');
      recommendations.push('Create service classes for business logic separation');
    }
    
    return {
      dependencyComplexity,
      moduleOrganization: 'good' as const, // Would need more sophisticated analysis
      testCoverage: 'unknown' as const,
      potentialIssues,
      recommendations
    };
  }
  
  // Helper methods (simplified implementations)
  private extractRoutes(elements: CodeElement[]): any[] {
    return elements
      .filter(e => e.type === CodeElementType.NestJSEndpoint)
      .map(e => ({
        method: e.metadata?.httpMethod || 'GET',
        path: e.metadata?.route || '/',
        handler: e.name,
        guards: [],
        validation: false,
        documentation: false
      }));
  }
  
  private extractGuards(elements: CodeElement[]): string[] {
    return elements
      .filter(e => e.metadata?.decorators?.some((d: any) => d.decoratorName === 'UseGuards'))
      .map(e => e.name);
  }
  
  private extractInterceptors(elements: CodeElement[]): string[] {
    return elements
      .filter(e => e.metadata?.decorators?.some((d: any) => d.decoratorName === 'UseInterceptors'))
      .map(e => e.name);
  }
  
  private extractInjectedDependencies(elements: CodeElement[]): string[] {
    return elements
      .filter(e => e.type === CodeElementType.NestJSInjection)
      .map(e => e.name);
  }
  
  private extractModuleMetadata(elements: CodeElement[]): any {
    return {
      imports: [],
      providers: [],
      controllers: [],
      exports: []
    };
  }
  
  private extractControllerBasePath(controller: CodeElement): string | null {
    // Extract base path from @Controller decorator
    return null; // Simplified
  }
  
  private isInDependencyChain(element: CodeElement, focusFile: string, dependencyGraph: any): boolean {
    // Check if element is in the dependency chain of the focus file
    return false; // Simplified
  }
  
  private isSameModule(elementFile: string, focusFile: string): boolean {
    // Check if files are in the same module/directory
    const elementDir = elementFile.split('/').slice(0, -1).join('/');
    const focusDir = focusFile.split('/').slice(0, -1).join('/');
    return elementDir === focusDir;
  }
}
