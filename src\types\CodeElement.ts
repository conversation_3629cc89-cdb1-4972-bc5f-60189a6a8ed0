/**
 * Type definitions for code analysis elements, inspired by Repomap and user requirements.
 */

/**
 * Types of code elements that can be identified.
 */
export enum CodeElementType {
  Function = 'function',
  Method = 'method',
  Class = 'class',
  Interface = 'interface',
  Type = 'type', // Type alias
  Enum = 'enum',
  Module = 'module', // e.g., TS namespace
  Variable = 'variable', // const, let, var
  Property = 'property', // Class property/field
  Parameter = 'parameter', // Function/method parameter
  Import = 'import',   // Import statement info
  Export = 'export',   // Export statement info
  Call = 'call',     // Function or method call site (for call graph)
  Reference = 'reference', // Generic identifier reference (for future use)
  Unknown = 'unknown'
}

/**
 * Represents visibility modifiers (TypeScript specific).
 */
export type Visibility = 'public' | 'private' | 'protected' | undefined;

/**
 * Represents the location of a code element within a file.
 */
export interface CodeLocation {
  startLine: number;
  startCol: number; // Added for potential finer granularity
  endLine: number;
  endCol: number;   // Added for potential finer granularity
  startPos: number; // Character offset from start of file
  endPos: number;   // Character offset from start of file
}

/**
 * Base interface for any identified code element.
 */
export interface CodeElement {
  type: CodeElementType;
  name: string;
  location: CodeLocation;
  filePath: string; // Relative path from project root
  fullText?: string; // Optional: Complete text of the element
  // Add other common properties like isExported?
  isExported?: boolean;
  visibility?: Visibility;
}

/**
 * Function or Method specific details.
 */
export interface FunctionElement extends CodeElement {
  type: CodeElementType.Function | CodeElementType.Method;
  parameters: { name: string; type?: string; isOptional?: boolean; isRest?: boolean }[]; // Structured parameters
  returnType?: string;
  isAsync: boolean;
  isGenerator: boolean;
  isArrow?: boolean;
  visibility?: Visibility; // For methods within classes
}

/**
 * Class Property/Field specific details.
 */
export interface PropertyElement extends CodeElement {
    type: CodeElementType.Property;
    propertyType?: string; // Data type of the property
    visibility?: Visibility;
    isStatic?: boolean;
    isReadOnly?: boolean;
}

/**
 * Class specific details.
 */
export interface ClassElement extends CodeElement {
  type: CodeElementType.Class;
  methods: FunctionElement[]; // Extracted methods
  properties: PropertyElement[]; // Extracted properties/fields
  extendsClass?: string; // Name of the parent class
  implementsInterfaces?: string[]; // Names of implemented interfaces
  isAbstract?: boolean;
  constructorDef?: FunctionElement; // Optional: Extracted constructor details (use distinct name)
}

/**
 * Interface, Type Alias, or Enum specific details.
 */
export interface TypeElement extends CodeElement {
  type: CodeElementType.Interface | CodeElementType.Type | CodeElementType.Enum;
  // More detailed members could be added later if needed for interfaces/enums
  members?: { name: string; type?: string }[]; // Simple list for now for interfaces/enums
}

/**
 * Import specific details.
 */
export interface ImportElement extends CodeElement {
    type: CodeElementType.Import;
    source: string; // The module path being imported (e.g., './utils', 'react')
    importedNames: { name: string; alias?: string }[]; // Names imported (e.g., { name: 'useState', alias: 'useState' } or { name: 'Component' alias: 'MyComp'})
    isDefault?: boolean; // If true, one of importedNames represents the default import (check name/alias)
    isNamespace?: boolean; // If true, one of importedNames represents the namespace import (check name/alias)
    resolvedPath?: string | null; // Absolute or relative path resolved by CodeAnalyzer, null if unresolved/external
}

/**
 * Export specific details.
 */
export interface ExportElement extends CodeElement {
    type: CodeElementType.Export;
    exportedName: string; // The name being exported ('default' for default exports)
    source?: string; // For re-exports (export { name } from './mod')
    isDefault?: boolean; // Is it a default export statement?
    isNamespace?: boolean; // Is it a namespace re-export (export * as name from ...)?
}

// Add other element types as needed (VariableElement, etc.)

/**
 * Options for analysis/extraction.
 */
export interface AnalysisOptions {
  includeFileContent?: boolean; // Whether to include full file text
  includeElementContent?: boolean; // Whether to include full text for each element
  targetElementTypes?: CodeElementType[]; // Filter by specific types
  maxDepth?: number; // For hierarchical analysis
  indexForSearch?: boolean; // Whether to generate embeddings and index content for semantic search
}

/**
 * Result of code analysis for a single file.
 */
export interface FileAnalysisResult {
  filePath: string; // Relative path from project root
  elements: CodeElement[]; // Identified elements in the file
  errors?: string[]; // Errors encountered during parsing or analysis of this file
  language?: string; // Detected language (e.g., 'typescript', 'javascript')
  fileContent?: string; // Full content of the file if options.includeFileContent is true
}

/**
 * Result of overall project analysis.
 */
export interface ProjectAnalysisResult {
  projectRoot: string; // The root directory analyzed
  files: FileAnalysisResult[]; // Analysis results for each file
  relations?: any; // File-level import graph (or potentially more complex graph)
  summary?: { [key: string]: any }; // Summary statistics (counts by type, etc.)
  errors?: string[]; // Global errors encountered during analysis
}

/**
 * Represents a code chunk that has been processed and embedded.
 */
export interface EmbeddedCodeChunk extends CodeElement {
  embedding: number[]; // The vector embedding of the code chunk's content
  embeddingModel: string; // Identifier for the model used to generate the embedding
  // Retains all properties from CodeElement (name, location, filePath, fullText, etc.)
}

/**
 * Represents a single search result when querying the vector index.
 */
export interface SearchResult {
  chunk: EmbeddedCodeChunk; // The code chunk that matched the query
  similarity: number; // The similarity score (e.g., cosine similarity) between the query and the chunk
}

/**
 * Optional filters for refining search results.
 */
export interface MetadataFilters {
  filePath?: string; // Filter by specific file path
  elementType?: CodeElementType; // Filter by specific code element type
  minSimilarity?: number; // Minimum similarity score
  // Add other potential metadata fields to filter on, e.g., language, specific tags
}