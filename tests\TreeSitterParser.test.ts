import { TreeSitterParser } from '../src/parsers/TreeSitterParser.js';
import { CodeElementType } from '../src/types/CodeElement.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

describe('TreeSitterParser', () => {
    let parser: TreeSitterParser;

    beforeAll(async () => {
        parser = new TreeSitterParser();
        await parser.initQueries(path.resolve(__dirname, '../queries'));
    });

    describe('Language Detection', () => {
        it('should detect JavaScript files', () => {
            expect(parser.getLanguage('test.js')).toBeDefined();
            expect(parser.getLanguage('test.jsx')).toBeDefined();
            expect(parser.getLanguage('test.mjs')).toBeDefined();
            expect(parser.getLanguage('test.cjs')).toBeDefined();
        });

        it('should detect TypeScript files', () => {
            expect(parser.getLanguage('test.ts')).toBeDefined();
            expect(parser.getLanguage('test.tsx')).toBeDefined();
            expect(parser.getLanguage('test.mts')).toBeDefined();
            expect(parser.getLanguage('test.cts')).toBeDefined();
        });

        it('should return undefined for unsupported files', () => {
            expect(parser.getLanguage('test.py')).toBeUndefined();
            expect(parser.getLanguage('test.java')).toBeUndefined();
            expect(parser.getLanguage('test.txt')).toBeUndefined();
        });
    });

    describe('JavaScript Parsing', () => {
        it('should parse function declarations', () => {
            const code = `
                function testFunction(param1, param2) {
                    return param1 + param2;
                }
                
                export function exportedFunction() {
                    console.log('exported');
                }
            `;
            
            const result = parser.parse(code, 'test.js');
            
            expect(result.filePath).toBe('test.js');
            expect(result.language).toBe('javascript');
            expect(result.elements).toHaveLength(2);
            
            const testFunc = result.elements.find(e => e.name === 'testFunction');
            expect(testFunc).toBeDefined();
            expect(testFunc?.type).toBe(CodeElementType.Function);
            expect(testFunc?.isExported).toBeFalsy();
            
            const exportedFunc = result.elements.find(e => e.name === 'exportedFunction');
            expect(exportedFunc).toBeDefined();
            expect(exportedFunc?.type).toBe(CodeElementType.Function);
            expect(exportedFunc?.isExported).toBeTruthy();
        });

        it('should parse class declarations', () => {
            const code = `
                class TestClass {
                    constructor(name) {
                        this.name = name;
                    }
                    
                    getName() {
                        return this.name;
                    }
                }
                
                export class ExportedClass extends TestClass {
                    setName(newName) {
                        this.name = newName;
                    }
                }
            `;
            
            const result = parser.parse(code, 'test.js');
            
            const classes = result.elements.filter(e => e.type === CodeElementType.Class);
            expect(classes).toHaveLength(2);
            
            const testClass = classes.find(e => e.name === 'TestClass');
            expect(testClass).toBeDefined();
            expect(testClass?.isExported).toBeFalsy();
            
            const exportedClass = classes.find(e => e.name === 'ExportedClass');
            expect(exportedClass).toBeDefined();
            expect(exportedClass?.isExported).toBeTruthy();
        });

        it('should parse arrow functions and function expressions', () => {
            const code = `
                const arrowFunc = (x, y) => x + y;
                
                const funcExpression = function(a, b) {
                    return a * b;
                };
                
                export const exportedArrow = () => 'hello';
            `;
            
            const result = parser.parse(code, 'test.js');
            
            const functions = result.elements.filter(e => e.type === CodeElementType.Function);
            expect(functions.length).toBeGreaterThanOrEqual(2);
            
            const arrowFunc = functions.find(e => e.name === 'arrowFunc');
            expect(arrowFunc).toBeDefined();
            
            const exportedArrow = functions.find(e => e.name === 'exportedArrow');
            expect(exportedArrow).toBeDefined();
            expect(exportedArrow?.isExported).toBeTruthy();
        });

        it('should parse import statements', () => {
            const code = `
                import React from 'react';
                import { useState, useEffect } from 'react';
                import * as utils from './utils';
                import defaultExport, { namedExport } from './module';
            `;
            
            const result = parser.parse(code, 'test.js');
            
            const imports = result.elements.filter(e => e.type === CodeElementType.Import);
            expect(imports.length).toBeGreaterThan(0);
        });
    });

    describe('TypeScript Parsing', () => {
        it('should parse TypeScript-specific constructs', () => {
            const code = `
                interface TestInterface {
                    name: string;
                    age: number;
                }
                
                type TestType = string | number;
                
                enum TestEnum {
                    VALUE1,
                    VALUE2,
                    VALUE3
                }
                
                export class TypedClass implements TestInterface {
                    constructor(public name: string, public age: number) {}
                    
                    getName(): string {
                        return this.name;
                    }
                }
            `;
            
            const result = parser.parse(code, 'test.ts');
            
            expect(result.language).toBe('typescript');
            
            const interface_ = result.elements.find(e => e.type === CodeElementType.Interface);
            expect(interface_).toBeDefined();
            expect(interface_?.name).toBe('TestInterface');
            
            const type = result.elements.find(e => e.type === CodeElementType.Type);
            expect(type).toBeDefined();
            expect(type?.name).toBe('TestType');
            
            const enum_ = result.elements.find(e => e.type === CodeElementType.Enum);
            expect(enum_).toBeDefined();
            expect(enum_?.name).toBe('TestEnum');
            
            const class_ = result.elements.find(e => e.type === CodeElementType.Class);
            expect(class_).toBeDefined();
            expect(class_?.name).toBe('TypedClass');
            expect(class_?.isExported).toBeTruthy();
        });

        it('should parse generic functions and async functions', () => {
            const code = `
                async function asyncFunction<T>(param: T): Promise<T> {
                    return param;
                }
                
                export const genericArrow = <T>(items: T[]): T[] => {
                    return items.filter(Boolean);
                };
                
                function* generatorFunction(): Generator<number> {
                    yield 1;
                    yield 2;
                }
            `;
            
            const result = parser.parse(code, 'test.ts');
            
            const functions = result.elements.filter(e => e.type === CodeElementType.Function);
            expect(functions.length).toBeGreaterThanOrEqual(2);
            
            const asyncFunc = functions.find(e => e.name === 'asyncFunction');
            expect(asyncFunc).toBeDefined();
            
            const genericArrow = functions.find(e => e.name === 'genericArrow');
            expect(genericArrow).toBeDefined();
            expect(genericArrow?.isExported).toBeTruthy();
        });
    });

    describe('Error Handling', () => {
        it('should handle malformed code gracefully', () => {
            const malformedCode = `
                function incomplete(
                class MissingBrace {
                    method() {
                        return "unclosed
                }
            `;
            
            const result = parser.parse(malformedCode, 'malformed.js');
            
            expect(result.filePath).toBe('malformed.js');
            expect(result.elements).toBeDefined();
            // Should not throw an error, even with malformed code
        });

        it('should handle empty files', () => {
            const result = parser.parse('', 'empty.js');
            
            expect(result.filePath).toBe('empty.js');
            expect(result.elements).toEqual([]);
            expect(result.language).toBe('javascript');
        });

        it('should handle unsupported file extensions', () => {
            const result = parser.parse('print("hello")', 'test.py');
            
            expect(result.filePath).toBe('test.py');
            expect(result.elements).toEqual([]);
            expect(result.errors).toContain('Unsupported language extension: .py');
        });
    });

    describe('Location Information', () => {
        it('should provide accurate location information', () => {
            const code = `function testFunc() {
    return 'test';
}`;
            
            const result = parser.parse(code, 'test.js');
            const func = result.elements.find(e => e.name === 'testFunc');
            
            expect(func?.location).toBeDefined();
            expect(func?.location.startLine).toBe(1);
            expect(func?.location.endLine).toBeGreaterThan(1);
            expect(func?.location.startPos).toBeDefined();
            expect(func?.location.endPos).toBeDefined();
        });
    });
});
