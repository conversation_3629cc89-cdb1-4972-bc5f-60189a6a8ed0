# 🔌 Correct Plugin Structure: Self-Contained Packages

## 📁 **Plugin-Centric Organization**

```
src/plugins/
├── javascript-plugin/              # Self-contained JavaScript plugin
│   ├── index.ts                    # Main plugin export
│   ├── JavaScriptPlugin.ts         # Plugin implementation
│   ├── queries/                    # All JS-related queries
│   │   ├── javascript-base.scm     # Core JS patterns
│   │   ├── es6-modules.scm         # ES6 import/export
│   │   └── async-patterns.scm      # Promises, async/await
│   ├── analyzers/                  # JS-specific analyzers
│   │   ├── ModuleAnalyzer.ts       # Import/export analysis
│   │   └── FunctionAnalyzer.ts     # Function pattern analysis
│   ├── types/                      # JS-specific types
│   │   └── JavaScriptTypes.ts
│   ├── filtering/                  # JS-specific filtering
│   │   └── JavaScriptFiltering.ts
│   └── package.json                # Future: @cyzer/plugin-javascript
│
├── typescript-plugin/              # Self-contained TypeScript plugin
│   ├── index.ts                    # Main plugin export
│   ├── TypeScriptPlugin.ts         # Plugin implementation
│   ├── queries/                    # All TS-related queries
│   │   ├── typescript-base.scm     # Core TS patterns
│   │   ├── interfaces.scm          # Interface definitions
│   │   ├── generics.scm            # Generic types
│   │   └── decorators.scm          # ✅ TS decorators included here
│   ├── analyzers/                  # TS-specific analyzers
│   │   ├── TypeAnalyzer.ts         # Type analysis with Compiler API
│   │   ├── InterfaceAnalyzer.ts    # Interface analysis
│   │   └── DecoratorAnalyzer.ts    # ✅ Decorator analysis included
│   ├── compiler/                   # TypeScript Compiler API integration
│   │   ├── CompilerService.ts      # TS Compiler API wrapper
│   │   └── TypeChecker.ts          # Type checking utilities
│   ├── types/                      # TS-specific types
│   │   ├── TypeScriptTypes.ts
│   │   └── DecoratorTypes.ts       # ✅ Decorator types included
│   ├── filtering/                  # TS-specific filtering
│   │   └── TypeScriptFiltering.ts
│   └── package.json                # Future: @cyzer/plugin-typescript
│
├── nestjs-plugin/                  # Self-contained NestJS plugin
│   ├── index.ts                    # Main plugin export
│   ├── NestJSPlugin.ts             # Plugin implementation
│   ├── queries/                    # All NestJS-related queries
│   │   ├── nestjs-decorators.scm   # NestJS-specific decorators
│   │   ├── dependency-injection.scm # DI patterns
│   │   └── modules.scm             # Module patterns
│   ├── analyzers/                  # NestJS-specific analyzers
│   │   ├── ControllerAnalyzer.ts   # Controller analysis
│   │   ├── ServiceAnalyzer.ts      # Service analysis
│   │   ├── ModuleAnalyzer.ts       # Module analysis
│   │   └── DIAnalyzer.ts           # Dependency injection analysis
│   ├── types/                      # NestJS-specific types
│   │   ├── NestJSTypes.ts
│   │   ├── ControllerTypes.ts
│   │   └── DITypes.ts
│   ├── filtering/                  # NestJS-specific filtering
│   │   └── NestJSFiltering.ts
│   └── package.json                # Future: @cyzer/plugin-nestjs
│
├── react-plugin/                   # Self-contained React plugin
│   ├── index.ts                    # Main plugin export
│   ├── ReactPlugin.ts              # Plugin implementation
│   ├── queries/                    # All React-related queries
│   │   ├── react-components.scm    # Component patterns
│   │   ├── react-hooks.scm         # Hook patterns
│   │   └── jsx-patterns.scm        # JSX analysis
│   ├── analyzers/                  # React-specific analyzers
│   │   ├── ComponentAnalyzer.ts    # Component analysis
│   │   ├── HookAnalyzer.ts         # Hook analysis
│   │   └── JSXAnalyzer.ts          # JSX analysis
│   ├── types/                      # React-specific types
│   │   ├── ReactTypes.ts
│   │   ├── ComponentTypes.ts
│   │   └── HookTypes.ts
│   ├── filtering/                  # React-specific filtering
│   │   └── ReactFiltering.ts
│   └── package.json                # Future: @cyzer/plugin-react
│
└── testing-plugin/                 # Self-contained Testing plugin
    ├── index.ts                    # Main plugin export
    ├── TestingPlugin.ts            # Plugin implementation
    ├── queries/                    # All testing-related queries
    │   ├── jest-patterns.scm       # Jest test patterns
    │   ├── mocha-patterns.scm      # Mocha test patterns
    │   └── vitest-patterns.scm     # Vitest test patterns
    ├── analyzers/                  # Testing-specific analyzers
    │   ├── TestAnalyzer.ts         # Test structure analysis
    │   ├── MockAnalyzer.ts         # Mock analysis
    │   └── CoverageAnalyzer.ts     # Coverage analysis
    ├── types/                      # Testing-specific types
    │   └── TestingTypes.ts
    ├── filtering/                  # Testing-specific filtering
    │   └── TestingFiltering.ts
    └── package.json                # Future: @cyzer/plugin-testing
```

## 🤔 **Decorator Plugin Decision: Include in TypeScript Plugin**

### **✅ Recommendation: Include decorators in TypeScript plugin**

**Reasoning:**

1. **Decorators are TypeScript-specific** - While the syntax exists in JS (experimental), decorators are primarily a TypeScript feature
2. **Tight coupling with type system** - Decorator analysis benefits from TypeScript Compiler API for type information
3. **Simpler dependency management** - No need for separate decorator plugin dependency
4. **Most decorator usage is framework-specific** - NestJS, Angular decorators are better analyzed in their respective plugins

### **🔧 TypeScript Plugin Structure (with decorators):**

```typescript
// typescript-plugin/TypeScriptPlugin.ts
export class TypeScriptPlugin implements AnalysisPlugin {
  private compilerService: CompilerService;
  private decoratorAnalyzer: DecoratorAnalyzer;  // ✅ Included in TS plugin
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    // 1. Basic TypeScript analysis
    const tsElements = await this.analyzeTypeScript(context);
    
    // 2. Decorator analysis (using TS Compiler API for type info)
    const decoratorElements = await this.decoratorAnalyzer.analyze(context, this.compilerService);
    
    // 3. Merge results
    return this.mergeResults(tsElements, decoratorElements);
  }
}
```

### **🎯 Framework Plugins Use TypeScript Plugin:**

```typescript
// nestjs-plugin/NestJSPlugin.ts
export class NestJSPlugin implements AnalysisPlugin {
  private typeScriptPlugin: TypeScriptPlugin;  // ✅ Gets decorators from TS plugin
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    // 1. Get TypeScript analysis (includes generic decorators)
    const tsResult = await this.typeScriptPlugin.analyze(context);
    
    // 2. Enhance with NestJS-specific decorator interpretation
    const nestjsEnhancements = await this.enhanceNestJSDecorators(tsResult);
    
    return this.mergeResults(tsResult, nestjsEnhancements);
  }
  
  private async enhanceNestJSDecorators(tsResult: PluginAnalysisResult) {
    // Interpret generic decorators as NestJS-specific elements
    // @Injectable -> NestJSService
    // @Controller -> NestJSController
    // @Get -> NestJSEndpoint
  }
}
```

## 📦 **Future NPM Package Structure**

### **Individual Packages:**
```bash
# Core language support
npm install @cyzer/plugin-javascript
npm install @cyzer/plugin-typescript

# Framework support  
npm install @cyzer/plugin-nestjs
npm install @cyzer/plugin-react
npm install @cyzer/plugin-angular

# Feature support
npm install @cyzer/plugin-testing
npm install @cyzer/plugin-security

# All-in-one
npm install @cyzer/cli  # Includes all plugins
```

### **Plugin Dependencies:**
```json
// @cyzer/plugin-nestjs/package.json
{
  "name": "@cyzer/plugin-nestjs",
  "dependencies": {
    "@cyzer/core": "^1.0.0",
    "@cyzer/plugin-typescript": "^1.0.0"  // ✅ Depends on TS plugin
  },
  "peerDependencies": {
    "typescript": "^5.0.0",
    "@nestjs/common": "^10.0.0"
  }
}
```

## 🎯 **Plugin Loading Strategy:**

```typescript
// Plugin registry loads complete packages
export class PluginRegistry {
  async loadPlugin(pluginName: string): Promise<AnalysisPlugin> {
    // Dynamic import of complete plugin package
    const pluginModule = await import(`@cyzer/plugin-${pluginName}`);
    return new pluginModule.default();
  }
  
  async loadPluginFromPath(pluginPath: string): Promise<AnalysisPlugin> {
    // For development - load from local path
    const pluginModule = await import(pluginPath);
    return new pluginModule.default();
  }
}
```

## 💡 **Benefits of This Structure:**

### **✅ Self-Contained Packages:**
- Each plugin has everything it needs
- Easy to publish as separate npm packages
- Clear ownership and maintenance

### **✅ Logical Grouping:**
- All TypeScript-related code in typescript-plugin
- All NestJS-related code in nestjs-plugin
- Easy to find and modify related functionality

### **✅ Dependency Management:**
- Clear plugin dependencies (NestJS depends on TypeScript)
- Optional dependencies for different use cases
- No circular dependencies

### **✅ Development Workflow:**
- Work on one plugin at a time
- Independent testing and deployment
- Easy to contribute to specific plugins

## 🚀 **Implementation Priority:**

1. **Week 1**: Restructure existing code into plugin-centric folders
2. **Week 2**: Implement TypeScript plugin with decorators included
3. **Week 3**: Implement NestJS plugin that uses TypeScript plugin
4. **Week 4**: Add React plugin and testing plugin
5. **Week 5**: Prepare for npm package separation

This structure is much better for the future npm package ecosystem! 🎯
