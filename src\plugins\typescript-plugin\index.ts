/**
 * TypeScript Plugin - Main Export
 * 
 * Self-contained TypeScript analysis plugin with decorators included.
 * Future: @cyzer/plugin-typescript
 */

export { TypeScriptPlugin } from './TypeScriptPlugin.js';
export { TypeScriptFilteringStrategy } from './filtering/TypeScriptFiltering.js';
export * from './types/TypeScriptTypes.js';
export * from './types/DecoratorTypes.js';

// Plugin metadata for registry
export const PLUGIN_METADATA = {
  name: 'typescript-plugin',
  version: '1.0.0',
  description: 'TypeScript analysis with decorators, types, and Compiler API integration',
  languages: ['typescript'],
  frameworks: [],
  dependencies: ['javascript-plugin'], // Builds on JavaScript
  capabilities: {
    syntaxAnalysis: true,
    semanticAnalysis: true,
    typeInference: true,
    decoratorAnalysis: true,
    crossFileAnalysis: true
  }
};

// Default export for easy importing
export default TypeScriptPlugin;
