import {
    CodeElement,
    CodeElementType,
    FileAnalysisResult,
    NestJSDecoratorElement,
    ReactComponentElement,
    ReactHookElement
} from '../types/CodeElement.js';

export interface LLMContext {
    focusFile: string;
    summary: {
        totalFiles: number;
        totalElements: number;
        languages: string[];
    };
    dependencies: {
        imports: Array<{ from: string; what: string[]; }>;
        exports: Array<{ what: string; type: string; }>;
    };
    relatedFiles: Array<{
        file: string;
        relationship: 'imports_from' | 'exports_to' | 'calls_functions' | 'extends_class';
        elements: string[];
    }>;
    codeStructure: {
        classes: Array<{ name: string; methods: string[]; extends?: string; }>;
        functions: Array<{ name: string; parameters: string[]; isAsync: boolean; }>;
        interfaces: Array<{ name: string; }>;
        types: Array<{ name: string; }>;
    };
    callGraph: Array<{ caller: string; callee: string; file: string; }>;
}

export class ContextExtractionService {
    
    /**
     * Extract focused context for LLM - only relevant information for a specific file
     */
    extractFocusedContext(
        focusFile: string, 
        allResults: FileAnalysisResult[], 
        maxTokens: number = 4000
    ): LLMContext {
        const focusFileResult = allResults.find(f => f.filePath === focusFile);
        if (!focusFileResult) {
            throw new Error(`Focus file ${focusFile} not found in analysis results`);
        }

        const context: LLMContext = {
            focusFile,
            summary: this.generateSummary(allResults),
            dependencies: this.extractDependencies(focusFileResult),
            relatedFiles: this.findRelatedFiles(focusFile, allResults),
            codeStructure: this.extractCodeStructure(focusFileResult),
            callGraph: this.buildCallGraph(focusFile, allResults)
        };

        return this.optimizeForTokenLimit(context, maxTokens);
    }

    /**
     * Extract NestJS-specific context - controllers, services, modules, DI
     */
    extractNestJSContext(
        focusFile: string,
        allResults: FileAnalysisResult[]
    ): {
        controllers: Array<{ name: string; routes: string[]; file: string; }>;
        services: Array<{ name: string; injections: string[]; file: string; }>;
        modules: Array<{ name: string; imports: string[]; providers: string[]; file: string; }>;
        dependencyGraph: Array<{ from: string; to: string; type: 'injection' | 'import'; }>;
        apiEndpoints: Array<{ method: string; path: string; controller: string; handler: string; }>;
    } {
        const nestjsElements = allResults.flatMap(file =>
            file.elements.filter(el =>
                el.type.toString().startsWith('nestjs_')
            )
        );

        const controllers = nestjsElements
            .filter(el => el.type === CodeElementType.NestJSController)
            .map(el => ({
                name: el.name,
                routes: [], // Extract from endpoints
                file: el.filePath
            }));

        const services = nestjsElements
            .filter(el => el.type === CodeElementType.NestJSService)
            .map(el => ({
                name: el.name,
                injections: [], // Extract from constructor/property injections
                file: el.filePath
            }));

        return {
            controllers,
            services,
            modules: [],
            dependencyGraph: [],
            apiEndpoints: []
        };
    }

    /**
     * Extract React-specific context - components, hooks, state flow
     */
    extractReactContext(
        focusFile: string,
        allResults: FileAnalysisResult[]
    ): {
        components: Array<{ name: string; hooks: string[]; props: string[]; file: string; }>;
        hooks: Array<{ name: string; type: string; dependencies: string[]; file: string; }>;
        stateFlow: Array<{ component: string; state: string; setter: string; }>;
        componentHierarchy: Array<{ parent: string; child: string; }>;
        contextUsage: Array<{ context: string; provider: string; consumers: string[]; }>;
    } {
        const reactElements = allResults.flatMap(file =>
            file.elements.filter(el =>
                el.type.toString().startsWith('react_')
            )
        );

        const components = reactElements
            .filter(el => el.type === CodeElementType.ReactComponent)
            .map(el => {
                const comp = el as ReactComponentElement;
                return {
                    name: comp.name,
                    hooks: comp.hooks || [],
                    props: comp.props?.map(p => p.name) || [],
                    file: comp.filePath
                };
            });

        const hooks = reactElements
            .filter(el => el.type === CodeElementType.ReactHook)
            .map(el => {
                const hook = el as ReactHookElement;
                return {
                    name: hook.name,
                    type: hook.hookType,
                    dependencies: hook.dependencies || [],
                    file: hook.filePath
                };
            });

        return {
            components,
            hooks,
            stateFlow: [],
            componentHierarchy: [],
            contextUsage: []
        };
    }

    /**
     * Extract debugging context - what calls what, where errors might propagate
     */
    extractDebuggingContext(
        errorLocation: { file: string; function?: string; line?: number },
        allResults: FileAnalysisResult[]
    ): {
        errorContext: any;
        callers: Array<{ file: string; function: string; line: number; }>;
        callees: Array<{ file: string; function: string; }>;
        dataFlow: Array<{ from: string; to: string; via: string; }>;
    } {
        // Implementation for debugging context
        return {
            errorContext: {},
            callers: [],
            callees: [],
            dataFlow: []
        };
    }

    /**
     * Extract development context - what's being worked on, what might be affected
     */
    extractDevelopmentContext(
        changedFiles: string[],
        allResults: FileAnalysisResult[]
    ): {
        impactedFiles: string[];
        testFiles: string[];
        dependentComponents: string[];
        suggestedReviews: string[];
    } {
        // Implementation for development context
        return {
            impactedFiles: [],
            testFiles: [],
            dependentComponents: [],
            suggestedReviews: []
        };
    }

    private generateSummary(allResults: FileAnalysisResult[]) {
        const languages = [...new Set(allResults.map(f => f.language).filter(Boolean))] as string[];
        return {
            totalFiles: allResults.length,
            totalElements: allResults.reduce((sum, f) => sum + f.elements.length, 0),
            languages
        };
    }

    private extractDependencies(fileResult: FileAnalysisResult) {
        const imports = fileResult.elements
            .filter(e => e.type === CodeElementType.Import)
            .map(e => ({
                from: (e as any).source || '',
                what: (e as any).importedNames?.map((n: any) => n.name) || []
            }));

        const exports = fileResult.elements
            .filter(e => e.isExported)
            .map(e => ({
                what: e.name,
                type: e.type
            }));

        return { imports, exports };
    }

    private findRelatedFiles(focusFile: string, allResults: FileAnalysisResult[]) {
        const related: LLMContext['relatedFiles'] = [];
        
        // Find files that import from focus file
        // Find files that focus file imports from
        // Find files with similar function calls
        
        return related;
    }

    private extractCodeStructure(fileResult: FileAnalysisResult) {
        const classes = fileResult.elements
            .filter(e => e.type === CodeElementType.Class)
            .map(e => ({
                name: e.name,
                methods: (e as any).methods?.map((m: any) => m.name) || [],
                extends: (e as any).extends
            }));

        const functions = fileResult.elements
            .filter(e => e.type === CodeElementType.Function)
            .map(e => ({
                name: e.name,
                parameters: (e as any).parameters?.map((p: any) => p.name) || [],
                isAsync: (e as any).isAsync || false
            }));

        const interfaces = fileResult.elements
            .filter(e => e.type === CodeElementType.Interface)
            .map(e => ({ name: e.name }));

        const types = fileResult.elements
            .filter(e => e.type === CodeElementType.Type)
            .map(e => ({ name: e.name }));

        return { classes, functions, interfaces, types };
    }

    private buildCallGraph(focusFile: string, allResults: FileAnalysisResult[]) {
        // Build function call relationships
        return [];
    }

    private optimizeForTokenLimit(context: LLMContext, maxTokens: number): LLMContext {
        // Estimate tokens and trim if necessary
        // Priority: dependencies > code structure > call graph > related files
        return context;
    }
}
