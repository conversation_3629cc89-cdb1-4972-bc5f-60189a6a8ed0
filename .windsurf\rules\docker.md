---
trigger: manual
---

When debugging docker build issues:
- Put comprehensive, abundant logs, so we can see how build and image unfolds.
- Output build logs to file, so we can easily check them. You can use something like "docker-compose up --build --remove-orphans -d 2>&1 | tee ../../logs/docker-compose-full-build-diag-$(date +%Y%m%d-%H%M%S).log" or similar commands.
- Save time - build only what is neecessary, as bulds take a lot of time. But dont hesitate to make whole builds if necessary.
- Save time - if we debugging some specific step, put command that throw error and stop build, if we dont need to know more. and