import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Configure environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, '.env') });

// Configuration
const CONFIG = {
    model: 'embedding-001',
    debug: process.env.DEBUG_MODE === 'true' || false,
    chunkSize: 1000,  // Max tokens per chunk
    overlapSize: 100,  // Token overlap between chunks
    testFiles: [
        'test-gemini-embeddings.js',  // Our existing test file
        'src/core/CodeAnalyzer.ts'    // A TypeScript file from your project
    ]
};

// Logging utility
class Logger {
    static log(message, data = null) {
        console.log(`[LOG] ${message}`);
        if (data && CONFIG.debug) {
            console.log(JSON.stringify(data, null, 2));
        }
    }

    static error(message, error = null) {
        console.error(`[ERROR] ${message}`);
        if (error) {
            console.error(error);
        }
    }
}

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: CONFIG.model });

// Store code chunks with their embeddings
let codeChunks = [];

/**
 * Split code into meaningful chunks
 */
async function chunkCode(code, filePath) {
    // Simple splitting by function/class declarations for demo
    // In production, consider using AST parsing for better accuracy
    const chunks = [];
    
    // Split by functions/classes
    const functionRegex = /(?:function|class|const\s+\w+\s*=|let\s+\w+\s*=|var\s+\w+\s*=)(?:[^{]+)?\{[\s\S]*?\}/g;
    let match;
    let chunkId = 0;
    
    while ((match = functionRegex.exec(code)) !== null) {
        const chunk = match[0];
        const startLine = code.substring(0, match.index).split('\n').length;
        const endLine = startLine + chunk.split('\n').length - 1;
        
        chunks.push({
            id: `${path.basename(filePath)}-${chunkId++}`,
            file: filePath,
            content: chunk.trim(),
            startLine,
            endLine,
            type: chunk.startsWith('class') ? 'class' : 'function'
        });
    }
    
    // If no functions found, split by lines
    if (chunks.length === 0) {
        const lines = code.split('\n');
        for (let i = 0; i < lines.length; i += CONFIG.chunkSize) {
            const chunk = lines.slice(i, i + CONFIG.chunkSize).join('\n');
            chunks.push({
                id: `${path.basename(filePath)}-chunk-${i}`,
                file: filePath,
                content: chunk,
                startLine: i,
                endLine: Math.min(i + CONFIG.chunkSize - 1,
                type: 'code_block'
            });
        }
    }
    
    return chunks;
}

/**
 * Generate embeddings for code chunks
 */
async function generateEmbeddings(chunks) {
    Logger.log(`Generating embeddings for ${chunks.length} chunks...`);
    
    for (const chunk of chunks) {
        try {
            const result = await model.embedContent(chunk.content);
            chunk.embedding = result.embedding.values;
            chunk.embeddingSize = chunk.embedding.length;
            codeChunks.push(chunk);
            Logger.log(`  ✓ Embedded: ${chunk.id} (${chunk.type})`);
        } catch (error) {
            Logger.error(`Error generating embedding for ${chunk.id}`, error);
        }
    }
}

/**
 * Find similar code chunks using cosine similarity
 */
function findSimilarChunks(queryEmbedding, topK = 3) {
    const similarities = [];
    
    for (const chunk of codeChunks) {
        try {
            const similarity = cosineSimilarity(queryEmbedding, chunk.embedding);
            similarities.push({
                ...chunk,
                similarity
            });
        } catch (error) {
            Logger.error(`Error calculating similarity for ${chunk.id}`, error);
        }
    }
    
    return similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, topK);
}

/**
 * Cosine similarity between two vectors
 */
function cosineSimilarity(vecA, vecB) {
    if (!vecA || !vecB || vecA.length !== vecB.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        normA += vecA[i] * vecA[i];
        normB += vecB[i] * vecB[i];
    }
    
    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);
    
    if (normA === 0 || normB === 0) return 0;
    return dotProduct / (normA * normB);
}

/**
 * Main function to test code embeddings
 */
async function testCodeEmbeddings() {
    try {
        // 1. Read and process test files
        Logger.log('Processing test files...');
        for (const filePath of CONFIG.testFiles) {
            const fullPath = path.resolve(__dirname, filePath);
            if (!fs.existsSync(fullPath)) {
                Logger.error(`File not found: ${fullPath}`);
                continue;
            }
            
            const code = fs.readFileSync(fullPath, 'utf-8');
            const chunks = await chunkCode(code, filePath);
            Logger.log(`  Found ${chunks.length} chunks in ${filePath}`);
            
            // Generate embeddings for each chunk
            await generateEmbeddings(chunks);
        }
        
        if (codeChunks.length === 0) {
            throw new Error('No code chunks found to process');
        }
        
        // 2. Test semantic search
        Logger.log('\n--- Testing Semantic Search ---');
        
        // Example queries
        const testQueries = [
            'function that calculates similarity',
            'class definition',
            'error handling code'
        ];
        
        for (const query of testQueries) {
            Logger.log(`\nSearching for: "${query}"`);
            
            // Get embedding for the query
            const result = await model.embedContent(query);
            const queryEmbedding = result.embedding.values;
            
            // Find similar code chunks
            const similarChunks = findSimilarChunks(queryEmbedding, 2);
            
            // Display results
            similarChunks.forEach((chunk, index) => {
                console.log(`\n${index + 1}. ${chunk.file} (${chunk.type}, similarity: ${chunk.similarity.toFixed(4)})`);
                console.log(`   Lines ${chunk.startLine}-${chunk.endLine}`);
                console.log('   ' + chunk.content.split('\n')[0] + '...');
            });
        }
        
        Logger.log('\n--- Test Complete ---');
        
    } catch (error) {
        Logger.error('Error in testCodeEmbeddings:', error);
    }
}

// Run the test
testCodeEmbeddings();
