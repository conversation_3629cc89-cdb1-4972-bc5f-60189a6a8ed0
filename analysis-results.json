{"projectPath": ".", "timestamp": "2025-06-14T21:30:39.230Z", "results": {"files": [{"filePath": "test-gemini-embeddings.js", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 60, "startPos": 0, "endPos": 59}, "filePath": "test-gemini-embeddings.js", "isExported": false, "importedNames": [], "source": "@google/generative-ai", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 29, "startPos": 60, "endPos": 88}, "filePath": "test-gemini-embeddings.js", "isExported": false, "importedNames": [], "source": "dotenv", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 25, "startPos": 89, "endPos": 113}, "filePath": "test-gemini-embeddings.js", "isExported": false, "importedNames": [], "source": "path", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 37, "startPos": 114, "endPos": 150}, "filePath": "test-gemini-embeddings.js", "isExported": false, "importedNames": [], "source": "url", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "__filename", "location": {"startLine": 7, "startCol": 1, "endLine": 7, "endCol": 51, "startPos": 202, "endPos": 252}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "class", "name": "<PERSON><PERSON>", "location": {"startLine": 24, "startCol": 1, "endLine": 47, "endCol": 2, "startPos": 735, "endPos": 1329}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [{"type": "method", "name": "log", "location": {"startLine": 25, "startCol": 5, "endLine": 30, "endCol": 6, "startPos": 754, "endPos": 937}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}, {"type": "method", "name": "error", "location": {"startLine": 32, "startCol": 5, "endLine": 37, "endCol": 6, "startPos": 943, "endPos": 1096}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}, {"type": "method", "name": "debug", "location": {"startLine": 39, "startCol": 5, "endLine": 46, "endCol": 6, "startPos": 1102, "endPos": 1327}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}], "properties": []}, {"type": "method", "name": "log", "location": {"startLine": 25, "startCol": 5, "endLine": 30, "endCol": 6, "startPos": 754, "endPos": 937}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}, {"type": "method", "name": "error", "location": {"startLine": 32, "startCol": 5, "endLine": 37, "endCol": 6, "startPos": 943, "endPos": 1096}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}, {"type": "method", "name": "debug", "location": {"startLine": 39, "startCol": 5, "endLine": 46, "endCol": 6, "startPos": 1102, "endPos": 1327}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}, {"type": "function", "name": "testGeminiEmbeddings", "location": {"startLine": 50, "startCol": 1, "endLine": 127, "endCol": 2, "startPos": 1348, "endPos": 4380}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "Error", "location": {"startLine": 50, "startCol": 1, "endLine": 127, "endCol": 2, "startPos": 1348, "endPos": 4380}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "genAI", "location": {"startLine": 63, "startCol": 9, "endLine": 63, "endCol": 54, "startPos": 1858, "endPos": 1903}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "sim", "location": {"startLine": 108, "startCol": 21, "endLine": 111, "endCol": 23, "startPos": 3692, "endPos": 3849}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "cosineSimilarity", "location": {"startLine": 130, "startCol": 1, "endLine": 150, "endCol": 2, "startPos": 4452, "endPos": 4992}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "vecA"}, {"name": "vecB"}]}, {"type": "function", "name": "Error", "location": {"startLine": 130, "startCol": 1, "endLine": 150, "endCol": 2, "startPos": 4452, "endPos": 4992}, "filePath": "test-gemini-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "javascript"}, {"filePath": "test-code-embeddings.js", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 60, "startPos": 0, "endPos": 59}, "filePath": "test-code-embeddings.js", "isExported": false, "importedNames": [], "source": "@google/generative-ai", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 29, "startPos": 60, "endPos": 88}, "filePath": "test-code-embeddings.js", "isExported": false, "importedNames": [], "source": "dotenv", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 25, "startPos": 89, "endPos": 113}, "filePath": "test-code-embeddings.js", "isExported": false, "importedNames": [], "source": "path", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 37, "startPos": 114, "endPos": 150}, "filePath": "test-code-embeddings.js", "isExported": false, "importedNames": [], "source": "url", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 5, "startCol": 1, "endLine": 5, "endCol": 21, "startPos": 151, "endPos": 171}, "filePath": "test-code-embeddings.js", "isExported": false, "importedNames": [], "source": "fs", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "__filename", "location": {"startLine": 8, "startCol": 1, "endLine": 8, "endCol": 51, "startPos": 208, "endPos": 258}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "class", "name": "<PERSON><PERSON>", "location": {"startLine": 25, "startCol": 1, "endLine": 39, "endCol": 2, "startPos": 764, "endPos": 1127}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [{"type": "method", "name": "log", "location": {"startLine": 26, "startCol": 5, "endLine": 31, "endCol": 6, "startPos": 783, "endPos": 966}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}, {"type": "method", "name": "error", "location": {"startLine": 33, "startCol": 5, "endLine": 38, "endCol": 6, "startPos": 972, "endPos": 1125}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}], "properties": []}, {"type": "method", "name": "log", "location": {"startLine": 26, "startCol": 5, "endLine": 31, "endCol": 6, "startPos": 783, "endPos": 966}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}, {"type": "method", "name": "error", "location": {"startLine": 33, "startCol": 5, "endLine": 38, "endCol": 6, "startPos": 972, "endPos": 1125}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "message"}]}, {"type": "variable", "name": "genAI", "location": {"startLine": 42, "startCol": 1, "endLine": 42, "endCol": 66, "startPos": 1153, "endPos": 1218}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "chunkCode", "location": {"startLine": 51, "startCol": 1, "endLine": 93, "endCol": 2, "startPos": 1395, "endPos": 2900}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "code"}, {"name": "filePath"}]}, {"type": "function", "name": "generateEmbeddings", "location": {"startLine": 98, "startCol": 1, "endLine": 112, "endCol": 2, "startPos": 2949, "endPos": 3526}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "chunks"}]}, {"type": "function", "name": "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 117, "startCol": 1, "endLine": 135, "endCol": 2, "startPos": 3588, "endPos": 4143}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "queryEmbe<PERSON>"}]}, {"type": "variable", "name": "similarity", "location": {"startLine": 122, "startCol": 13, "endLine": 122, "endCol": 82, "startPos": 3741, "endPos": 3810}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "cosineSimilarity", "location": {"startLine": 140, "startCol": 1, "endLine": 158, "endCol": 2, "startPos": 4194, "endPos": 4692}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "vecA"}, {"name": "vecB"}]}, {"type": "function", "name": "testCodeEmbeddings", "location": {"startLine": 163, "startCol": 1, "endLine": 219, "endCol": 2, "startPos": 4743, "endPos": 6846}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": []}, {"type": "variable", "name": "chunks", "location": {"startLine": 175, "startCol": 13, "endLine": 175, "endCol": 60, "startPos": 5227, "endPos": 5274}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "generateEmbeddings", "location": {"startLine": 163, "startCol": 1, "endLine": 219, "endCol": 2, "startPos": 4743, "endPos": 6846}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "Error", "location": {"startLine": 163, "startCol": 1, "endLine": 219, "endCol": 2, "startPos": 4743, "endPos": 6846}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "similarChunks", "location": {"startLine": 204, "startCol": 13, "endLine": 204, "endCol": 72, "startPos": 6233, "endPos": 6292}, "filePath": "test-code-embeddings.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "javascript"}, {"filePath": "jest.config.js", "elements": [], "errors": [], "language": "javascript"}, {"filePath": "tests/TreeSitterParser.test.ts", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 71, "startPos": 0, "endPos": 70}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "importedNames": [], "source": "../src/parsers/TreeSitterParser.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 63, "startPos": 71, "endPos": 133}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "importedNames": [], "source": "../src/types/CodeElement.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 25, "startPos": 134, "endPos": 158}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "importedNames": [], "source": "path", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 37, "startPos": 159, "endPos": 195}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "importedNames": [], "source": "url", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "__filename", "location": {"startLine": 6, "startCol": 1, "endLine": 6, "endCol": 51, "startPos": 197, "endPos": 247}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "dirname", "location": {"startLine": 7, "startCol": 1, "endLine": 7, "endCol": 44, "startPos": 248, "endPos": 291}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 52, "startCol": 13, "endLine": 52, "endCol": 58, "startPos": 2011, "endPos": 2056}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 64, "startCol": 17, "endLine": 64, "endCol": 100, "startPos": 2583, "endPos": 2666}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 95, "startCol": 13, "endLine": 95, "endCol": 58, "startPos": 3688, "endPos": 3733}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 102, "startCol": 13, "endLine": 102, "endCol": 91, "startPos": 3974, "endPos": 4052}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 123, "startCol": 13, "endLine": 123, "endCol": 58, "startPos": 4761, "endPos": 4806}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 130, "startCol": 13, "endLine": 130, "endCol": 96, "startPos": 5049, "endPos": 5132}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 148, "startCol": 13, "endLine": 148, "endCol": 58, "startPos": 5800, "endPos": 5845}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 155, "startCol": 13, "endLine": 155, "endCol": 92, "startPos": 6086, "endPos": 6165}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 189, "startCol": 13, "endLine": 189, "endCol": 58, "startPos": 7189, "endPos": 7234}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 198, "startCol": 13, "endLine": 198, "endCol": 98, "startPos": 7546, "endPos": 7631}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 199, "startCol": 13, "endLine": 199, "endCol": 88, "startPos": 7644, "endPos": 7719}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 200, "startCol": 13, "endLine": 200, "endCol": 88, "startPos": 7732, "endPos": 7807}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 201, "startCol": 13, "endLine": 201, "endCol": 91, "startPos": 7820, "endPos": 7898}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 226, "startCol": 13, "endLine": 226, "endCol": 58, "startPos": 8771, "endPos": 8816}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 233, "startCol": 13, "endLine": 233, "endCol": 96, "startPos": 9059, "endPos": 9142}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 254, "startCol": 13, "endLine": 254, "endCol": 72, "startPos": 9814, "endPos": 9873}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 262, "startCol": 13, "endLine": 262, "endCol": 57, "startPos": 10136, "endPos": 10180}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 270, "startCol": 13, "endLine": 270, "endCol": 70, "startPos": 10442, "endPos": 10499}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 284, "startCol": 13, "endLine": 284, "endCol": 58, "startPos": 10928, "endPos": 10973}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 291, "startCol": 13, "endLine": 291, "endCol": 96, "startPos": 11208, "endPos": 11291}, "filePath": "tests/TreeSitterParser.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "typescript"}, {"filePath": "tests/EmbeddingService.test.ts", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 72, "startPos": 0, "endPos": 71}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "importedNames": [], "source": "../src/services/EmbeddingService.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 76, "startPos": 72, "endPos": 147}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "importedNames": [], "source": "../src/types/CodeElement.js", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "spyOn", "location": {"startLine": 17, "startCol": 13, "endLine": 17, "endCol": 81, "startPos": 581, "endPos": 649}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "mockImplementation", "location": {"startLine": 17, "startCol": 13, "endLine": 17, "endCol": 81, "startPos": 581, "endPos": 649}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "generateEmbeddings", "location": {"startLine": 39, "startCol": 13, "endLine": 39, "endCol": 84, "startPos": 1525, "endPos": 1596}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "generateEmbeddings", "location": {"startLine": 55, "startCol": 13, "endLine": 55, "endCol": 84, "startPos": 2157, "endPos": 2228}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "generateEmbeddings", "location": {"startLine": 71, "startCol": 13, "endLine": 71, "endCol": 84, "startPos": 2804, "endPos": 2875}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "generateEmbeddings", "location": {"startLine": 91, "startCol": 13, "endLine": 91, "endCol": 84, "startPos": 3756, "endPos": 3827}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "embed<PERSON><PERSON><PERSON>", "location": {"startLine": 98, "startCol": 13, "endLine": 98, "endCol": 76, "startPos": 4028, "endPos": 4091}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "embed<PERSON><PERSON><PERSON>", "location": {"startLine": 103, "startCol": 13, "endLine": 103, "endCol": 66, "startPos": 4224, "endPos": 4277}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "embed<PERSON><PERSON><PERSON>", "location": {"startLine": 108, "startCol": 13, "endLine": 108, "endCol": 69, "startPos": 4420, "endPos": 4476}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "spyOn", "location": {"startLine": 113, "startCol": 13, "endLine": 113, "endCol": 82, "startPos": 4610, "endPos": 4679}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "mockImplementation", "location": {"startLine": 113, "startCol": 13, "endLine": 113, "endCol": 82, "startPos": 4610, "endPos": 4679}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "spyOn", "location": {"startLine": 122, "startCol": 13, "endLine": 122, "endCol": 81, "startPos": 5007, "endPos": 5075}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "mockImplementation", "location": {"startLine": 122, "startCol": 13, "endLine": 122, "endCol": 81, "startPos": 5007, "endPos": 5075}, "filePath": "tests/EmbeddingService.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "typescript"}, {"filePath": "tests/CodeAnalyzer.test.ts", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 60, "startPos": 0, "endPos": 59}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "importedNames": [], "source": "../src/core/CodeAnalyzer.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 63, "startPos": 60, "endPos": 122}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "importedNames": [], "source": "../src/types/CodeElement.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 25, "startPos": 123, "endPos": 147}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "importedNames": [], "source": "path", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 30, "startPos": 148, "endPos": 177}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "importedNames": [], "source": "fs/promises", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 5, "startCol": 1, "endLine": 5, "endCol": 37, "startPos": 178, "endPos": 214}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "importedNames": [], "source": "url", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "__filename", "location": {"startLine": 7, "startCol": 1, "endLine": 7, "endCol": 51, "startPos": 216, "endPos": 266}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "dirname", "location": {"startLine": 8, "startCol": 1, "endLine": 8, "endCol": 44, "startPos": 267, "endPos": 310}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "resolve", "location": {"startLine": 12, "startCol": 5, "endLine": 12, "endCol": 81, "startPos": 381, "endPos": 457}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 93, "startCol": 13, "endLine": 93, "endCol": 61, "startPos": 2447, "endPos": 2495}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "map", "location": {"startLine": 99, "startCol": 13, "endLine": 99, "endCol": 66, "startPos": 2692, "endPos": 2745}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 114, "startCol": 13, "endLine": 114, "endCol": 61, "startPos": 3524, "endPos": 3572}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "flatMap", "location": {"startLine": 115, "startCol": 13, "endLine": 115, "endCol": 72, "startPos": 3585, "endPos": 3644}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 117, "startCol": 13, "endLine": 117, "endCol": 92, "startPos": 3670, "endPos": 3749}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 121, "startCol": 13, "endLine": 121, "endCol": 69, "startPos": 3872, "endPos": 3928}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 125, "startCol": 13, "endLine": 125, "endCol": 73, "startPos": 4053, "endPos": 4113}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 129, "startCol": 13, "endLine": 129, "endCol": 77, "startPos": 4242, "endPos": 4306}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 135, "startCol": 13, "endLine": 135, "endCol": 61, "startPos": 4499, "endPos": 4547}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "flatMap", "location": {"startLine": 136, "startCol": 13, "endLine": 136, "endCol": 72, "startPos": 4560, "endPos": 4619}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 138, "startCol": 13, "endLine": 138, "endCol": 87, "startPos": 4645, "endPos": 4719}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 141, "startCol": 13, "endLine": 141, "endCol": 80, "startPos": 4807, "endPos": 4874}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 145, "startCol": 13, "endLine": 145, "endCol": 82, "startPos": 5013, "endPos": 5082}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 151, "startCol": 13, "endLine": 151, "endCol": 61, "startPos": 5294, "endPos": 5342}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "flatMap", "location": {"startLine": 152, "startCol": 13, "endLine": 152, "endCol": 72, "startPos": 5355, "endPos": 5414}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 154, "startCol": 13, "endLine": 154, "endCol": 94, "startPos": 5440, "endPos": 5521}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 157, "startCol": 13, "endLine": 157, "endCol": 75, "startPos": 5605, "endPos": 5667}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 161, "startCol": 13, "endLine": 161, "endCol": 84, "startPos": 5802, "endPos": 5873}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 162, "startCol": 13, "endLine": 162, "endCol": 69, "startPos": 5886, "endPos": 5942}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 165, "startCol": 13, "endLine": 165, "endCol": 84, "startPos": 6014, "endPos": 6085}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 166, "startCol": 13, "endLine": 166, "endCol": 65, "startPos": 6098, "endPos": 6150}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 171, "startCol": 13, "endLine": 171, "endCol": 61, "startPos": 6280, "endPos": 6328}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "flatMap", "location": {"startLine": 172, "startCol": 13, "endLine": 172, "endCol": 72, "startPos": 6341, "endPos": 6400}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 174, "startCol": 13, "endLine": 174, "endCol": 88, "startPos": 6426, "endPos": 6501}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 179, "startCol": 13, "endLine": 179, "endCol": 61, "startPos": 6645, "endPos": 6693}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 191, "startCol": 13, "endLine": 193, "endCol": 16, "startPos": 7174, "endPos": 7300}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "flatMap", "location": {"startLine": 195, "startCol": 13, "endLine": 195, "endCol": 72, "startPos": 7326, "endPos": 7385}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 196, "startCol": 13, "endLine": 196, "endCol": 102, "startPos": 7398, "endPos": 7487}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 201, "startCol": 13, "endLine": 203, "endCol": 16, "startPos": 7643, "endPos": 7750}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "flatMap", "location": {"startLine": 205, "startCol": 13, "endLine": 205, "endCol": 72, "startPos": 7764, "endPos": 7823}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 214, "startCol": 13, "endLine": 216, "endCol": 16, "startPos": 8272, "endPos": 8380}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "flatMap", "location": {"startLine": 218, "startCol": 13, "endLine": 218, "endCol": 72, "startPos": 8406, "endPos": 8465}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 219, "startCol": 13, "endLine": 219, "endCol": 77, "startPos": 8478, "endPos": 8542}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 226, "startCol": 13, "endLine": 226, "endCol": 61, "startPos": 8738, "endPos": 8786}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "nonExistentAnalyzer", "location": {"startLine": 235, "startCol": 13, "endLine": 235, "endCol": 80, "startPos": 9073, "endPos": 9140}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 239, "startCol": 13, "endLine": 239, "endCol": 72, "startPos": 9349, "endPos": 9408}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "resolve", "location": {"startLine": 245, "startCol": 13, "endLine": 245, "endCol": 91, "startPos": 9622, "endPos": 9700}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "emptyAnalyzer", "location": {"startLine": 248, "startCol": 13, "endLine": 248, "endCol": 70, "startPos": 9793, "endPos": 9850}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 251, "startCol": 13, "endLine": 251, "endCol": 66, "startPos": 9922, "endPos": 9975}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 272, "startCol": 13, "endLine": 272, "endCol": 61, "startPos": 10780, "endPos": 10828}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "map", "location": {"startLine": 273, "startCol": 13, "endLine": 273, "endCol": 66, "startPos": 10841, "endPos": 10894}, "filePath": "tests/CodeAnalyzer.test.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "typescript"}, {"filePath": "test-files/simple.js", "elements": [{"type": "function", "name": "helloWorld", "location": {"startLine": 1, "startCol": 8, "endLine": 3, "endCol": 2, "startPos": 7, "endPos": 66}, "filePath": "test-files/simple.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "helloWorld", "location": {"startLine": 1, "startCol": 8, "endLine": 3, "endCol": 2, "startPos": 7, "endPos": 66}, "filePath": "test-files/simple.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}], "errors": [], "language": "javascript"}, {"filePath": "test-files/advanced.ts", "elements": [{"type": "class", "name": "MyClass", "location": {"startLine": 1, "startCol": 16, "endLine": 5, "endCol": 2, "startPos": 15, "endPos": 120}, "filePath": "test-files/advanced.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [{"type": "method", "name": "staticMethod", "location": {"startLine": 2, "startCol": 5, "endLine": 2, "endCol": 29, "startPos": 35, "endPos": 59}, "filePath": "test-files/advanced.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "value", "location": {"startLine": 3, "startCol": 5, "endLine": 3, "endCol": 31, "startPos": 64, "endPos": 90}, "filePath": "test-files/advanced.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "value", "location": {"startLine": 4, "startCol": 5, "endLine": 4, "endCol": 28, "startPos": 95, "endPos": 118}, "filePath": "test-files/advanced.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "v", "type": "number"}]}], "properties": []}, {"type": "method", "name": "staticMethod", "location": {"startLine": 2, "startCol": 5, "endLine": 2, "endCol": 29, "startPos": 35, "endPos": 59}, "filePath": "test-files/advanced.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "value", "location": {"startLine": 3, "startCol": 5, "endLine": 3, "endCol": 31, "startPos": 64, "endPos": 90}, "filePath": "test-files/advanced.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "value", "location": {"startLine": 4, "startCol": 5, "endLine": 4, "endCol": 28, "startPos": 95, "endPos": 118}, "filePath": "test-files/advanced.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "v", "type": "number"}]}, {"type": "interface", "name": "MyInterface", "location": {"startLine": 6, "startCol": 8, "endLine": 6, "endCol": 45, "startPos": 128, "endPos": 165}, "filePath": "test-files/advanced.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "type", "name": "MyType", "location": {"startLine": 7, "startCol": 8, "endLine": 7, "endCol": 38, "startPos": 173, "endPos": 203}, "filePath": "test-files/advanced.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "enum", "name": "MyEnum", "location": {"startLine": 8, "startCol": 8, "endLine": 8, "endCol": 31, "startPos": 211, "endPos": 234}, "filePath": "test-files/advanced.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "arrow", "location": {"startLine": 9, "startCol": 8, "endLine": 9, "endCol": 31, "startPos": 242, "endPos": 265}, "filePath": "test-files/advanced.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "arrow", "location": {"startLine": 9, "startCol": 14, "endLine": 9, "endCol": 30, "startPos": 248, "endPos": 264}, "filePath": "test-files/advanced.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true}, {"type": "function", "name": "arrow", "location": {"startLine": 9, "startCol": 8, "endLine": 9, "endCol": 31, "startPos": 242, "endPos": 265}, "filePath": "test-files/advanced.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "generatorFunc", "location": {"startLine": 10, "startCol": 8, "endLine": 10, "endCol": 46, "startPos": 273, "endPos": 311}, "filePath": "test-files/advanced.ts", "isExported": true, "isAsync": false, "isGenerator": true, "isArrow": false, "parameters": []}, {"type": "function", "name": "generatorFunc", "location": {"startLine": 10, "startCol": 8, "endLine": 10, "endCol": 46, "startPos": 273, "endPos": 311}, "filePath": "test-files/advanced.ts", "isExported": true, "isAsync": false, "isGenerator": true, "isArrow": false, "parameters": []}], "errors": [], "language": "typescript"}, {"filePath": "test-files/advanced.js", "elements": [{"type": "function", "name": "exportedFunc", "location": {"startLine": 1, "startCol": 8, "endLine": 1, "endCol": 34, "startPos": 7, "endPos": 33}, "filePath": "test-files/advanced.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "exportedFunc", "location": {"startLine": 1, "startCol": 8, "endLine": 1, "endCol": 34, "startPos": 7, "endPos": 33}, "filePath": "test-files/advanced.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "localFunc", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 24, "startPos": 34, "endPos": 57}, "filePath": "test-files/advanced.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "arrowFunc", "location": {"startLine": 3, "startCol": 7, "endLine": 3, "endCol": 27, "startPos": 64, "endPos": 84}, "filePath": "test-files/advanced.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true}, {"type": "function", "name": "arrowFunc", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 28, "startPos": 58, "endPos": 85}, "filePath": "test-files/advanced.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "class", "name": "LocalClass", "location": {"startLine": 4, "startCol": 1, "endLine": 6, "endCol": 2, "startPos": 86, "endPos": 120}, "filePath": "test-files/advanced.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [{"type": "method", "name": "method", "location": {"startLine": 5, "startCol": 3, "endLine": 5, "endCol": 14, "startPos": 107, "endPos": 118}, "filePath": "test-files/advanced.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}], "properties": []}, {"type": "method", "name": "method", "location": {"startLine": 5, "startCol": 3, "endLine": 5, "endCol": 14, "startPos": 107, "endPos": 118}, "filePath": "test-files/advanced.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "class", "name": "ExportedClass", "location": {"startLine": 7, "startCol": 16, "endLine": 7, "endCol": 38, "startPos": 136, "endPos": 158}, "filePath": "test-files/advanced.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [], "properties": []}], "errors": [], "language": "javascript"}, {"filePath": "src/index.ts", "elements": [{"type": "import", "name": "import", "location": {"startLine": 7, "startCol": 1, "endLine": 7, "endCol": 27, "startPos": 149, "endPos": 175}, "filePath": "src/index.ts", "isExported": false, "importedNames": [], "source": "yargs", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 8, "startCol": 1, "endLine": 8, "endCol": 41, "startPos": 176, "endPos": 216}, "filePath": "src/index.ts", "isExported": false, "importedNames": [], "source": "yargs/helpers", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 9, "startCol": 1, "endLine": 9, "endCol": 55, "startPos": 217, "endPos": 271}, "filePath": "src/index.ts", "isExported": false, "importedNames": [], "source": "./core/CodeAnalyzer.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 10, "startCol": 1, "endLine": 10, "endCol": 106, "startPos": 272, "endPos": 377}, "filePath": "src/index.ts", "isExported": false, "importedNames": [], "source": "./types/CodeElement.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 11, "startCol": 1, "endLine": 11, "endCol": 25, "startPos": 378, "endPos": 402}, "filePath": "src/index.ts", "isExported": false, "importedNames": [], "source": "path", "isDefault": false, "isNamespace": false}, {"type": "function", "name": "getAnalyzer", "location": {"startLine": 18, "startCol": 1, "endLine": 30, "endCol": 2, "startPos": 657, "endPos": 1315}, "filePath": "src/index.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "projectPath", "type": "string"}, {"name": "forceReanalyze", "type": "boolean"}]}, {"type": "variable", "name": "resolve", "location": {"startLine": 19, "startCol": 5, "endLine": 19, "endCol": 59, "startPos": 767, "endPos": 821}, "filePath": "src/index.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "log", "location": {"startLine": 18, "startCol": 1, "endLine": 30, "endCol": 2, "startPos": 657, "endPos": 1315}, "filePath": "src/index.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "log", "location": {"startLine": 18, "startCol": 1, "endLine": 30, "endCol": 2, "startPos": 657, "endPos": 1315}, "filePath": "src/index.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzer", "location": {"startLine": 25, "startCol": 5, "endLine": 25, "endCol": 60, "startPos": 1125, "endPos": 1180}, "filePath": "src/index.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "initialize", "location": {"startLine": 18, "startCol": 1, "endLine": 30, "endCol": 2, "startPos": 657, "endPos": 1315}, "filePath": "src/index.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzer", "location": {"startLine": 63, "startCol": 13, "endLine": 63, "endCol": 72, "startPos": 2667, "endPos": 2726}, "filePath": "src/index.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "analyzeProject", "location": {"startLine": 72, "startCol": 17, "endLine": 72, "endCol": 72, "startPos": 3089, "endPos": 3144}, "filePath": "src/index.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "semanticSearch", "location": {"startLine": 160, "startCol": 17, "endLine": 160, "endCol": 117, "startPos": 7466, "endPos": 7566}, "filePath": "src/index.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "typescript"}, {"filePath": "test-files/mini-repo/index.js", "elements": [{"type": "import", "name": "import", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 49, "startPos": 12, "endPos": 60}, "filePath": "test-files/mini-repo/index.js", "isExported": false, "importedNames": [], "source": "./a.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 49, "startPos": 61, "endPos": 109}, "filePath": "test-files/mini-repo/index.js", "isExported": false, "importedNames": [], "source": "./b.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 35, "startPos": 110, "endPos": 144}, "filePath": "test-files/mini-repo/index.js", "isExported": false, "importedNames": [], "source": "./c.js", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "summary", "location": {"startLine": 6, "startCol": 8, "endLine": 6, "endCol": 85, "startPos": 153, "endPos": 230}, "filePath": "test-files/mini-repo/index.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "javascript"}, {"filePath": "test-files/mini-repo/c.js", "elements": [{"type": "function", "name": "cFunc", "location": {"startLine": 4, "startCol": 8, "endLine": 4, "endCol": 38, "startPos": 46, "endPos": 76}, "filePath": "test-files/mini-repo/c.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "cFunc", "location": {"startLine": 4, "startCol": 8, "endLine": 4, "endCol": 38, "startPos": 46, "endPos": 76}, "filePath": "test-files/mini-repo/c.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}], "errors": [], "language": "javascript"}, {"filePath": "test-files/mini-repo/b.js", "elements": [{"type": "variable", "name": "B", "location": {"startLine": 2, "startCol": 8, "endLine": 2, "endCol": 20, "startPos": 15, "endPos": 27}, "filePath": "test-files/mini-repo/b.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "bFunc", "location": {"startLine": 3, "startCol": 8, "endLine": 3, "endCol": 38, "startPos": 35, "endPos": 65}, "filePath": "test-files/mini-repo/b.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "bFunc", "location": {"startLine": 3, "startCol": 8, "endLine": 3, "endCol": 38, "startPos": 35, "endPos": 65}, "filePath": "test-files/mini-repo/b.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 35, "startPos": 66, "endPos": 100}, "filePath": "test-files/mini-repo/b.js", "isExported": false, "importedNames": [], "source": "./c.js", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "fromC", "location": {"startLine": 5, "startCol": 8, "endLine": 5, "endCol": 24, "startPos": 108, "endPos": 124}, "filePath": "test-files/mini-repo/b.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "callC", "location": {"startLine": 6, "startCol": 8, "endLine": 6, "endCol": 30, "startPos": 132, "endPos": 154}, "filePath": "test-files/mini-repo/b.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "callC", "location": {"startLine": 6, "startCol": 8, "endLine": 6, "endCol": 30, "startPos": 132, "endPos": 154}, "filePath": "test-files/mini-repo/b.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "javascript"}, {"filePath": "test-files/mini-repo/a.js", "elements": [{"type": "variable", "name": "A", "location": {"startLine": 2, "startCol": 8, "endLine": 2, "endCol": 20, "startPos": 15, "endPos": 27}, "filePath": "test-files/mini-repo/a.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "aFunc", "location": {"startLine": 3, "startCol": 8, "endLine": 3, "endCol": 38, "startPos": 35, "endPos": 65}, "filePath": "test-files/mini-repo/a.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "aFunc", "location": {"startLine": 3, "startCol": 8, "endLine": 3, "endCol": 38, "startPos": 35, "endPos": 65}, "filePath": "test-files/mini-repo/a.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 35, "startPos": 66, "endPos": 100}, "filePath": "test-files/mini-repo/a.js", "isExported": false, "importedNames": [], "source": "./b.js", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "fromB", "location": {"startLine": 5, "startCol": 8, "endLine": 5, "endCol": 24, "startPos": 108, "endPos": 124}, "filePath": "test-files/mini-repo/a.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "callB", "location": {"startLine": 6, "startCol": 8, "endLine": 6, "endCol": 30, "startPos": 132, "endPos": 154}, "filePath": "test-files/mini-repo/a.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "callB", "location": {"startLine": 6, "startCol": 8, "endLine": 6, "endCol": 30, "startPos": 132, "endPos": 154}, "filePath": "test-files/mini-repo/a.js", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "javascript"}, {"filePath": "src/types/CodeElement.ts", "elements": [{"type": "enum", "name": "CodeElementType", "location": {"startLine": 8, "startCol": 8, "endLine": 24, "endCol": 2, "startPos": 165, "endPos": 785}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "type", "name": "Visibility", "location": {"startLine": 29, "startCol": 8, "endLine": 29, "endCol": 73, "startPos": 860, "endPos": 925}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "CodeLocation", "location": {"startLine": 34, "startCol": 8, "endLine": 41, "endCol": 2, "startPos": 1002, "endPos": 1308}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "CodeElement", "location": {"startLine": 46, "startCol": 8, "endLine": 55, "endCol": 2, "startPos": 1376, "endPos": 1687}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "FunctionElement", "location": {"startLine": 60, "startCol": 8, "endLine": 68, "endCol": 2, "startPos": 1744, "endPos": 2111}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "PropertyElement", "location": {"startLine": 73, "startCol": 8, "endLine": 79, "endCol": 2, "startPos": 2170, "endPos": 2390}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "ClassElement", "location": {"startLine": 84, "startCol": 8, "endLine": 92, "endCol": 2, "startPos": 2434, "endPos": 2872}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "TypeElement", "location": {"startLine": 97, "startCol": 8, "endLine": 101, "endCol": 2, "startPos": 2941, "endPos": 3237}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "ImportElement", "location": {"startLine": 106, "startCol": 8, "endLine": 113, "endCol": 2, "startPos": 3282, "endPos": 3940}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "ExportElement", "location": {"startLine": 118, "startCol": 8, "endLine": 124, "endCol": 2, "startPos": 3985, "endPos": 4370}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "AnalysisOptions", "location": {"startLine": 131, "startCol": 8, "endLine": 137, "endCol": 2, "startPos": 4485, "endPos": 4887}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "FileAnalysisResult", "location": {"startLine": 142, "startCol": 8, "endLine": 148, "endCol": 2, "startPos": 4950, "endPos": 5349}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "ProjectAnalysisResult", "location": {"startLine": 153, "startCol": 8, "endLine": 159, "endCol": 2, "startPos": 5405, "endPos": 5788}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "EmbeddedCodeChunk", "location": {"startLine": 164, "startCol": 8, "endLine": 168, "endCol": 2, "startPos": 5870, "endPos": 6169}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "SearchResult", "location": {"startLine": 173, "startCol": 8, "endLine": 176, "endCol": 2, "startPos": 6255, "endPos": 6454}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "interface", "name": "MetadataFilters", "location": {"startLine": 181, "startCol": 8, "endLine": 186, "endCol": 2, "startPos": 6520, "endPos": 6814}, "filePath": "src/types/CodeElement.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "typescript"}, {"filePath": "src/services/VectorSearchService.ts", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 109, "startPos": 0, "endPos": 108}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "importedNames": [], "source": "../types/CodeElement.js", "isDefault": false, "isNamespace": false}, {"type": "class", "name": "VectorSearchService", "location": {"startLine": 3, "startCol": 8, "endLine": 117, "endCol": 2, "startPos": 117, "endPos": 4241}, "filePath": "src/services/VectorSearchService.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [{"type": "method", "name": "constructor", "location": {"startLine": 6, "startCol": 5, "endLine": 6, "endCol": 21, "startPos": 201, "endPos": 217}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "indexChunks", "location": {"startLine": 13, "startCol": 5, "endLine": 23, "endCol": 6, "startPos": 444, "endPos": 1061}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [{"name": "chunks", "type": "EmbeddedCodeChunk[]"}]}, {"type": "method", "name": "clearIndex", "location": {"startLine": 28, "startCol": 5, "endLine": 31, "endCol": 6, "startPos": 1140, "endPos": 1271}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "search", "location": {"startLine": 40, "startCol": 5, "endLine": 76, "endCol": 6, "startPos": 1654, "endPos": 3055}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [{"name": "queryVector", "type": "number[]"}, {"name": "topK", "type": "number"}, {"name": "filters", "type": "MetadataFilters"}]}, {"type": "method", "name": "cosineSimilarity", "location": {"startLine": 84, "startCol": 5, "endLine": 108, "endCol": 6, "startPos": 3281, "endPos": 4042}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "vecA", "type": "number[]"}, {"name": "vecB", "type": "number[]"}], "visibility": "private"}, {"type": "method", "name": "getIndexSize", "location": {"startLine": 114, "startCol": 5, "endLine": 116, "endCol": 6, "startPos": 4169, "endPos": 4239}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}], "properties": [{"type": "property", "name": "vectorIndex", "propertyType": "EmbeddedCodeChunk[]", "visibility": "private", "location": {"startLine": 3, "startCol": 12, "endLine": 3, "endCol": 23, "startPos": 157, "endPos": 168}, "filePath": "src/services/VectorSearchService.ts"}]}, {"type": "method", "name": "indexChunks", "location": {"startLine": 13, "startCol": 5, "endLine": 23, "endCol": 6, "startPos": 444, "endPos": 1061}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [{"name": "chunks", "type": "EmbeddedCodeChunk[]"}]}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 13, "startCol": 5, "endLine": 23, "endCol": 6, "startPos": 444, "endPos": 1061}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "findIndex", "location": {"startLine": 15, "startCol": 13, "endLine": 15, "endCol": 185, "startPos": 557, "endPos": 729}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 13, "startCol": 5, "endLine": 23, "endCol": 6, "startPos": 444, "endPos": 1061}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "clearIndex", "location": {"startLine": 28, "startCol": 5, "endLine": 31, "endCol": 6, "startPos": 1140, "endPos": 1271}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "search", "location": {"startLine": 40, "startCol": 5, "endLine": 76, "endCol": 6, "startPos": 1654, "endPos": 3055}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [{"name": "queryVector", "type": "number[]"}, {"name": "topK", "type": "number"}, {"name": "filters", "type": "MetadataFilters"}]}, {"type": "method", "name": "warn", "location": {"startLine": 40, "startCol": 5, "endLine": 76, "endCol": 6, "startPos": 1654, "endPos": 3055}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "cosineSimilarity", "location": {"startLine": 60, "startCol": 13, "endLine": 60, "endCol": 84, "startPos": 2539, "endPos": 2610}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 40, "startCol": 5, "endLine": 76, "endCol": 6, "startPos": 1654, "endPos": 3055}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "sort", "location": {"startLine": 40, "startCol": 5, "endLine": 76, "endCol": 6, "startPos": 1654, "endPos": 3055}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "slice", "location": {"startLine": 40, "startCol": 5, "endLine": 76, "endCol": 6, "startPos": 1654, "endPos": 3055}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "cosineSimilarity", "location": {"startLine": 84, "startCol": 5, "endLine": 108, "endCol": 6, "startPos": 3281, "endPos": 4042}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "vecA", "type": "number[]"}, {"name": "vecB", "type": "number[]"}]}, {"type": "method", "name": "sqrt", "location": {"startLine": 84, "startCol": 5, "endLine": 108, "endCol": 6, "startPos": 3281, "endPos": 4042}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "sqrt", "location": {"startLine": 84, "startCol": 5, "endLine": 108, "endCol": 6, "startPos": 3281, "endPos": 4042}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getIndexSize", "location": {"startLine": 114, "startCol": 5, "endLine": 116, "endCol": 6, "startPos": 4169, "endPos": 4239}, "filePath": "src/services/VectorSearchService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}], "errors": [], "language": "typescript"}, {"filePath": "src/services/EmbeddingService.ts", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 77, "startPos": 0, "endPos": 76}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "importedNames": [], "source": "@google/generative-ai", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 29, "startPos": 77, "endPos": 105}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "importedNames": [], "source": "dotenv", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 25, "startPos": 106, "endPos": 130}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "importedNames": [], "source": "path", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 37, "startPos": 131, "endPos": 167}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "importedNames": [], "source": "url", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 5, "startCol": 1, "endLine": 5, "endCol": 91, "startPos": 168, "endPos": 258}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "importedNames": [], "source": "../types/CodeElement.js", "isDefault": false, "isNamespace": false}, {"type": "variable", "name": "__filename", "location": {"startLine": 8, "startCol": 1, "endLine": 8, "endCol": 51, "startPos": 307, "endPos": 357}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "dirname", "location": {"startLine": 9, "startCol": 1, "endLine": 9, "endCol": 44, "startPos": 358, "endPos": 401}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "class", "name": "EmbeddingService", "location": {"startLine": 14, "startCol": 8, "endLine": 97, "endCol": 2, "startPos": 603, "endPos": 4096}, "filePath": "src/services/EmbeddingService.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [{"type": "method", "name": "constructor", "location": {"startLine": 19, "startCol": 5, "endLine": 28, "endCol": 6, "startPos": 736, "endPos": 1288}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "generateEmbeddings", "location": {"startLine": 35, "startCol": 5, "endLine": 72, "endCol": 6, "startPos": 1511, "endPos": 3235}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "codeElements", "type": "CodeElement[]"}]}, {"type": "method", "name": "embed<PERSON><PERSON><PERSON>", "location": {"startLine": 79, "startCol": 5, "endLine": 96, "endCol": 6, "startPos": 3466, "endPos": 4094}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "queryText", "type": "string"}]}], "properties": [{"type": "property", "name": "genAI", "propertyType": "GoogleGenerativeAI", "visibility": "private", "location": {"startLine": 14, "startCol": 12, "endLine": 14, "endCol": 17, "startPos": 640, "endPos": 645}, "filePath": "src/services/EmbeddingService.ts"}, {"type": "property", "name": "model", "propertyType": "GenerativeModel", "visibility": "private", "location": {"startLine": 15, "startCol": 12, "endLine": 15, "endCol": 17, "startPos": 679, "endPos": 684}, "filePath": "src/services/EmbeddingService.ts"}, {"type": "property", "name": "<PERSON><PERSON><PERSON><PERSON>", "propertyType": "string", "visibility": "private", "location": {"startLine": 16, "startCol": 12, "endLine": 16, "endCol": 18, "startPos": 715, "endPos": 721}, "filePath": "src/services/EmbeddingService.ts"}]}, {"type": "method", "name": "warn", "location": {"startLine": 19, "startCol": 5, "endLine": 28, "endCol": 6, "startPos": 736, "endPos": 1288}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "GoogleGenerativeAI", "location": {"startLine": 19, "startCol": 5, "endLine": 28, "endCol": 6, "startPos": 736, "endPos": 1288}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getGenerativeModel", "location": {"startLine": 19, "startCol": 5, "endLine": 28, "endCol": 6, "startPos": 736, "endPos": 1288}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "generateEmbeddings", "location": {"startLine": 35, "startCol": 5, "endLine": 72, "endCol": 6, "startPos": 1511, "endPos": 3235}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "codeElements", "type": "CodeElement[]"}]}, {"type": "method", "name": "error", "location": {"startLine": 35, "startCol": 5, "endLine": 72, "endCol": 6, "startPos": 1511, "endPos": 3235}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "trim", "location": {"startLine": 35, "startCol": 5, "endLine": 72, "endCol": 6, "startPos": 1511, "endPos": 3235}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "embedContent", "location": {"startLine": 54, "startCol": 17, "endLine": 54, "endCol": 78, "startPos": 2363, "endPos": 2424}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 35, "startCol": 5, "endLine": 72, "endCol": 6, "startPos": 1511, "endPos": 3235}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "warn", "location": {"startLine": 35, "startCol": 5, "endLine": 72, "endCol": 6, "startPos": 1511, "endPos": 3235}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 35, "startCol": 5, "endLine": 72, "endCol": 6, "startPos": 1511, "endPos": 3235}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "embed<PERSON><PERSON><PERSON>", "location": {"startLine": 79, "startCol": 5, "endLine": 96, "endCol": 6, "startPos": 3466, "endPos": 4094}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "queryText", "type": "string"}]}, {"type": "method", "name": "error", "location": {"startLine": 79, "startCol": 5, "endLine": 96, "endCol": 6, "startPos": 3466, "endPos": 4094}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "trim", "location": {"startLine": 79, "startCol": 5, "endLine": 96, "endCol": 6, "startPos": 3466, "endPos": 4094}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "warn", "location": {"startLine": 79, "startCol": 5, "endLine": 96, "endCol": 6, "startPos": 3466, "endPos": 4094}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "embedContent", "location": {"startLine": 90, "startCol": 13, "endLine": 90, "endCol": 69, "startPos": 3852, "endPos": 3908}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 79, "startCol": 5, "endLine": 96, "endCol": 6, "startPos": 3466, "endPos": 4094}, "filePath": "src/services/EmbeddingService.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "typescript"}, {"filePath": "src/parsers/TreeSitterParser.ts", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 64, "startPos": 0, "endPos": 63}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "importedNames": [], "source": "tree-sitter", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 30, "startPos": 85, "endPos": 114}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "importedNames": [], "source": "fs/promises", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 25, "startPos": 115, "endPos": 139}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "importedNames": [], "source": "path", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 5, "startCol": 1, "endLine": 7, "endCol": 34, "startPos": 140, "endPos": 335}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "importedNames": [], "source": "../types/CodeElement.js", "isDefault": false, "isNamespace": false}, {"type": "function", "name": "findChildByType", "location": {"startLine": 10, "startCol": 1, "endLine": 13, "endCol": 2, "startPos": 385, "endPos": 570}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "node", "type": "SyntaxNode | null"}, {"name": "type", "type": "string"}]}, {"type": "function", "name": "find", "location": {"startLine": 10, "startCol": 1, "endLine": 13, "endCol": 2, "startPos": 385, "endPos": 570}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "findChildrenByType", "location": {"startLine": 15, "startCol": 1, "endLine": 18, "endCol": 2, "startPos": 615, "endPos": 788}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "node", "type": "SyntaxNode | null"}, {"name": "type", "type": "string"}]}, {"type": "function", "name": "filter", "location": {"startLine": 15, "startCol": 1, "endLine": 18, "endCol": 2, "startPos": 615, "endPos": 788}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "getNodeText", "location": {"startLine": 20, "startCol": 1, "endLine": 22, "endCol": 2, "startPos": 823, "endPos": 977}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "node", "type": "SyntaxNode | null | undefined"}, {"name": "code", "type": "string"}]}, {"type": "function", "name": "substring", "location": {"startLine": 20, "startCol": 1, "endLine": 22, "endCol": 2, "startPos": 823, "endPos": 977}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "hasModifier", "location": {"startLine": 24, "startCol": 1, "endLine": 26, "endCol": 2, "startPos": 1042, "endPos": 1214}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "node", "type": "SyntaxNode | null"}, {"name": "kind", "type": "string"}]}, {"type": "function", "name": "some", "location": {"startLine": 24, "startCol": 1, "endLine": 26, "endCol": 2, "startPos": 1042, "endPos": 1214}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "hasKeyword", "location": {"startLine": 27, "startCol": 1, "endLine": 31, "endCol": 2, "startPos": 1215, "endPos": 1482}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "node", "type": "SyntaxNode | null"}, {"name": "keyword", "type": "string"}]}, {"type": "function", "name": "some", "location": {"startLine": 27, "startCol": 1, "endLine": 31, "endCol": 2, "startPos": 1215, "endPos": 1482}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "class", "name": "TreeSitte<PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 33, "startCol": 8, "endLine": 617, "endCol": 2, "startPos": 1491, "endPos": 30074}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [{"type": "method", "name": "constructor", "location": {"startLine": 38, "startCol": 5, "endLine": 40, "endCol": 6, "startPos": 1760, "endPos": 1817}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "method", "name": "initQueries", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "queryDir", "type": "string"}]}, {"type": "method", "name": "getLanguage", "location": {"startLine": 91, "startCol": 5, "endLine": 108, "endCol": 6, "startPos": 4219, "endPos": 4859}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "filePath", "type": "string"}]}, {"type": "method", "name": "parse", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "code", "type": "string"}, {"name": "filePath", "type": "string"}]}, {"type": "method", "name": "getLangName", "location": {"startLine": 159, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6916, "endPos": 7157}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "language", "type": "any"}], "visibility": "private"}, {"type": "method", "name": "getNodeLocation", "location": {"startLine": 168, "startCol": 6, "endLine": 181, "endCol": 6, "startPos": 7164, "endPos": 7744}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "node", "type": "SyntaxNode | null"}], "visibility": "private"}, {"type": "method", "name": "findDefinitionNode", "location": {"startLine": 184, "startCol": 5, "endLine": 210, "endCol": 6, "startPos": 7827, "endPos": 9290}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "captureNode", "type": "SyntaxNode"}, {"name": "<PERSON><PERSON><PERSON>", "type": "string"}], "visibility": "private"}, {"type": "method", "name": "processCaptures", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "captures", "type": "QueryCapture[]"}, {"name": "code", "type": "string"}, {"name": "filePath", "type": "string"}, {"name": "language", "type": "any"}], "visibility": "private"}, {"type": "method", "name": "createBaseElement", "location": {"startLine": 272, "startCol": 5, "endLine": 372, "endCol": 6, "startPos": 11907, "endPos": 16366}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "definitionNode", "type": "SyntaxNode"}, {"name": "code", "type": "string"}, {"name": "filePath", "type": "string"}, {"name": "nameText", "type": "string"}], "visibility": "private"}, {"type": "method", "name": "refineElement", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "element", "type": "CodeElement"}, {"name": "capture", "type": "QueryCapture"}, {"name": "definitionNode", "type": "SyntaxNode"}, {"name": "code", "type": "string"}], "visibility": "private"}, {"type": "method", "name": "extractFunctionDetails", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "funcElement", "type": "FunctionElement"}, {"name": "definitionNode", "type": "SyntaxNode"}, {"name": "code", "type": "string"}], "visibility": "private"}, {"type": "method", "name": "extractClassDetails", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "classElement", "type": "ClassElement"}, {"name": "definitionNode", "type": "SyntaxNode"}, {"name": "code", "type": "string"}], "visibility": "private"}], "properties": [{"type": "property", "name": "parser", "propertyType": "<PERSON><PERSON><PERSON>", "visibility": "private", "location": {"startLine": 33, "startCol": 12, "endLine": 33, "endCol": 18, "startPos": 1528, "endPos": 1534}, "filePath": "src/parsers/TreeSitterParser.ts"}, {"type": "property", "name": "languages", "propertyType": "Map<string, any>", "visibility": "private", "location": {"startLine": 34, "startCol": 12, "endLine": 34, "endCol": 21, "startPos": 1556, "endPos": 1565}, "filePath": "src/parsers/TreeSitterParser.ts"}, {"type": "property", "name": "queries", "propertyType": "Map<string, Map<string, Parser.Query>>", "visibility": "private", "location": {"startLine": 35, "startCol": 12, "endLine": 35, "endCol": 19, "startPos": 1664, "endPos": 1671}, "filePath": "src/parsers/TreeSitterParser.ts"}]}, {"type": "class", "name": "Map", "location": {"startLine": 33, "startCol": 8, "endLine": 617, "endCol": 2, "startPos": 1491, "endPos": 30074}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "class", "name": "Map", "location": {"startLine": 33, "startCol": 8, "endLine": 617, "endCol": 2, "startPos": 1491, "endPos": 30074}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "<PERSON><PERSON><PERSON>", "location": {"startLine": 38, "startCol": 5, "endLine": 40, "endCol": 6, "startPos": 1760, "endPos": 1817}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "initQueries", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "queryDir", "type": "string"}]}, {"type": "method", "name": "set", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "set", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "access", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "warn", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "access", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "queriesForLang", "location": {"startLine": 78, "startCol": 13, "endLine": 78, "endCol": 68, "startPos": 3673, "endPos": 3728}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "join", "location": {"startLine": 79, "startCol": 13, "endLine": 79, "endCol": 80, "startPos": 3741, "endPos": 3808}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "readFile", "location": {"startLine": 81, "startCol": 17, "endLine": 81, "endCol": 74, "startPos": 3843, "endPos": 3900}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "query", "location": {"startLine": 82, "startCol": 17, "endLine": 82, "endCol": 67, "startPos": 3917, "endPos": 3967}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "set", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "warn", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "set", "location": {"startLine": 42, "startCol": 5, "endLine": 89, "endCol": 6, "startPos": 1823, "endPos": 4213}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getLanguage", "location": {"startLine": 91, "startCol": 5, "endLine": 108, "endCol": 6, "startPos": 4219, "endPos": 4859}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "filePath", "type": "string"}]}, {"type": "variable", "name": "extname", "location": {"startLine": 92, "startCol": 9, "endLine": 92, "endCol": 64, "startPos": 4276, "endPos": 4331}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "toLowerCase", "location": {"startLine": 92, "startCol": 9, "endLine": 92, "endCol": 64, "startPos": 4276, "endPos": 4331}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "get", "location": {"startLine": 91, "startCol": 5, "endLine": 108, "endCol": 6, "startPos": 4219, "endPos": 4859}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "get", "location": {"startLine": 91, "startCol": 5, "endLine": 108, "endCol": 6, "startPos": 4219, "endPos": 4859}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "parse", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "code", "type": "string"}, {"name": "filePath", "type": "string"}]}, {"type": "variable", "name": "getLanguage", "location": {"startLine": 111, "startCol": 9, "endLine": 111, "endCol": 53, "startPos": 4933, "endPos": 4977}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "getLangName", "location": {"startLine": 112, "startCol": 9, "endLine": 112, "endCol": 76, "startPos": 4986, "endPos": 5053}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "setLanguage", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "parse", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "get", "location": {"startLine": 135, "startCol": 9, "endLine": 135, "endCol": 63, "startPos": 5985, "endPos": 6039}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "get", "location": {"startLine": 135, "startCol": 9, "endLine": 135, "endCol": 63, "startPos": 5985, "endPos": 6039}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "matches", "location": {"startLine": 142, "startCol": 21, "endLine": 142, "endCol": 66, "startPos": 6232, "endPos": 6277}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "processCaptures", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 110, "startCol": 5, "endLine": 157, "endCol": 6, "startPos": 4865, "endPos": 6910}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getLangName", "location": {"startLine": 159, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6916, "endPos": 7157}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "language", "type": "any"}]}, {"type": "method", "name": "entries", "location": {"startLine": 159, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6916, "endPos": 7157}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeLocation", "location": {"startLine": 168, "startCol": 6, "endLine": 181, "endCol": 6, "startPos": 7164, "endPos": 7744}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "node", "type": "SyntaxNode | null"}]}, {"type": "method", "name": "findDefinitionNode", "location": {"startLine": 184, "startCol": 5, "endLine": 210, "endCol": 6, "startPos": 7827, "endPos": 9290}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "captureNode", "type": "SyntaxNode"}, {"name": "<PERSON><PERSON><PERSON>", "type": "string"}]}, {"type": "method", "name": "startsWith", "location": {"startLine": 184, "startCol": 5, "endLine": 210, "endCol": 6, "startPos": 7827, "endPos": 9290}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "startsWith", "location": {"startLine": 184, "startCol": 5, "endLine": 210, "endCol": 6, "startPos": 7827, "endPos": 9290}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "includes", "location": {"startLine": 184, "startCol": 5, "endLine": 210, "endCol": 6, "startPos": 7827, "endPos": 9290}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "includes", "location": {"startLine": 184, "startCol": 5, "endLine": 210, "endCol": 6, "startPos": 7827, "endPos": 9290}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "processCaptures", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "captures", "type": "QueryCapture[]"}, {"name": "code", "type": "string"}, {"name": "filePath", "type": "string"}, {"name": "language", "type": "any"}]}, {"type": "variable", "name": "elementsMap", "location": {"startLine": 214, "startCol": 9, "endLine": 214, "endCol": 65, "startPos": 9419, "endPos": 9475}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "processedNodeIds", "location": {"startLine": 215, "startCol": 9, "endLine": 215, "endCol": 52, "startPos": 9541, "endPos": 9584}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "findDefinitionNode", "location": {"startLine": 222, "startCol": 17, "endLine": 222, "endCol": 83, "startPos": 9873, "endPos": 9939}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "has", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "nameText", "location": {"startLine": 227, "startCol": 17, "endLine": 227, "endCol": 58, "startPos": 10118, "endPos": 10159}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "createBaseElement", "location": {"startLine": 229, "startCol": 17, "endLine": 229, "endCol": 96, "startPos": 10251, "endPos": 10330}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "refineElement", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "set", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "add", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "findDefinitionNode", "location": {"startLine": 241, "startCol": 13, "endLine": 241, "endCol": 79, "startPos": 10790, "endPos": 10856}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "has", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "createBaseElement", "location": {"startLine": 245, "startCol": 13, "endLine": 245, "endCol": 82, "startPos": 10968, "endPos": 11037}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "has", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "get", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "refineElement", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "set", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "add", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "from", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "values", "location": {"startLine": 213, "startCol": 5, "endLine": 269, "endCol": 6, "startPos": 9297, "endPos": 11841}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "createBaseElement", "location": {"startLine": 272, "startCol": 5, "endLine": 372, "endCol": 6, "startPos": 11907, "endPos": 16366}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "definitionNode", "type": "SyntaxNode"}, {"name": "code", "type": "string"}, {"name": "filePath", "type": "string"}, {"name": "nameText", "type": "string"}]}, {"type": "variable", "name": "declarator", "location": {"startLine": 309, "startCol": 21, "endLine": 309, "endCol": 95, "startPos": 13550, "endPos": 13624}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "value", "location": {"startLine": 311, "startCol": 25, "endLine": 311, "endCol": 139, "startPos": 13687, "endPos": 13801}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "value", "location": {"startLine": 311, "startCol": 25, "endLine": 311, "endCol": 139, "startPos": 13687, "endPos": 13801}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "nameNode", "location": {"startLine": 336, "startCol": 9, "endLine": 338, "endCol": 77, "startPos": 14686, "endPos": 14905}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "nameNode", "location": {"startLine": 336, "startCol": 9, "endLine": 338, "endCol": 77, "startPos": 14686, "endPos": 14905}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "nameNode", "location": {"startLine": 336, "startCol": 9, "endLine": 338, "endCol": 77, "startPos": 14686, "endPos": 14905}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "declarator", "location": {"startLine": 341, "startCol": 13, "endLine": 341, "endCol": 87, "startPos": 15096, "endPos": 15170}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "varName", "location": {"startLine": 343, "startCol": 17, "endLine": 343, "endCol": 75, "startPos": 15217, "endPos": 15275}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "refineElement", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "element", "type": "CodeElement"}, {"name": "capture", "type": "QueryCapture"}, {"name": "definitionNode", "type": "SyntaxNode"}, {"name": "code", "type": "string"}]}, {"type": "method", "name": "startsWith", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "split", "location": {"startLine": 379, "startCol": 13, "endLine": 379, "endCol": 58, "startPos": 16657, "endPos": 16702}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "pop", "location": {"startLine": 379, "startCol": 13, "endLine": 379, "endCol": 58, "startPos": 16657, "endPos": 16702}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "extractFunctionDetails", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "extractFunctionDetails", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "extractClassDetails", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 405, "startCol": 21, "endLine": 405, "endCol": 83, "startPos": 18189, "endPos": 18251}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeText", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "slice", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 409, "startCol": 21, "endLine": 409, "endCol": 90, "startPos": 18440, "endPos": 18509}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 418, "startCol": 33, "endLine": 418, "endCol": 102, "startPos": 19105, "endPos": 19174}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "descendantsOfType", "location": {"startLine": 423, "startCol": 33, "endLine": 425, "endCol": 102, "startPos": 19547, "endPos": 19773}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 423, "startCol": 33, "endLine": 425, "endCol": 102, "startPos": 19547, "endPos": 19773}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 427, "startCol": 37, "endLine": 427, "endCol": 113, "startPos": 19871, "endPos": 19947}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "name", "location": {"startLine": 428, "startCol": 37, "endLine": 428, "endCol": 113, "startPos": 19984, "endPos": 20060}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "alias", "location": {"startLine": 429, "startCol": 37, "endLine": 429, "endCol": 114, "startPos": 20097, "endPos": 20174}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "startsWith", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "split", "location": {"startLine": 444, "startCol": 18, "endLine": 444, "endCol": 66, "startPos": 20759, "endPos": 20807}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "pop", "location": {"startLine": 444, "startCol": 18, "endLine": 444, "endCol": 66, "startPos": 20759, "endPos": 20807}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeText", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "slice", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "startsWith", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "split", "location": {"startLine": 470, "startCol": 17, "endLine": 470, "endCol": 65, "startPos": 22258, "endPos": 22306}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "pop", "location": {"startLine": 470, "startCol": 17, "endLine": 470, "endCol": 65, "startPos": 22258, "endPos": 22306}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeText", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeText", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "findChildByType", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "slice", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeText", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "slice", "location": {"startLine": 374, "startCol": 5, "endLine": 505, "endCol": 6, "startPos": 16372, "endPos": 23707}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "extractFunctionDetails", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "funcElement", "type": "FunctionElement"}, {"name": "definitionNode", "type": "SyntaxNode"}, {"name": "code", "type": "string"}]}, {"type": "method", "name": "hasModifier", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "hasKeyword", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "includes", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "paramsNode", "location": {"startLine": 513, "startCol": 9, "endLine": 514, "endCol": 78, "startPos": 24146, "endPos": 24314}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "paramsNode", "location": {"startLine": 513, "startCol": 9, "endLine": 514, "endCol": 78, "startPos": 24146, "endPos": 24314}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "filter", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "includes", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "map", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "nameNode", "location": {"startLine": 520, "startCol": 21, "endLine": 520, "endCol": 76, "startPos": 24640, "endPos": 24695}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "typeNode", "location": {"startLine": 521, "startCol": 21, "endLine": 521, "endCol": 76, "startPos": 24738, "endPos": 24793}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "returnTypeNode", "location": {"startLine": 530, "startCol": 9, "endLine": 531, "endCol": 79, "startPos": 25120, "endPos": 25293}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "returnTypeNode", "location": {"startLine": 530, "startCol": 9, "endLine": 531, "endCol": 79, "startPos": 25120, "endPos": 25293}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "actualTypeNode", "location": {"startLine": 536, "startCol": 13, "endLine": 538, "endCol": 51, "startPos": 25553, "endPos": 25763}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "actualTypeNode", "location": {"startLine": 536, "startCol": 13, "endLine": 538, "endCol": 51, "startPos": 25553, "endPos": 25763}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeText", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "includes", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "descendantsOfType", "location": {"startLine": 507, "startCol": 6, "endLine": 550, "endCol": 6, "startPos": 23714, "endPos": 26271}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "extractClassDetails", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "classElement", "type": "ClassElement"}, {"name": "definitionNode", "type": "SyntaxNode"}, {"name": "code", "type": "string"}]}, {"type": "method", "name": "hasModifier", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "heritageNode", "location": {"startLine": 555, "startCol": 9, "endLine": 555, "endCol": 80, "startPos": 26461, "endPos": 26532}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "extendsClause", "location": {"startLine": 557, "startCol": 13, "endLine": 557, "endCol": 83, "startPos": 26573, "endPos": 26643}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeText", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "implements<PERSON><PERSON>e", "location": {"startLine": 561, "startCol": 14, "endLine": 561, "endCol": 90, "startPos": 26831, "endPos": 26907}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "findChildrenByType", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "map", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getNodeText", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "classBodyNode", "location": {"startLine": 569, "startCol": 9, "endLine": 569, "endCol": 77, "startPos": 27202, "endPos": 27270}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "createBaseElement", "location": {"startLine": 576, "startCol": 21, "endLine": 576, "endCol": 129, "startPos": 27540, "endPos": 27648}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "extractFunctionDetails", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 581, "startCol": 26, "endLine": 581, "endCol": 114, "startPos": 27985, "endPos": 28073}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "nameNode", "location": {"startLine": 589, "startCol": 22, "endLine": 589, "endCol": 127, "startPos": 28598, "endPos": 28703}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "nameNode", "location": {"startLine": 589, "startCol": 22, "endLine": 589, "endCol": 127, "startPos": 28598, "endPos": 28703}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "typeNode", "location": {"startLine": 590, "startCol": 22, "endLine": 590, "endCol": 82, "startPos": 28725, "endPos": 28785}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 591, "startCol": 22, "endLine": 591, "endCol": 110, "startPos": 28807, "endPos": 28895}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 552, "startCol": 5, "endLine": 615, "endCol": 6, "startPos": 26277, "endPos": 30071}, "filePath": "src/parsers/TreeSitterParser.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "typescript"}, {"filePath": "src/core/CodeAnalyzer.ts", "elements": [{"type": "import", "name": "import", "location": {"startLine": 1, "startCol": 1, "endLine": 1, "endCol": 30, "startPos": 0, "endPos": 29}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "fs/promises", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 2, "startCol": 1, "endLine": 2, "endCol": 25, "startPos": 30, "endPos": 54}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "path", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 3, "startCol": 1, "endLine": 3, "endCol": 29, "startPos": 55, "endPos": 83}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "glob", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 4, "startCol": 1, "endLine": 4, "endCol": 67, "startPos": 84, "endPos": 150}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "../parsers/TreeSitterParser.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 5, "startCol": 1, "endLine": 9, "endCol": 34, "startPos": 151, "endPos": 387}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "../types/CodeElement.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 10, "startCol": 1, "endLine": 10, "endCol": 68, "startPos": 388, "endPos": 455}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "../services/EmbeddingService.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 11, "startCol": 1, "endLine": 11, "endCol": 74, "startPos": 471, "endPos": 544}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "../services/VectorSearchService.js", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 12, "startCol": 1, "endLine": 12, "endCol": 44, "startPos": 560, "endPos": 603}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "graphology", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 13, "startCol": 1, "endLine": 13, "endCol": 47, "startPos": 604, "endPos": 650}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "graphology-graphml", "isDefault": false, "isNamespace": false}, {"type": "import", "name": "import", "location": {"startLine": 14, "startCol": 1, "endLine": 14, "endCol": 41, "startPos": 651, "endPos": 691}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "importedNames": [], "source": "ignore", "isDefault": false, "isNamespace": false}, {"type": "class", "name": "CodeAnalyzer", "location": {"startLine": 16, "startCol": 8, "endLine": 344, "endCol": 2, "startPos": 700, "endPos": 17528}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false, "isAbstract": false, "methods": [{"type": "method", "name": "constructor", "location": {"startLine": 24, "startCol": 5, "endLine": 32, "endCol": 6, "startPos": 1036, "endPos": 1579}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "projectRoot", "type": "string"}, {"name": "queryDirectory", "type": "string"}]}, {"type": "method", "name": "initialize", "location": {"startLine": 34, "startCol": 5, "endLine": 43, "endCol": 6, "startPos": 1585, "endPos": 2005}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "queryDirOverride", "type": "string"}]}, {"type": "method", "name": "buildFileExtensionMap", "location": {"startLine": 46, "startCol": 5, "endLine": 57, "endCol": 6, "startPos": 2076, "endPos": 2662}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [], "visibility": "private"}, {"type": "method", "name": "analyzeProject", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [{"name": "options", "type": "AnalysisOptions"}]}, {"type": "method", "name": "findProjectFiles", "location": {"startLine": 133, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6645, "endPos": 8021}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [{"name": "findAllForMap", "type": "boolean"}], "visibility": "private"}, {"type": "method", "name": "semanticSearch", "location": {"startLine": 168, "startCol": 5, "endLine": 176, "endCol": 6, "startPos": 8027, "endPos": 8520}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "queryText", "type": "string"}, {"name": "topK", "type": "number"}, {"name": "filters", "type": "MetadataFilters"}]}, {"type": "method", "name": "resolveImportPath", "location": {"startLine": 179, "startCol": 5, "endLine": 222, "endCol": 6, "startPos": 8604, "endPos": 10965}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "importerPath", "type": "string"}, {"name": "importSource", "type": "string"}], "visibility": "private"}, {"type": "method", "name": "buildRelations", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "fileResults", "type": "FileAnalysisResult[]"}], "visibility": "private"}, {"type": "method", "name": "summarizeR<PERSON>ults", "location": {"startLine": 319, "startCol": 5, "endLine": 343, "endCol": 6, "startPos": 16519, "endPos": 17526}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "fileResults", "type": "FileAnalysisResult[]"}], "visibility": "private"}], "properties": [{"type": "property", "name": "parser", "propertyType": "TreeSitte<PERSON><PERSON><PERSON><PERSON>", "visibility": "private", "location": {"startLine": 16, "startCol": 12, "endLine": 16, "endCol": 18, "startPos": 733, "endPos": 739}, "filePath": "src/core/CodeAnalyzer.ts"}, {"type": "property", "name": "projectRoot", "propertyType": "string", "visibility": "private", "location": {"startLine": 17, "startCol": 12, "endLine": 17, "endCol": 23, "startPos": 771, "endPos": 782}, "filePath": "src/core/CodeAnalyzer.ts"}, {"type": "property", "name": "fileExtensionMap", "propertyType": "Map<string, string[]>", "visibility": "private", "location": {"startLine": 18, "startCol": 12, "endLine": 18, "endCol": 28, "startPos": 804, "endPos": 820}, "filePath": "src/core/CodeAnalyzer.ts"}, {"type": "property", "name": "queryDir", "propertyType": "string", "visibility": "private", "location": {"startLine": 19, "startCol": 12, "endLine": 19, "endCol": 20, "startPos": 911, "endPos": 919}, "filePath": "src/core/CodeAnalyzer.ts"}, {"type": "property", "name": "embeddingService", "propertyType": "EmbeddingService", "visibility": "private", "location": {"startLine": 20, "startCol": 12, "endLine": 20, "endCol": 28, "startPos": 941, "endPos": 957}, "filePath": "src/core/CodeAnalyzer.ts"}, {"type": "property", "name": "vectorSearchService", "propertyType": "VectorSearchService", "visibility": "private", "location": {"startLine": 21, "startCol": 12, "endLine": 21, "endCol": 31, "startPos": 989, "endPos": 1008}, "filePath": "src/core/CodeAnalyzer.ts"}]}, {"type": "class", "name": "Map", "location": {"startLine": 16, "startCol": 8, "endLine": 344, "endCol": 2, "startPos": 700, "endPos": 17528}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": true, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "resolve", "location": {"startLine": 24, "startCol": 5, "endLine": 32, "endCol": 6, "startPos": 1036, "endPos": 1579}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "TreeSitte<PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 24, "startCol": 5, "endLine": 32, "endCol": 6, "startPos": 1036, "endPos": 1579}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "resolve", "location": {"startLine": 24, "startCol": 5, "endLine": 32, "endCol": 6, "startPos": 1036, "endPos": 1579}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "cwd", "location": {"startLine": 24, "startCol": 5, "endLine": 32, "endCol": 6, "startPos": 1036, "endPos": 1579}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "EmbeddingService", "location": {"startLine": 24, "startCol": 5, "endLine": 32, "endCol": 6, "startPos": 1036, "endPos": 1579}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "VectorSearchService", "location": {"startLine": 24, "startCol": 5, "endLine": 32, "endCol": 6, "startPos": 1036, "endPos": 1579}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 24, "startCol": 5, "endLine": 32, "endCol": 6, "startPos": 1036, "endPos": 1579}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "initialize", "location": {"startLine": 34, "startCol": 5, "endLine": 43, "endCol": 6, "startPos": 1585, "endPos": 2005}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "queryDirOverride", "type": "string"}]}, {"type": "method", "name": "initQueries", "location": {"startLine": 34, "startCol": 5, "endLine": 43, "endCol": 6, "startPos": 1585, "endPos": 2005}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "buildFileExtensionMap", "location": {"startLine": 34, "startCol": 5, "endLine": 43, "endCol": 6, "startPos": 1585, "endPos": 2005}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 34, "startCol": 5, "endLine": 43, "endCol": 6, "startPos": 1585, "endPos": 2005}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "buildFileExtensionMap", "location": {"startLine": 46, "startCol": 5, "endLine": 57, "endCol": 6, "startPos": 2076, "endPos": 2662}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": []}, {"type": "method", "name": "clear", "location": {"startLine": 46, "startCol": 5, "endLine": 57, "endCol": 6, "startPos": 2076, "endPos": 2662}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "findProjectFiles", "location": {"startLine": 48, "startCol": 9, "endLine": 48, "endCol": 60, "startPos": 2163, "endPos": 2214}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 46, "startCol": 5, "endLine": 57, "endCol": 6, "startPos": 2076, "endPos": 2662}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 50, "startCol": 13, "endLine": 50, "endCol": 53, "startPos": 2294, "endPos": 2334}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "join", "location": {"startLine": 51, "startCol": 13, "endLine": 51, "endCol": 73, "startPos": 2347, "endPos": 2407}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "has", "location": {"startLine": 46, "startCol": 5, "endLine": 57, "endCol": 6, "startPos": 2076, "endPos": 2662}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "set", "location": {"startLine": 46, "startCol": 5, "endLine": 57, "endCol": 6, "startPos": 2076, "endPos": 2662}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "get", "location": {"startLine": 46, "startCol": 5, "endLine": 57, "endCol": 6, "startPos": 2076, "endPos": 2662}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 46, "startCol": 5, "endLine": 57, "endCol": 6, "startPos": 2076, "endPos": 2662}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "analyzeProject", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [{"name": "options", "type": "AnalysisOptions"}]}, {"type": "variable", "name": "findProjectFiles", "location": {"startLine": 61, "startCol": 9, "endLine": 61, "endCol": 57, "startPos": 2867, "endPos": 2915}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "map", "location": {"startLine": 65, "startCol": 9, "endLine": 87, "endCol": 12, "startPos": 3035, "endPos": 4362}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "join", "location": {"startLine": 66, "startCol": 13, "endLine": 66, "endCol": 76, "startPos": 3112, "endPos": 3175}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "readFile", "location": {"startLine": 68, "startCol": 17, "endLine": 68, "endCol": 74, "startPos": 3210, "endPos": 3267}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "parse", "location": {"startLine": 69, "startCol": 17, "endLine": 69, "endCol": 77, "startPos": 3284, "endPos": 3344}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 65, "startCol": 9, "endLine": 87, "endCol": 12, "startPos": 3035, "endPos": 4362}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "includes", "location": {"startLine": 65, "startCol": 9, "endLine": 87, "endCol": 12, "startPos": 3035, "endPos": 4362}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "for<PERSON>ach", "location": {"startLine": 65, "startCol": 9, "endLine": 87, "endCol": 12, "startPos": 3035, "endPos": 4362}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "warn", "location": {"startLine": 65, "startCol": 9, "endLine": 87, "endCol": 12, "startPos": 3035, "endPos": 4362}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "join", "location": {"startLine": 65, "startCol": 9, "endLine": 87, "endCol": 12, "startPos": 3035, "endPos": 4362}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "error", "location": {"startLine": 65, "startCol": 9, "endLine": 87, "endCol": 12, "startPos": 3035, "endPos": 4362}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "all", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "buildRelations", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "summarizeR<PERSON>ults", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "warn", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "clearIndex", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "generateEmbeddings", "location": {"startLine": 120, "startCol": 17, "endLine": 120, "endCol": 107, "startPos": 6110, "endPos": 6200}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "indexChunks", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getIndexSize", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 59, "startCol": 5, "endLine": 130, "endCol": 6, "startPos": 2668, "endPos": 6573}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "findProjectFiles", "location": {"startLine": 133, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6645, "endPos": 8021}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": true, "parameters": [{"name": "findAllForMap", "type": "boolean"}]}, {"type": "variable", "name": "files", "location": {"startLine": 139, "startCol": 13, "endLine": 143, "endCol": 16, "startPos": 6952, "endPos": 7132}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "ig", "location": {"startLine": 145, "startCol": 13, "endLine": 145, "endCol": 53, "startPos": 7146, "endPos": 7186}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "add", "location": {"startLine": 145, "startCol": 13, "endLine": 145, "endCol": 53, "startPos": 7146, "endPos": 7186}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "join", "location": {"startLine": 148, "startCol": 17, "endLine": 148, "endCol": 81, "startPos": 7238, "endPos": 7302}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "readFile", "location": {"startLine": 150, "startCol": 21, "endLine": 150, "endCol": 88, "startPos": 7345, "endPos": 7412}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "add", "location": {"startLine": 133, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6645, "endPos": 8021}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 133, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6645, "endPos": 8021}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 158, "startCol": 13, "endLine": 158, "endCol": 69, "startPos": 7685, "endPos": 7741}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "ignores", "location": {"startLine": 158, "startCol": 13, "endLine": 158, "endCol": 69, "startPos": 7685, "endPos": 7741}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "map", "location": {"startLine": 133, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6645, "endPos": 8021}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "replace", "location": {"startLine": 133, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6645, "endPos": 8021}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 133, "startCol": 5, "endLine": 166, "endCol": 6, "startPos": 6645, "endPos": 8021}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "semanticSearch", "location": {"startLine": 168, "startCol": 5, "endLine": 176, "endCol": 6, "startPos": 8027, "endPos": 8520}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": true, "isGenerator": false, "isArrow": false, "parameters": [{"name": "queryText", "type": "string"}, {"name": "topK", "type": "number"}, {"name": "filters", "type": "MetadataFilters"}]}, {"type": "method", "name": "log", "location": {"startLine": 168, "startCol": 5, "endLine": 176, "endCol": 6, "startPos": 8027, "endPos": 8520}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "embed<PERSON><PERSON><PERSON>", "location": {"startLine": 170, "startCol": 9, "endLine": 170, "endCol": 79, "startPos": 8221, "endPos": 8291}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "error", "location": {"startLine": 168, "startCol": 5, "endLine": 176, "endCol": 6, "startPos": 8027, "endPos": 8520}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "search", "location": {"startLine": 168, "startCol": 5, "endLine": 176, "endCol": 6, "startPos": 8027, "endPos": 8520}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "resolveImportPath", "location": {"startLine": 179, "startCol": 5, "endLine": 222, "endCol": 6, "startPos": 8604, "endPos": 10965}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "importerPath", "type": "string"}, {"name": "importSource", "type": "string"}]}, {"type": "method", "name": "startsWith", "location": {"startLine": 179, "startCol": 5, "endLine": 222, "endCol": 6, "startPos": 8604, "endPos": 10965}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "dirname", "location": {"startLine": 185, "startCol": 9, "endLine": 185, "endCol": 56, "startPos": 8884, "endPos": 8931}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "normalize", "location": {"startLine": 186, "startCol": 9, "endLine": 186, "endCol": 79, "startPos": 8940, "endPos": 9010}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "join", "location": {"startLine": 186, "startCol": 9, "endLine": 186, "endCol": 79, "startPos": 8940, "endPos": 9010}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "has", "location": {"startLine": 179, "startCol": 5, "endLine": 222, "endCol": 6, "startPos": 8604, "endPos": 10965}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "get", "location": {"startLine": 179, "startCol": 5, "endLine": 222, "endCol": 6, "startPos": 8604, "endPos": 10965}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "get", "location": {"startLine": 179, "startCol": 5, "endLine": 222, "endCol": 6, "startPos": 8604, "endPos": 10965}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "get", "location": {"startLine": 195, "startCol": 9, "endLine": 195, "endCol": 69, "startPos": 9453, "endPos": 9513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 202, "startCol": 18, "endLine": 202, "endCol": 80, "startPos": 10001, "endPos": 10063}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "join", "location": {"startLine": 209, "startCol": 9, "endLine": 209, "endCol": 64, "startPos": 10272, "endPos": 10327}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "get", "location": {"startLine": 210, "startCol": 9, "endLine": 210, "endCol": 79, "startPos": 10336, "endPos": 10406}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "find", "location": {"startLine": 214, "startCol": 18, "endLine": 214, "endCol": 90, "startPos": 10621, "endPos": 10693}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "buildRelations", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "fileResults", "type": "FileAnalysisResult[]"}]}, {"type": "method", "name": "log", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "graph", "location": {"startLine": 227, "startCol": 9, "endLine": 227, "endCol": 82, "startPos": 11097, "endPos": 11170}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "fileNodeMap", "location": {"startLine": 229, "startCol": 9, "endLine": 229, "endCol": 60, "startPos": 11180, "endPos": 11231}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "hasNode", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "addNode", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "set", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "updateNodeAttribute", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "setNodeAttribute", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "get", "location": {"startLine": 253, "startCol": 13, "endLine": 253, "endCol": 71, "startPos": 12374, "endPos": 12432}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "warn", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "importDetails", "location": {"startLine": 259, "startCol": 13, "endLine": 259, "endCol": 89, "startPos": 12654, "endPos": 12730}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "resolveImportPath", "location": {"startLine": 265, "startCol": 21, "endLine": 265, "endCol": 106, "startPos": 13056, "endPos": 13141}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "get", "location": {"startLine": 268, "startCol": 25, "endLine": 268, "endCol": 74, "startPos": 13205, "endPos": 13254}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "has", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "set", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "get", "location": {"startLine": 274, "startCol": 29, "endLine": 274, "endCol": 78, "startPos": 13619, "endPos": 13668}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "some", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "some", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "push", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "map", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "startsWith", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "for<PERSON>ach", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "from", "location": {"startLine": 298, "startCol": 18, "endLine": 298, "endCol": 88, "startPos": 15106, "endPos": 15176}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "uniqueNames", "location": {"startLine": 298, "startCol": 18, "endLine": 298, "endCol": 88, "startPos": 15106, "endPos": 15176}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "filter", "location": {"startLine": 298, "startCol": 18, "endLine": 298, "endCol": 88, "startPos": 15106, "endPos": 15176}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "hasEdge", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "addDirectedEdge", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "updateDirectedEdgeAttribute", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "updateDirectedEdgeAttribute", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "from", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "Set", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "updateDirectedEdgeAttribute", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "getDirectedEdgeAttribute", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "log", "location": {"startLine": 225, "startCol": 5, "endLine": 317, "endCol": 6, "startPos": 10972, "endPos": 16513}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "method", "name": "summarizeR<PERSON>ults", "location": {"startLine": 319, "startCol": 5, "endLine": 343, "endCol": 6, "startPos": 16519, "endPos": 17526}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": true, "parameters": [{"name": "fileResults", "type": "FileAnalysisResult[]"}]}, {"type": "method", "name": "log", "location": {"startLine": 319, "startCol": 5, "endLine": 343, "endCol": 6, "startPos": 16519, "endPos": 17526}, "filePath": "src/core/CodeAnalyzer.ts", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "typescript"}, {"filePath": "coverage/lcov-report/sorter.js", "elements": [{"type": "function", "name": "getTable", "location": {"startLine": 11, "startCol": 5, "endLine": 13, "endCol": 6, "startPos": 211, "endPos": 298}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "getTableHeader", "location": {"startLine": 15, "startCol": 5, "endLine": 17, "endCol": 6, "startPos": 357, "endPos": 443}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "getTable", "location": {"startLine": 15, "startCol": 5, "endLine": 17, "endCol": 6, "startPos": 357, "endPos": 443}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "getTableBody", "location": {"startLine": 19, "startCol": 5, "endLine": 21, "endCol": 6, "startPos": 502, "endPos": 583}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "getTable", "location": {"startLine": 19, "startCol": 5, "endLine": 21, "endCol": 6, "startPos": 502, "endPos": 583}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "getNthColumn", "location": {"startLine": 23, "startCol": 5, "endLine": 25, "endCol": 6, "startPos": 633, "endPos": 724}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "n"}]}, {"type": "function", "name": "getTableHeader", "location": {"startLine": 23, "startCol": 5, "endLine": 25, "endCol": 6, "startPos": 633, "endPos": 724}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "onFilterInput", "location": {"startLine": 27, "startCol": 5, "endLine": 42, "endCol": 6, "startPos": 730, "endPos": 1275}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "addSearchBox", "location": {"startLine": 45, "startCol": 5, "endLine": 50, "endCol": 6, "startPos": 1309, "endPos": 1603}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "loadColumns", "location": {"startLine": 53, "startCol": 5, "endLine": 75, "endCol": 6, "startPos": 1634, "endPos": 2387}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "variable", "name": "colNodes", "location": {"startLine": 54, "startCol": 9, "endLine": 58, "endCol": 15, "startPos": 1667, "endPos": 1798}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "loadRowData", "location": {"startLine": 78, "startCol": 5, "endLine": 95, "endCol": 6, "startPos": 2503, "endPos": 3014}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "tableRow"}]}, {"type": "function", "name": "Number", "location": {"startLine": 78, "startCol": 5, "endLine": 95, "endCol": 6, "startPos": 2503, "endPos": 3014}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "loadData", "location": {"startLine": 97, "startCol": 5, "endLine": 104, "endCol": 6, "startPos": 3045, "endPos": 3252}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "variable", "name": "rows", "location": {"startLine": 98, "startCol": 9, "endLine": 99, "endCol": 15, "startPos": 3075, "endPos": 3139}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "loadRowData", "location": {"startLine": 97, "startCol": 5, "endLine": 104, "endCol": 6, "startPos": 3045, "endPos": 3252}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "sortByIndex", "location": {"startLine": 106, "startCol": 5, "endLine": 135, "endCol": 6, "startPos": 3314, "endPos": 4176}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "index"}, {"name": "desc"}]}, {"type": "function", "name": "sorter", "location": {"startLine": 108, "startCol": 13, "endLine": 112, "endCol": 14, "startPos": 3397, "endPos": 3553}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "sorter", "location": {"startLine": 107, "startCol": 9, "endLine": 117, "endCol": 15, "startPos": 3358, "endPos": 3758}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "finalSorter", "location": {"startLine": 120, "startCol": 13, "endLine": 122, "endCol": 14, "startPos": 3792, "endPos": 3878}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "finalSorter", "location": {"startLine": 106, "startCol": 5, "endLine": 135, "endCol": 6, "startPos": 3314, "endPos": 4176}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "sorter", "location": {"startLine": 106, "startCol": 5, "endLine": 135, "endCol": 6, "startPos": 3314, "endPos": 4176}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "removeSortIndicators", "location": {"startLine": 137, "startCol": 5, "endLine": 143, "endCol": 6, "startPos": 4244, "endPos": 4469}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "variable", "name": "col", "location": {"startLine": 138, "startCol": 9, "endLine": 139, "endCol": 33, "startPos": 4286, "endPos": 4361}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "addSortIndicators", "location": {"startLine": 145, "startCol": 5, "endLine": 149, "endCol": 6, "startPos": 4534, "endPos": 4694}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "getNthColumn", "location": {"startLine": 145, "startCol": 5, "endLine": 149, "endCol": 6, "startPos": 4534, "endPos": 4694}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "enableUI", "location": {"startLine": 151, "startCol": 5, "endLine": 182, "endCol": 6, "startPos": 4750, "endPos": 5873}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 154, "startCol": 13, "endLine": 169, "endCol": 14, "startPos": 4815, "endPos": 5346}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 152, "startCol": 9, "endLine": 169, "endCol": 15, "startPos": 4780, "endPos": 5347}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "i", "location": {"startLine": 152, "startCol": 9, "endLine": 169, "endCol": 15, "startPos": 4780, "endPos": 5347}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "i", "location": {"startLine": 152, "startCol": 9, "endLine": 169, "endCol": 15, "startPos": 4780, "endPos": 5347}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "i", "location": {"startLine": 152, "startCol": 9, "endLine": 169, "endCol": 15, "startPos": 4780, "endPos": 5347}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "getNthColumn", "location": {"startLine": 151, "startCol": 5, "endLine": 182, "endCol": 6, "startPos": 4750, "endPos": 5873}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 151, "startCol": 5, "endLine": 182, "endCol": 6, "startPos": 4750, "endPos": 5873}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": {"startLine": 151, "startCol": 5, "endLine": 182, "endCol": 6, "startPos": 4750, "endPos": 5873}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "addSorting", "location": {"startLine": 2, "startCol": 1, "endLine": 194, "endCol": 6, "startPos": 21, "endPos": 6134}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "addSorting", "location": {"startLine": 2, "startCol": 1, "endLine": 194, "endCol": 6, "startPos": 21, "endPos": 6134}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "addSorting", "location": {"startLine": 2, "startCol": 1, "endLine": 194, "endCol": 6, "startPos": 21, "endPos": 6134}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "addSorting", "location": {"startLine": 2, "startCol": 1, "endLine": 194, "endCol": 6, "startPos": 21, "endPos": 6134}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "addSorting", "location": {"startLine": 2, "startCol": 1, "endLine": 194, "endCol": 6, "startPos": 21, "endPos": 6134}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "addSorting", "location": {"startLine": 2, "startCol": 1, "endLine": 194, "endCol": 6, "startPos": 21, "endPos": 6134}, "filePath": "coverage/lcov-report/sorter.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "javascript"}, {"filePath": "coverage/lcov-report/prettify.js", "elements": [{"type": "function", "name": "k", "location": {"startLine": 2, "startCol": 2525, "endLine": 2, "endCol": 5372, "startPos": 2545, "endPos": 5392}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "Z"}]}, {"type": "function", "name": "ab", "location": {"startLine": 2, "startCol": 2791, "endLine": 2, "endCol": 3056, "startPos": 2811, "endPos": 3076}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "ah"}]}, {"type": "function", "name": "T", "location": {"startLine": 2, "startCol": 3056, "endLine": 2, "endCol": 3220, "startPos": 3076, "endPos": 3240}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "af"}]}, {"type": "function", "name": "X", "location": {"startLine": 2, "startCol": 3220, "endLine": 2, "endCol": 4215, "startPos": 3240, "endPos": 4235}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "am"}]}, {"type": "variable", "name": "aq", "location": {"startLine": 2, "startCol": 3235, "endLine": 2, "endCol": 3394, "startPos": 3255, "endPos": 3414}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "ag", "location": {"startLine": 2, "startCol": 3532, "endLine": 2, "endCol": 3546, "startPos": 3552, "endPos": 3566}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "ab", "location": {"startLine": 2, "startCol": 3220, "endLine": 2, "endCol": 4215, "startPos": 3240, "endPos": 4235}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "T", "location": {"startLine": 2, "startCol": 3220, "endLine": 2, "endCol": 4215, "startPos": 3240, "endPos": 4235}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "T", "location": {"startLine": 2, "startCol": 3220, "endLine": 2, "endCol": 4215, "startPos": 3240, "endPos": 4235}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "W", "location": {"startLine": 2, "startCol": 4215, "endLine": 2, "endCol": 5198, "startPos": 4235, "endPos": 5218}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "al"}]}, {"type": "variable", "name": "aj", "location": {"startLine": 2, "startCol": 4230, "endLine": 2, "endCol": 4426, "startPos": 4250, "endPos": 4446}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "X", "location": {"startLine": 2, "startCol": 4215, "endLine": 2, "endCol": 5198, "startPos": 4235, "endPos": 5218}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "Error", "location": {"startLine": 2, "startCol": 2525, "endLine": 2, "endCol": 5372, "startPos": 2545, "endPos": 5392}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "W", "location": {"startLine": 2, "startCol": 2525, "endLine": 2, "endCol": 5372, "startPos": 2545, "endPos": 5392}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "RegExp", "location": {"startLine": 2, "startCol": 2525, "endLine": 2, "endCol": 5372, "startPos": 2545, "endPos": 5392}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "a", "location": {"startLine": 2, "startCol": 5372, "endLine": 2, "endCol": 6109, "startPos": 5392, "endPos": 6129}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "V"}]}, {"type": "function", "name": "aa", "location": {"startLine": 2, "startCol": 5652, "endLine": 2, "endCol": 6047, "startPos": 5672, "endPos": 6067}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "ab"}]}, {"type": "function", "name": "aa", "location": {"startLine": 2, "startCol": 5652, "endLine": 2, "endCol": 6047, "startPos": 5672, "endPos": 6067}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "aa", "location": {"startLine": 2, "startCol": 5372, "endLine": 2, "endCol": 6109, "startPos": 5392, "endPos": 6129}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "B", "location": {"startLine": 2, "startCol": 6109, "endLine": 2, "endCol": 6209, "startPos": 6129, "endPos": 6229}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "S"}, {"name": "U"}, {"name": "W"}, {"name": "T"}]}, {"type": "function", "name": "W", "location": {"startLine": 2, "startCol": 6109, "endLine": 2, "endCol": 6209, "startPos": 6129, "endPos": 6229}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "o", "location": {"startLine": 2, "startCol": 6220, "endLine": 2, "endCol": 6388, "startPos": 6240, "endPos": 6408}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "S"}]}, {"type": "function", "name": "g", "location": {"startLine": 2, "startCol": 6388, "endLine": 2, "endCol": 7521, "startPos": 6408, "endPos": 7541}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "U"}, {"name": "T"}]}, {"type": "function", "name": "k", "location": {"startLine": 2, "startCol": 6388, "endLine": 2, "endCol": 7521, "startPos": 6408, "endPos": 7541}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "W", "location": {"startLine": 2, "startCol": 6720, "endLine": 2, "endCol": 7511, "startPos": 6740, "endPos": 7531}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "W", "location": {"startLine": 2, "startCol": 6716, "endLine": 2, "endCol": 7512, "startPos": 6736, "endPos": 7532}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "W", "location": {"startLine": 2, "startCol": 6716, "endLine": 2, "endCol": 7512, "startPos": 6736, "endPos": 7532}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "W", "location": {"startLine": 2, "startCol": 6716, "endLine": 2, "endCol": 7512, "startPos": 6736, "endPos": 7532}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "W", "location": {"startLine": 2, "startCol": 6716, "endLine": 2, "endCol": 7512, "startPos": 6736, "endPos": 7532}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "W", "location": {"startLine": 2, "startCol": 6716, "endLine": 2, "endCol": 7512, "startPos": 6736, "endPos": 7532}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "i", "location": {"startLine": 2, "startCol": 7521, "endLine": 2, "endCol": 9272, "startPos": 7541, "endPos": 9292}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "T"}]}, {"type": "function", "name": "RegExp", "location": {"startLine": 2, "startCol": 7521, "endLine": 2, "endCol": 9272, "startPos": 7541, "endPos": 9292}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "RegExp", "location": {"startLine": 2, "startCol": 7521, "endLine": 2, "endCol": 9272, "startPos": 7541, "endPos": 9292}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "RegExp", "location": {"startLine": 2, "startCol": 7521, "endLine": 2, "endCol": 9272, "startPos": 7541, "endPos": 9292}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "g", "location": {"startLine": 2, "startCol": 7521, "endLine": 2, "endCol": 9272, "startPos": 7541, "endPos": 9292}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "K", "location": {"startLine": 2, "startCol": 9272, "endLine": 2, "endCol": 9373, "startPos": 9292, "endPos": 9393}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "Q", "location": {"startLine": 2, "startCol": 9373, "endLine": 2, "endCol": 10970, "startPos": 9393, "endPos": 10990}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "V"}, {"name": "ag"}]}, {"type": "function", "name": "ae", "location": {"startLine": 2, "startCol": 9747, "endLine": 2, "endCol": 10260, "startPos": 9767, "endPos": 10280}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "al"}]}, {"type": "function", "name": "ad", "location": {"startLine": 2, "startCol": 9747, "endLine": 2, "endCol": 10260, "startPos": 9767, "endPos": 10280}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "ae", "location": {"startLine": 2, "startCol": 9747, "endLine": 2, "endCol": 10260, "startPos": 9767, "endPos": 10280}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "ad", "location": {"startLine": 2, "startCol": 9747, "endLine": 2, "endCol": 10260, "startPos": 9767, "endPos": 10280}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "ad", "location": {"startLine": 2, "startCol": 10260, "endLine": 2, "endCol": 10635, "startPos": 10280, "endPos": 10655}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "ak"}]}, {"type": "function", "name": "ai", "location": {"startLine": 2, "startCol": 10332, "endLine": 2, "endCol": 10541, "startPos": 10352, "endPos": 10561}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "al"}, {"name": "ar"}]}, {"type": "variable", "name": "ap", "location": {"startLine": 2, "startCol": 10412, "endLine": 2, "endCol": 10428, "startPos": 10432, "endPos": 10448}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "ah", "location": {"startLine": 2, "startCol": 10541, "endLine": 2, "endCol": 10569, "startPos": 10561, "endPos": 10589}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "ae", "location": {"startLine": 2, "startCol": 9373, "endLine": 2, "endCol": 10970, "startPos": 9393, "endPos": 10990}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "D", "location": {"startLine": 2, "startCol": 10970, "endLine": 2, "endCol": 11894, "startPos": 10990, "endPos": 11914}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "ac"}]}, {"type": "function", "name": "c", "location": {"startLine": 2, "startCol": 11903, "endLine": 2, "endCol": 12071, "startPos": 11923, "endPos": 12091}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "U"}, {"name": "V"}]}, {"type": "function", "name": "q", "location": {"startLine": 2, "startCol": 12071, "endLine": 2, "endCol": 12179, "startPos": 12091, "endPos": 12199}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "T"}, {"name": "S"}]}, {"type": "function", "name": "d", "location": {"startLine": 2, "startCol": 14061, "endLine": 2, "endCol": 14269, "startPos": 14081, "endPos": 14289}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "V"}]}, {"type": "variable", "name": "S", "location": {"startLine": 2, "startCol": 14101, "endLine": 2, "endCol": 14123, "startPos": 14121, "endPos": 14143}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "q", "location": {"startLine": 2, "startCol": 14061, "endLine": 2, "endCol": 14269, "startPos": 14081, "endPos": 14289}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "D", "location": {"startLine": 2, "startCol": 14061, "endLine": 2, "endCol": 14269, "startPos": 14081, "endPos": 14289}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "y", "location": {"startLine": 2, "startCol": 14269, "endLine": 2, "endCol": 14425, "startPos": 14289, "endPos": 14445}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "W"}, {"name": "V"}, {"name": "U"}]}, {"type": "function", "name": "Q", "location": {"startLine": 2, "startCol": 14269, "endLine": 2, "endCol": 14425, "startPos": 14289, "endPos": 14445}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "d", "location": {"startLine": 2, "startCol": 14269, "endLine": 2, "endCol": 14425, "startPos": 14289, "endPos": 14445}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "b", "location": {"startLine": 2, "startCol": 14425, "endLine": 2, "endCol": 15491, "startPos": 14445, "endPos": 15511}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "ad"}]}, {"type": "function", "name": "Y", "location": {"startLine": 2, "startCol": 14440, "endLine": 2, "endCol": 14496, "startPos": 14460, "endPos": 14516}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "af"}]}, {"type": "variable", "name": "ac", "location": {"startLine": 2, "startCol": 14496, "endLine": 2, "endCol": 14533, "startPos": 14516, "endPos": 14553}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "ac", "location": {"startLine": 2, "startCol": 14496, "endLine": 2, "endCol": 14533, "startPos": 14516, "endPos": 14553}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "ac", "location": {"startLine": 2, "startCol": 14496, "endLine": 2, "endCol": 14533, "startPos": 14516, "endPos": 14553}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "now", "location": {"startLine": 2, "startCol": 14663, "endLine": 2, "endCol": 14697, "startPos": 14683, "endPos": 14717}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "U", "location": {"startLine": 2, "startCol": 14778, "endLine": 2, "endCol": 15487, "startPos": 14798, "endPos": 15507}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "o", "location": {"startLine": 2, "startCol": 14778, "endLine": 2, "endCol": 15487, "startPos": 14798, "endPos": 15507}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "Q", "location": {"startLine": 2, "startCol": 14778, "endLine": 2, "endCol": 15487, "startPos": 14798, "endPos": 15507}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "d", "location": {"startLine": 2, "startCol": 14778, "endLine": 2, "endCol": 15487, "startPos": 14798, "endPos": 15507}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "ad", "location": {"startLine": 2, "startCol": 14778, "endLine": 2, "endCol": 15487, "startPos": 14798, "endPos": 15507}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "U", "location": {"startLine": 2, "startCol": 14425, "endLine": 2, "endCol": 15491, "startPos": 14445, "endPos": 15511}, "filePath": "coverage/lcov-report/prettify.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "javascript"}, {"filePath": "coverage/lcov-report/block-navigation.js", "elements": [{"type": "function", "name": "toggleClass", "location": {"startLine": 24, "startCol": 5, "endLine": 29, "endCol": 6, "startPos": 915, "endPos": 1134}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "index"}]}, {"type": "function", "name": "makeCurrent", "location": {"startLine": 31, "startCol": 5, "endLine": 39, "endCol": 6, "startPos": 1140, "endPos": 1396}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": [{"name": "index"}]}, {"type": "function", "name": "toggleClass", "location": {"startLine": 31, "startCol": 5, "endLine": 39, "endCol": 6, "startPos": 1140, "endPos": 1396}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "goToPrevious", "location": {"startLine": 41, "startCol": 5, "endLine": 50, "endCol": 6, "startPos": 1402, "endPos": 1732}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "makeCurrent", "location": {"startLine": 41, "startCol": 5, "endLine": 50, "endCol": 6, "startPos": 1402, "endPos": 1732}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "function", "name": "goToNext", "location": {"startLine": 52, "startCol": 5, "endLine": 63, "endCol": 6, "startPos": 1738, "endPos": 2013}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false, "parameters": []}, {"type": "function", "name": "makeCurrent", "location": {"startLine": 52, "startCol": 5, "endLine": 63, "endCol": 6, "startPos": 1738, "endPos": 2013}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "jumpToCode", "location": {"startLine": 2, "startCol": 1, "endLine": 86, "endCol": 6, "startPos": 21, "endPos": 2606}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}, {"type": "variable", "name": "jumpToCode", "location": {"startLine": 2, "startCol": 1, "endLine": 86, "endCol": 6, "startPos": 21, "endPos": 2606}, "filePath": "coverage/lcov-report/block-navigation.js", "isExported": false, "isAsync": false, "isGenerator": false, "isArrow": false}], "errors": [], "language": "javascript"}], "errors": [], "projectRoot": "D:\\Code\\cyzer", "relations": {"options": {"type": "directed", "multi": false, "allowSelfLoops": false}, "attributes": {}, "nodes": [{"key": "file:test-gemini-embeddings.js", "attributes": {"type": "file", "filePath": "test-gemini-embeddings.js", "language": "javascript", "errorCount": 0, "elementCount": 15, "label": "test-gemini-embeddings.js"}}, {"key": "file:test-code-embeddings.js", "attributes": {"type": "file", "filePath": "test-code-embeddings.js", "language": "javascript", "errorCount": 0, "elementCount": 20, "label": "test-code-embeddings.js"}}, {"key": "file:jest.config.js", "attributes": {"type": "file", "filePath": "jest.config.js", "language": "javascript", "errorCount": 0, "elementCount": 0, "label": "jest.config.js"}}, {"key": "file:tests/TreeSitterParser.test.ts", "attributes": {"type": "file", "filePath": "tests/TreeSitterParser.test.ts", "language": "typescript", "errorCount": 0, "elementCount": 26, "label": "tests/TreeSitterParser.test.ts"}}, {"key": "file:tests/EmbeddingService.test.ts", "attributes": {"type": "file", "filePath": "tests/EmbeddingService.test.ts", "language": "typescript", "errorCount": 0, "elementCount": 15, "label": "tests/EmbeddingService.test.ts"}}, {"key": "file:tests/CodeAnalyzer.test.ts", "attributes": {"type": "file", "filePath": "tests/CodeAnalyzer.test.ts", "language": "typescript", "errorCount": 0, "elementCount": 49, "label": "tests/CodeAnalyzer.test.ts"}}, {"key": "file:test-files/simple.js", "attributes": {"type": "file", "filePath": "test-files/simple.js", "language": "javascript", "errorCount": 0, "elementCount": 2, "label": "test-files/simple.js"}}, {"key": "file:test-files/advanced.ts", "attributes": {"type": "file", "filePath": "test-files/advanced.ts", "language": "typescript", "errorCount": 0, "elementCount": 12, "label": "test-files/advanced.ts"}}, {"key": "file:test-files/advanced.js", "attributes": {"type": "file", "filePath": "test-files/advanced.js", "language": "javascript", "errorCount": 0, "elementCount": 8, "label": "test-files/advanced.js"}}, {"key": "file:src/index.ts", "attributes": {"type": "file", "filePath": "src/index.ts", "language": "typescript", "errorCount": 0, "elementCount": 14, "label": "src/index.ts"}}, {"key": "file:test-files/mini-repo/index.js", "attributes": {"type": "file", "filePath": "test-files/mini-repo/index.js", "language": "javascript", "errorCount": 0, "elementCount": 4, "label": "test-files/mini-repo/index.js"}}, {"key": "file:test-files/mini-repo/c.js", "attributes": {"type": "file", "filePath": "test-files/mini-repo/c.js", "language": "javascript", "errorCount": 0, "elementCount": 2, "label": "test-files/mini-repo/c.js"}}, {"key": "file:test-files/mini-repo/b.js", "attributes": {"type": "file", "filePath": "test-files/mini-repo/b.js", "language": "javascript", "errorCount": 0, "elementCount": 7, "label": "test-files/mini-repo/b.js"}}, {"key": "file:test-files/mini-repo/a.js", "attributes": {"type": "file", "filePath": "test-files/mini-repo/a.js", "language": "javascript", "errorCount": 0, "elementCount": 7, "label": "test-files/mini-repo/a.js"}}, {"key": "file:src/types/CodeElement.ts", "attributes": {"type": "file", "filePath": "src/types/CodeElement.ts", "language": "typescript", "errorCount": 0, "elementCount": 16, "label": "src/types/CodeElement.ts"}}, {"key": "file:src/services/VectorSearchService.ts", "attributes": {"type": "file", "filePath": "src/services/VectorSearchService.ts", "language": "typescript", "errorCount": 0, "elementCount": 17, "label": "src/services/VectorSearchService.ts"}}, {"key": "file:src/services/EmbeddingService.ts", "attributes": {"type": "file", "filePath": "src/services/EmbeddingService.ts", "language": "typescript", "errorCount": 0, "elementCount": 24, "label": "src/services/EmbeddingService.ts"}}, {"key": "file:src/parsers/TreeSitterParser.ts", "attributes": {"type": "file", "filePath": "src/parsers/TreeSitterParser.ts", "language": "typescript", "errorCount": 0, "elementCount": 166, "label": "src/parsers/TreeSitterParser.ts"}}, {"key": "file:src/core/CodeAnalyzer.ts", "attributes": {"type": "file", "filePath": "src/core/CodeAnalyzer.ts", "language": "typescript", "errorCount": 0, "elementCount": 135, "label": "src/core/CodeAnalyzer.ts"}}, {"key": "file:coverage/lcov-report/sorter.js", "attributes": {"type": "file", "filePath": "coverage/lcov-report/sorter.js", "language": "javascript", "errorCount": 0, "elementCount": 41, "label": "coverage/lcov-report/sorter.js"}}, {"key": "file:coverage/lcov-report/prettify.js", "attributes": {"type": "file", "filePath": "coverage/lcov-report/prettify.js", "language": "javascript", "errorCount": 0, "elementCount": 68, "label": "coverage/lcov-report/prettify.js"}}, {"key": "file:coverage/lcov-report/block-navigation.js", "attributes": {"type": "file", "filePath": "coverage/lcov-report/block-navigation.js", "language": "javascript", "errorCount": 0, "elementCount": 9, "label": "coverage/lcov-report/block-navigation.js"}}], "edges": []}, "summary": {"totalFilesAnalyzed": 22, "filesWithErrors": 0, "elementsByType": {"import": 50, "variable": 236, "class": 12, "method": 209, "function": 131, "interface": 15, "type": 2, "enum": 2}, "totalElements": 657, "languages": {"javascript": 12, "typescript": 10}}}}