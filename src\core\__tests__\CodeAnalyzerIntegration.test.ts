import { CodeAnalyzer } from '../CodeAnalyzer.js';
import { EmbeddingService } from '../../services/EmbeddingService.js';
import { VectorSearchService } from '../../services/VectorSearchService.js';
import { CodeElement, CodeElementType, AnalysisOptions, ProjectAnalysisResult, EmbeddedCodeChunk, SearchResult } from '../../types/CodeElement.js';
import fs from 'fs/promises';
import path from 'path';

// Mock EmbeddingService to avoid actual API calls
const mockGenerateEmbeddings = jest.fn();
const mockEmbedQuery = jest.fn();

jest.mock('../../services/EmbeddingService', () => ({
    EmbeddingService: jest.fn().mockImplementation(() => ({
        generateEmbeddings: mockGenerateEmbeddings,
        embedQuery: mockEmbedQuery,
    })),
}));

// We will use the actual VectorSearchService as it's already unit-tested

// Mock TreeSitterParser and its dependencies to control parsed output
const mockParserParse = jest.fn();
jest.mock('../../parsers/TreeSitterParser', () => ({
    TreeSitterParser: jest.fn().mockImplementation(() => ({
        initQueries: jest.fn().mockResolvedValue(undefined),
        parse: mockParserParse,
    })),
}));

// Mock fs and glob for controlled file system interactions
jest.mock('fs/promises');
jest.mock('glob');

// Mock dotenv to prevent it from trying to load .env during tests
jest.mock('dotenv', () => ({
    config: jest.fn(),
}));

const mockProjectRoot = 'd:/mock/project';

describe('CodeAnalyzer Integration with Embedding and VectorSearch Services', () => {
    let codeAnalyzer: CodeAnalyzer;
    let consoleWarnSpy: jest.SpyInstance;
    let consoleLogSpy: jest.SpyInstance;

    const sampleCodeElements: CodeElement[] = [
        {
            name: 'func1',
            type: CodeElementType.FUNCTION,
            filePath: 'file1.ts',
            location: { startLine: 1, endLine: 3, startColumn: 0, endColumn: 1 }, 
            fullText: 'function func1() {}',
            summary: 'Function 1'
        },
        {
            name: 'classA',
            type: CodeElementType.CLASS,
            filePath: 'file2.ts',
            location: { startLine: 1, endLine: 5, startColumn: 0, endColumn: 1 },
            fullText: 'class classA {}',
            summary: 'Class A'
        },
    ];

    const sampleEmbeddedChunks: EmbeddedCodeChunk[] = sampleCodeElements.map((el, i) => ({
        ...el,
        embedding: [0.1 * (i + 1), 0.2 * (i + 1)],
        embeddingModel: 'mock-model',
    }));

    beforeEach(async () => {
        // Reset mocks
        (fs.readFile as jest.Mock).mockReset();
        (fs.stat as jest.Mock).mockReset(); // If CodeAnalyzer uses stat
        (require('glob').glob as jest.Mock).mockReset();
        mockParserParse.mockReset();
        mockGenerateEmbeddings.mockReset();
        mockEmbedQuery.mockReset();

        // Default mock implementations
        (fs.readFile as jest.Mock).mockResolvedValue('file content');
        (require('glob').glob as jest.Mock).mockImplementation((pattern: string, options: any, cb: Function) => {
            if (pattern.includes('*.{js,jsx,ts,tsx,mjs,cjs}')) {
                 // For findProjectFiles
                return cb(null, ['file1.ts', 'file2.ts']);
            }
            return cb(null, []); // Default for other globs like queries
        });
        // For buildFileExtensionMap, glob is called with different params
        (require('glob').glob as jest.Mock).mockImplementation((pattern, options) => {
            if (options && options.cwd === mockProjectRoot && pattern === '**/*.{js,jsx,ts,tsx,mjs,cjs}') {
                return Promise.resolve(['file1.ts', 'file2.ts']); 
            }
            return Promise.resolve([]);
        });

        mockParserParse.mockImplementation((content: string, filePath: string) => {
            if (filePath === 'file1.ts') return { filePath, elements: [sampleCodeElements[0]], errors: [], language: 'typescript' };
            if (filePath === 'file2.ts') return { filePath, elements: [sampleCodeElements[1]], errors: [], language: 'typescript' };
            return { filePath, elements: [], errors: [], language: 'typescript' };
        });

        codeAnalyzer = new CodeAnalyzer(mockProjectRoot);
        await codeAnalyzer.initialize(); // Initialize parser and file map

        consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
        consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    });

    afterEach(() => {
        consoleWarnSpy.mockRestore();
        consoleLogSpy.mockRestore();
    });

    describe('analyzeProject with indexForSearch', () => {
        it('should generate embeddings and index them when indexForSearch is true', async () => {
            mockGenerateEmbeddings.mockResolvedValue(sampleEmbeddedChunks);
            
            const options: AnalysisOptions = { indexForSearch: true, includeElementContent: true };
            const result = await codeAnalyzer.analyzeProject(options);

            expect(mockGenerateEmbeddings).toHaveBeenCalledWith(sampleCodeElements);
            // Verify that VectorSearchService.indexChunks was called (indirectly, by checking size or specific search)
            // We can't directly spy on vectorSearchService.indexChunks as it's instantiated internally by CodeAnalyzer.
            // So we test its effect:
            const vectorSearchServiceInstance = (codeAnalyzer as any).vectorSearchService as VectorSearchService;
            expect(vectorSearchServiceInstance.getIndexSize()).toBe(sampleEmbeddedChunks.length);
            
            // Check if a known item is searchable
            const queryVector = [0.1, 0.2]; // Matches sampleEmbeddedChunks[0]
            mockEmbedQuery.mockResolvedValue(queryVector);
            const searchResults = await codeAnalyzer.semanticSearch('func1', 1);
            expect(searchResults.length).toBe(1);
            expect(searchResults[0].chunk.name).toBe('func1');
        });

        it('should warn if indexForSearch is true but includeElementContent is false', async () => {
            const options: AnalysisOptions = { indexForSearch: true, includeElementContent: false }; // Content not included
            await codeAnalyzer.analyzeProject(options);
            expect(consoleWarnSpy).toHaveBeenCalledWith('Warning: indexForSearch is true, but includeElementContent was false. Forcing includeElementContent for indexing.');
            // Even with warning, it should proceed (current implementation detail)
            expect(mockGenerateEmbeddings).toHaveBeenCalled(); 
        });

        it('should not generate embeddings if indexForSearch is false or undefined', async () => {
            const options: AnalysisOptions = { includeElementContent: true }; // indexForSearch is undefined
            await codeAnalyzer.analyzeProject(options);
            expect(mockGenerateEmbeddings).not.toHaveBeenCalled();

            const optionsFalse: AnalysisOptions = { indexForSearch: false, includeElementContent: true };
            await codeAnalyzer.analyzeProject(optionsFalse);
            expect(mockGenerateEmbeddings).not.toHaveBeenCalled();
        });
    });

    describe('semanticSearch', () => {
        beforeEach(async () => {
            // Pre-index some data for search tests
            mockGenerateEmbeddings.mockResolvedValue(sampleEmbeddedChunks);
            await codeAnalyzer.analyzeProject({ indexForSearch: true, includeElementContent: true });
        });

        it('should call EmbeddingService.embedQuery and VectorSearchService.search', async () => {
            const queryText = 'find function 1';
            const mockQueryVector = [0.9, 0.8];
            mockEmbedQuery.mockResolvedValue(mockQueryVector);
            
            // Mock VectorSearchService.search by spying on the instance used by CodeAnalyzer
            const vectorSearchServiceInstance = (codeAnalyzer as any).vectorSearchService as VectorSearchService;
            const searchSpy = jest.spyOn(vectorSearchServiceInstance, 'search');
            searchSpy.mockResolvedValue([{ chunk: sampleEmbeddedChunks[0], similarity: 0.99 }]);

            const results = await codeAnalyzer.semanticSearch(queryText, 1);

            expect(mockEmbedQuery).toHaveBeenCalledWith(queryText);
            expect(searchSpy).toHaveBeenCalledWith(mockQueryVector, 1, undefined);
            expect(results.length).toBe(1);
            expect(results[0].chunk.name).toBe(sampleEmbeddedChunks[0].name);

            searchSpy.mockRestore();
        });

        it('should return empty array if query embedding fails', async () => {
            mockEmbedQuery.mockResolvedValue(null);
            const results = await codeAnalyzer.semanticSearch('test query', 1);
            expect(results).toEqual([]);
            expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to generate query vector. Cannot perform search.');
        });
    });
});
