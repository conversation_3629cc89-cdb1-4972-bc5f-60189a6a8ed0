# Cyzer: Vector Search Integration Development Plan

This document outlines the plan for integrating vector search capabilities into the `cyzer` code analysis tool. The goal is to enable semantic understanding and querying of codebases.

## 1. Current State of Cyzer

`cyzer` currently provides the following core functionalities relevant to this integration:

*   **AST-based Parsing**: Utilizes Tree-sitter to parse various programming languages (initially JavaScript/TypeScript).
*   **Code Element Extraction**: Identifies and extracts structured code elements such as functions, classes, methods, imports, and exports.
*   **Metadata Richness**: For each element, it captures its type, name, file path, start/end positions, and the raw `fullText` of the element.
*   **File Discovery**: Scans project directories for relevant source files, respecting `.gitignore` configurations.
*   **Relationship Graphing**: Builds a graph representing relationships between files and code elements (e.g., imports, exports, containment).

This foundation is strong for building semantic search, as the `fullText` of code elements can be directly used for generating embeddings, and the associated metadata provides crucial context.

## 2. Core Principles for Code Embeddings & Semantic Search

Based on research and best practices, the following principles will guide the development:

*   **Meaningful Code Chunking**:
    *   **Strategy**: Primarily use existing `CodeElement`s (functions, methods, classes) as the base unit for embeddings. Their `fullText` is ideal.
    *   **Granularity**: For very long elements, explore strategies to break them into smaller, semantically coherent sub-chunks (e.g., based on token limits or logical blocks within the AST).
    *   **Context**: Ensure each chunk retains its metadata (file path, original element type, line numbers).
*   **Embedding Model Agnosticism**:
    *   **Initial Model**: Start with Google's Gemini `embedding-001` model due to its availability and general-purpose text embedding capabilities.
    *   **Pluggable Design**: Design the embedding service to allow for easy swapping or addition of other models in the future (e.g., code-specific models like CodeBERT, GraphCodeBERT, UniXcoder) to leverage advancements in the field.
*   **Efficient Vector Storage & Retrieval**:
    *   **Initial Storage**: Begin with an in-memory vector store (e.g., simple array with brute-force cosine similarity) for rapid prototyping and testing on smaller codebases.
    *   **Scalable Solution**: Plan for integration with a dedicated vector database (e.g., Faiss, Qdrant, Weaviate, Milvus, Pinecone) for handling larger projects and providing efficient Approximate Nearest Neighbor (ANN) search.
*   **Hybrid Search Potential**:
    *   While the primary focus is semantic (vector) search, acknowledge that combining it with traditional lexical (keyword-based, e.g., BM25) search often yields superior results. This can be a future enhancement.
*   **Rich Metadata Association**:
    *   Crucially, all generated embeddings must be tightly coupled with their source metadata (file path, element name, element type, start/end lines, language). This allows for filtering search results and providing users with actionable context.

## 3. Proposed Architecture & Modules

The integration will involve developing the following new modules/services within `cyzer`:

*   **`EmbeddingService`**:
    *   **Responsibilities**: 
        *   Receiving code elements (or pre-defined chunks) from `CodeAnalyzer`.
        *   Preparing the text content for the embedding model.
        *   Interfacing with the chosen embedding model (e.g., Gemini API) to generate vector embeddings.
        *   Returning code chunks enriched with their vector embeddings.
    *   **Interface Ideas**:
        *   `generateEmbeddings(codeElements: CodeElement[]): Promise<EmbeddedCodeChunk[]>`
*   **`VectorSearchService`**:
    *   **Responsibilities**:
        *   Managing the vector index (in-memory or connection to a vector DB).
        *   Providing methods to add (index) new embedded code chunks.
        *   Providing methods to perform semantic search given a query string (which will also be embedded) or a query vector.
        *   Handling filtering based on metadata.
    *   **Interface Ideas**:
        *   `indexChunks(chunks: EmbeddedCodeChunk[]): Promise<void>`
        *   `search(queryText: string, topK: number, filters?: MetadataFilters): Promise<SearchResult[]>`
        *   `searchByVector(queryVector: number[], topK: number, filters?: MetadataFilters): Promise<SearchResult[]>`
*   **`CodeAnalyzer` Enhancements**:
    *   Integrate with `EmbeddingService` to process code elements after parsing.
    *   Potentially refine its output or provide specific methods to facilitate optimal chunking for embeddings.
*   **CLI / API Layer**:
    *   Expose new commands/endpoints for:
        *   `cyzer index <project_path>`: To parse a project, generate embeddings, and build the vector index.
        *   `cyzer search "<semantic_query>" [--project_path <path>] [--topK <k>] [--filter <key=value>]`: To perform a semantic search over an indexed project.

```mermaid
graph TD
    A[Project Files] --> B(CodeAnalyzer);
    B -- Code Elements --> C(EmbeddingService);
    C -- Text for Embedding --> D(Embedding Model API e.g., Gemini);
    D -- Vector Embeddings --> C;
    C -- Embedded Chunks --> E(VectorSearchService);
    E -- Stores/Indexes --> F(Vector Database / In-Memory Store);
    
    G[User Query CLI/API] -- Query Text --> C;
    G --> E;
    E -- Retrieves from --> F;
    E -- Search Results --> G;
```

## 4. Test-Driven Development (TDD) Strategy

A rigorous TDD approach is essential for building a reliable code analysis and search tool.

*   **Unit Tests**:
    *   **`EmbeddingService`**: Mock embedding API calls. Test text preparation, correct handling of API responses, and enrichment of chunks with embeddings.
    *   **`VectorSearchService`**: 
        *   For in-memory store: Test indexing, similarity calculations (cosine similarity), top-K retrieval, metadata filtering.
        *   For vector DB integration: Mock DB client, test connection, data formatting for indexing, query construction, and result parsing.
    *   **Code Chunking Logic**: If advanced chunking is added to `CodeAnalyzer` or `EmbeddingService`, test its accuracy against various code structures.
*   **Integration Tests**:
    *   Test the flow from `CodeAnalyzer` -> `EmbeddingService` -> `VectorSearchService` (indexing part).
    *   Test the flow for a search query: Query embedding -> `VectorSearchService` -> retrieval and ranking.
*   **End-to-End (E2E) Tests**:
    *   Use small, well-defined test repositories.
    *   Define a set of semantic queries and their expected top results (or types of results) from these repositories.
    *   These tests will be more qualitative initially, focusing on relevance.

## 5. Test Repositories

To effectively test the system, a diverse set of repositories will be used. These can be cloned locally for testing purposes.

*   **Small & Focused**: 
    *   `clsx` (or similar small JS utility): Good for testing basic function/module level embeddings and simple queries.
    *   A collection of standalone algorithm implementations in JS/TS.
*   **Medium Complexity**: 
    *   `axios` (HTTP client library): Offers classes, methods, asynchronous patterns. Good for testing semantic similarity between related functionalities.
    *   A small to medium Express.js or Fastify application.
*   **`cyzer` Itself**: As `cyzer` grows, it can serve as a test subject for its own capabilities (dogfooding).

## 6. Potential Roadmap (High-Level)

*   **Sprint 1: Core Infrastructure & In-Memory Search**
    *   Develop `EmbeddingService` with Gemini integration.
    *   Develop `VectorSearchService` with in-memory storage and cosine similarity search.
    *   Basic integration into `CodeAnalyzer` to embed elements.
    *   Initial CLI commands for indexing and searching (on a single file or small project).
    *   Unit tests for new services.
*   **Sprint 2: Project-Level Indexing & Basic E2E Tests**
    *   Enhance CLI to handle full project indexing.
    *   Develop initial E2E tests with a small test repository.
    *   Refine code chunking strategy based on initial results.
*   **Sprint 3: Vector Database Integration (Proof of Concept)**
    *   Choose and integrate a lightweight vector database (e.g., Faiss, Qdrant local instance).
    *   Update `VectorSearchService` to use the DB.
    *   Performance comparison with in-memory search.
*   **Sprint 4: Advanced Features & Optimization**
    *   Implement metadata filtering in search.
    *   Explore UI for displaying search results (if applicable).
    *   Investigate more advanced code-specific embedding models.
    *   Performance profiling and optimization.
*   **Ongoing**: Refinement, bug fixing, documentation, expansion of test coverage.

## 7. Open Questions & Considerations

*   **Optimal Chunk Size/Strategy**: Requires experimentation. What level of granularity yields the best search results for code?
*   **Embedding Model Choice**: How much improvement do code-specific models offer over general text models for semantic code search? This needs to be evaluated later.
*   **Scalability**: How will the system perform on very large codebases (e.g., millions of lines of code)? This will heavily influence the choice of vector database and indexing strategies.
*   **Cross-Language Search**: If `cyzer` supports multiple languages, how will embeddings from different languages interact? Will separate indices be needed?
*   **Incremental Updates**: How to efficiently update the vector index when code changes (avoid re-indexing everything)?

This plan provides a starting point and will evolve as development progresses and more insights are gained.
