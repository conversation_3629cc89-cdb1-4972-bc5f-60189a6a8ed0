/**
 * Decorator-specific types and interfaces
 */

import { CodeElement, CodeElementType } from '../../../types/CodeElement.js';

/**
 * Enhanced decorator element with framework-specific information
 */
export interface DecoratorElement extends CodeElement {
  type: CodeElementType.NestJSDecorator | CodeElementType.NestJSController | 
        CodeElementType.NestJSService | CodeElementType.NestJSModule |
        CodeElementType.NestJSEndpoint | CodeElementType.NestJSGuard |
        CodeElementType.NestJSInterceptor | CodeElementType.NestJSPipe |
        CodeElementType.NestJSInjection;
  
  decoratorName: string;           // e.g., 'Controller', 'Injectable', 'Get'
  arguments?: any[];               // Decorator arguments
  target?: string;                 // What the decorator is applied to
  framework?: string;              // 'nestjs', 'angular', 'react', etc.
}

/**
 * Detailed decorator information
 */
export interface DecoratorInfo {
  name: string;                    // Decorator name without @
  framework?: string;              // Framework this decorator belongs to
  arguments?: any[];               // Parsed arguments
  target: string;                  // 'class', 'method', 'property', 'parameter'
  
  // Decorator type flags
  isParameterDecorator: boolean;
  isMethodDecorator: boolean;
  isClassDecorator: boolean;
  isPropertyDecorator: boolean;
}

/**
 * NestJS-specific decorator information
 */
export interface NestJSDecoratorInfo extends DecoratorInfo {
  framework: 'nestjs';
  
  // HTTP decorators
  httpMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD';
  route?: string;                  // Route path
  
  // DI decorators
  injectionToken?: string;         // Custom injection token
  isOptional?: boolean;            // @Optional() decorator
  
  // Module decorators
  moduleMetadata?: {
    imports?: string[];
    providers?: string[];
    controllers?: string[];
    exports?: string[];
  };
  
  // Guard/Interceptor/Pipe decorators
  guardClasses?: string[];
  interceptorClasses?: string[];
  pipeClasses?: string[];
  
  // Validation decorators
  validationRules?: {
    type?: string;                 // 'string', 'number', 'email', etc.
    constraints?: any[];           // Min, Max, Length constraints
    isOptional?: boolean;
  };
  
  // Swagger/OpenAPI decorators
  apiDocumentation?: {
    tags?: string[];
    summary?: string;
    description?: string;
    responses?: { [statusCode: string]: string };
  };
}

/**
 * Angular-specific decorator information (for future use)
 */
export interface AngularDecoratorInfo extends DecoratorInfo {
  framework: 'angular';
  
  // Component decorators
  selector?: string;
  templateUrl?: string;
  styleUrls?: string[];
  
  // Input/Output decorators
  bindingPropertyName?: string;
  
  // Lifecycle decorators
  lifecycleHook?: string;
}

/**
 * React-specific decorator information (for future use)
 */
export interface ReactDecoratorInfo extends DecoratorInfo {
  framework: 'react';
  
  // MobX decorators
  isObserver?: boolean;
  isObservable?: boolean;
  
  // HOC decorators
  hocName?: string;
  hocOptions?: any;
}

/**
 * Decorator analysis result
 */
export interface DecoratorAnalysisResult {
  decorators: DecoratorElement[];
  
  // Statistics
  totalCount: number;
  byFramework: { [framework: string]: number };
  byType: { [type: string]: number };
  byClassification: { [classification: string]: number };
  
  // Framework detection
  detectedFrameworks: string[];
  primaryFramework?: string;       // Most common framework
  
  // Patterns
  commonPatterns: string[];        // Common decorator combinations
  unusualPatterns: string[];       // Unusual or potentially problematic patterns
  
  // Dependencies
  decoratorDependencies: Array<{
    decorator: string;
    dependsOn: string[];           // Other decorators this one typically works with
  }>;
}

/**
 * Decorator classification types
 */
export enum DecoratorClassification {
  HTTP = 'http',                   // @Get, @Post, etc.
  DEPENDENCY_INJECTION = 'dependency-injection', // @Injectable, @Inject
  VALIDATION = 'validation',       // @IsString, @Min, etc.
  ROUTING = 'routing',            // @Controller, @Route
  AUTHENTICATION = 'authentication', // @UseGuards, @Auth
  CACHING = 'caching',            // @Cache, @CacheKey
  LOGGING = 'logging',            // @Log, @Audit
  TRANSFORMATION = 'transformation', // @Transform, @Exclude
  METADATA = 'metadata',          // @ApiTags, @ApiOperation
  LIFECYCLE = 'lifecycle',        // @OnInit, @OnDestroy
  OTHER = 'other'                 // Generic or unknown decorators
}

/**
 * Decorator usage pattern
 */
export interface DecoratorPattern {
  name: string;                   // Pattern name
  decorators: string[];          // Decorators involved in this pattern
  description: string;           // What this pattern does
  framework?: string;            // Framework this pattern belongs to
  isCommon: boolean;             // Whether this is a common pattern
  examples: string[];            // Code examples
}

/**
 * Common NestJS decorator patterns
 */
export const NESTJS_DECORATOR_PATTERNS: DecoratorPattern[] = [
  {
    name: 'REST Controller',
    decorators: ['Controller', 'Get', 'Post', 'Put', 'Delete'],
    description: 'Standard REST API controller with CRUD operations',
    framework: 'nestjs',
    isCommon: true,
    examples: [
      '@Controller("users")\n@Get()\n@Post()'
    ]
  },
  {
    name: 'Guarded Endpoint',
    decorators: ['Controller', 'UseGuards', 'Get'],
    description: 'Protected endpoint with authentication/authorization',
    framework: 'nestjs',
    isCommon: true,
    examples: [
      '@Controller("admin")\n@UseGuards(AuthGuard)\n@Get()'
    ]
  },
  {
    name: 'Validated Input',
    decorators: ['Post', 'Body', 'IsString', 'IsEmail'],
    description: 'Endpoint with input validation',
    framework: 'nestjs',
    isCommon: true,
    examples: [
      '@Post()\ncreate(@Body() dto: CreateUserDto)'
    ]
  },
  {
    name: 'Injectable Service',
    decorators: ['Injectable'],
    description: 'Service that can be injected into other classes',
    framework: 'nestjs',
    isCommon: true,
    examples: [
      '@Injectable()\nexport class UserService'
    ]
  },
  {
    name: 'Module Definition',
    decorators: ['Module'],
    description: 'Application module with imports, providers, controllers',
    framework: 'nestjs',
    isCommon: true,
    examples: [
      '@Module({\n  imports: [],\n  providers: [],\n  controllers: []\n})'
    ]
  }
];

/**
 * Decorator validation rules
 */
export interface DecoratorValidationRule {
  decorator: string;
  framework: string;
  
  // Where this decorator can be applied
  validTargets: ('class' | 'method' | 'property' | 'parameter')[];
  
  // Required arguments
  requiredArguments?: string[];
  
  // Optional arguments
  optionalArguments?: string[];
  
  // Decorators that must be present together
  requiredWith?: string[];
  
  // Decorators that cannot be used together
  conflictsWith?: string[];
  
  // Validation function
  validate?: (decoratorInfo: DecoratorInfo) => string[]; // Returns error messages
}

/**
 * Export all types for easy importing
 */
export type {
  DecoratorElement,
  DecoratorInfo,
  NestJSDecoratorInfo,
  AngularDecoratorInfo,
  ReactDecoratorInfo,
  DecoratorAnalysisResult,
  DecoratorPattern,
  DecoratorValidationRule
};
