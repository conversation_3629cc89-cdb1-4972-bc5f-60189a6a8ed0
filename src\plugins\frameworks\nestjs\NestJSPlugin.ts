/**
 * NestJS Framework Plugin
 * 
 * Builds on TypeScript + Decorators plugins to provide NestJS-specific analysis.
 * Focuses on dependency injection, modules, controllers, and API structure.
 */

import { 
  AnalysisPlugin, 
  PluginMetadata, 
  PluginCapabilities, 
  PluginAnalysisContext, 
  PluginAnalysisResult 
} from '../../core/PluginSystem.js';
import { CodeElement, CodeElementType } from '../../types/CodeElement.js';
import { DecoratorElement, NestJSDecoratorInfo } from '../features/decorators/types/DecoratorTypes.js';
import { DecoratorsPlugin } from '../features/decorators/DecoratorsPlugin.js';
import { TypeScriptPlugin } from '../core/typescript/TypeScriptPlugin.js';
import { DIAnalyzer } from './analyzers/DIAnalyzer.js';
import { ModuleAnalyzer } from './analyzers/ModuleAnalyzer.js';
import { ControllerAnalyzer } from './analyzers/ControllerAnalyzer.js';
import { NestJSTypes } from './types/NestJSTypes.js';

export class NestJSPlugin implements AnalysisPlugin {
  metadata: PluginMetadata = {
    name: 'nestjs-framework',
    version: '1.0.0',
    description: 'NestJS framework analysis with DI, modules, and API structure',
    author: 'Cyzer Team',
    
    languages: ['typescript'],
    frameworks: ['nestjs'],
    fileExtensions: ['.ts'],
    
    capabilities: {
      syntaxAnalysis: true,
      semanticAnalysis: true,
      crossFileAnalysis: true,
      typeInference: true,
      dependencyTracking: true,
      callGraphGeneration: true,
      frameworkPatterns: true,     // ✅ NestJS-specific patterns
      decoratorAnalysis: true,     // ✅ Uses decorators plugin
      componentAnalysis: true,     // ✅ Controllers, services, modules
      incrementalAnalysis: false,
      largeCodebaseOptimized: true
    },
    
    priority: 300, // High priority for framework-specific analysis
    dependencies: ['typescript-enhanced', 'decorators-feature'], // Builds on other plugins
    
    requiresTypeScript: true,    // NestJS is TypeScript-based
    requiresNodeModules: true,   // Needs @nestjs/* packages
    memoryIntensive: false
  };
  
  // Dependent plugins
  private typeScriptPlugin: TypeScriptPlugin;
  private decoratorsPlugin: DecoratorsPlugin;
  
  // Specialized analyzers
  private diAnalyzer: DIAnalyzer;
  private moduleAnalyzer: ModuleAnalyzer;
  private controllerAnalyzer: ControllerAnalyzer;
  
  constructor() {
    this.typeScriptPlugin = new TypeScriptPlugin();
    this.decoratorsPlugin = new DecoratorsPlugin();
    this.diAnalyzer = new DIAnalyzer();
    this.moduleAnalyzer = new ModuleAnalyzer();
    this.controllerAnalyzer = new ControllerAnalyzer();
  }
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    // Initialize dependent plugins
    await this.typeScriptPlugin.initialize(projectRoot, options);
    await this.decoratorsPlugin.initialize(projectRoot, options);
    
    // Initialize analyzers
    await this.diAnalyzer.initialize(projectRoot);
    await this.moduleAnalyzer.initialize(projectRoot);
    await this.controllerAnalyzer.initialize(projectRoot);
  }
  
  async cleanup(): Promise<void> {
    await this.typeScriptPlugin.cleanup();
    await this.decoratorsPlugin.cleanup();
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    // Handle TypeScript files in NestJS projects
    return language === 'typescript' && 
           (framework === 'nestjs' || this.detectNestJSFile(filePath));
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    const startTime = Date.now();
    
    try {
      // 1. Get TypeScript analysis (syntax + semantics)
      const tsResult = await this.typeScriptPlugin.analyze(context);
      
      // 2. Get decorator analysis
      const decoratorResult = await this.decoratorsPlugin.analyze(context);
      
      // 3. Perform NestJS-specific analysis
      const nestjsAnalysis = await this.analyzeNestJSPatterns(
        context, 
        tsResult, 
        decoratorResult
      );
      
      // 4. Merge all results
      const mergedElements = this.mergeAnalysisResults(
        tsResult.elements,
        decoratorResult.elements,
        nestjsAnalysis.elements
      );
      
      // 5. Share NestJS-specific data
      const sharedData = new Map([
        ...(tsResult.sharedData || []),
        ...(decoratorResult.sharedData || []),
        ['nestjs-analysis', nestjsAnalysis],
        ['nestjs-metadata', this.extractNestJSMetadata(mergedElements)]
      ]);
      
      return {
        elements: mergedElements,
        errors: [
          ...(tsResult.errors || []),
          ...(decoratorResult.errors || []),
          ...(nestjsAnalysis.errors || [])
        ],
        warnings: nestjsAnalysis.warnings || [],
        metadata: {
          framework: 'nestjs',
          hasControllers: nestjsAnalysis.hasControllers,
          hasServices: nestjsAnalysis.hasServices,
          hasModules: nestjsAnalysis.hasModules,
          apiEndpoints: nestjsAnalysis.apiEndpoints?.length || 0,
          dependencyInjections: nestjsAnalysis.dependencyInjections?.length || 0
        },
        analysisTime: Date.now() - startTime,
        memoryUsed: tsResult.memoryUsed + decoratorResult.memoryUsed,
        confidence: 0.95, // High confidence for framework-specific analysis
        sharedData
      };
      
    } catch (error) {
      return {
        elements: [],
        errors: [`NestJS analysis failed: ${error}`],
        warnings: [],
        metadata: {},
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0,
      };
    }
  }
  
  /**
   * Detect if file is part of NestJS project
   */
  private detectNestJSFile(filePath: string): boolean {
    // Simple heuristics - would be more sophisticated in practice
    return filePath.includes('.controller.') ||
           filePath.includes('.service.') ||
           filePath.includes('.module.') ||
           filePath.includes('.guard.') ||
           filePath.includes('.interceptor.') ||
           filePath.includes('.pipe.');
  }
  
  /**
   * Perform NestJS-specific pattern analysis
   */
  private async analyzeNestJSPatterns(
    context: PluginAnalysisContext,
    tsResult: PluginAnalysisResult,
    decoratorResult: PluginAnalysisResult
  ): Promise<{
    elements: CodeElement[];
    errors: string[];
    warnings: string[];
    hasControllers: boolean;
    hasServices: boolean;
    hasModules: boolean;
    apiEndpoints?: NestJSTypes.APIEndpoint[];
    dependencyInjections?: NestJSTypes.DependencyInjection[];
  }> {
    
    const elements: CodeElement[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Extract NestJS decorators
    const nestjsDecorators = decoratorResult.elements.filter(
      (el): el is DecoratorElement => 
        el.framework === 'nestjs'
    );
    
    // Analyze controllers
    const controllerAnalysis = await this.controllerAnalyzer.analyze(
      context, 
      nestjsDecorators,
      tsResult.elements
    );
    elements.push(...controllerAnalysis.elements);
    errors.push(...controllerAnalysis.errors);
    
    // Analyze services and dependency injection
    const diAnalysis = await this.diAnalyzer.analyze(
      context,
      nestjsDecorators,
      tsResult.elements
    );
    elements.push(...diAnalysis.elements);
    errors.push(...diAnalysis.errors);
    
    // Analyze modules
    const moduleAnalysis = await this.moduleAnalyzer.analyze(
      context,
      nestjsDecorators,
      tsResult.elements
    );
    elements.push(...moduleAnalysis.elements);
    errors.push(...moduleAnalysis.errors);
    
    // Detect patterns and potential issues
    const patternAnalysis = this.analyzeNestJSPatterns_Internal(nestjsDecorators);
    warnings.push(...patternAnalysis.warnings);
    
    return {
      elements,
      errors,
      warnings,
      hasControllers: controllerAnalysis.hasControllers,
      hasServices: diAnalysis.hasServices,
      hasModules: moduleAnalysis.hasModules,
      apiEndpoints: controllerAnalysis.apiEndpoints,
      dependencyInjections: diAnalysis.dependencyInjections
    };
  }
  
  /**
   * Analyze NestJS patterns and detect issues
   */
  private analyzeNestJSPatterns_Internal(decorators: DecoratorElement[]): {
    warnings: string[];
  } {
    const warnings: string[] = [];
    
    // Check for common anti-patterns
    const controllers = decorators.filter(d => d.decoratorName === 'Controller');
    const injectables = decorators.filter(d => d.decoratorName === 'Injectable');
    
    // Warning: Controller without Injectable services
    if (controllers.length > 0 && injectables.length === 0) {
      warnings.push('Controllers found but no Injectable services - consider using services for business logic');
    }
    
    // Warning: Multiple HTTP method decorators on same method (would need more context)
    // This would require analyzing the AST structure more deeply
    
    return { warnings };
  }
  
  /**
   * Merge analysis results from different plugins
   */
  private mergeAnalysisResults(
    tsElements: CodeElement[],
    decoratorElements: CodeElement[],
    nestjsElements: CodeElement[]
  ): CodeElement[] {
    
    // Create a map for efficient lookup
    const elementMap = new Map<string, CodeElement>();
    
    // Add TypeScript elements as base
    tsElements.forEach(el => {
      const key = `${el.name}-${el.location.startLine}`;
      elementMap.set(key, el);
    });
    
    // Enhance with decorator information
    decoratorElements.forEach(decoratorEl => {
      const key = `${decoratorEl.name}-${decoratorEl.location.startLine}`;
      const existing = elementMap.get(key);
      
      if (existing) {
        // Merge decorator info into existing element
        elementMap.set(key, {
          ...existing,
          metadata: {
            ...existing.metadata,
            decorators: [...(existing.metadata?.decorators || []), decoratorEl],
            hasDecorators: true,
            framework: decoratorEl.framework
          }
        });
      } else {
        // Add as new element
        elementMap.set(key, decoratorEl);
      }
    });
    
    // Add NestJS-specific elements
    nestjsElements.forEach(nestjsEl => {
      const key = `${nestjsEl.name}-${nestjsEl.location.startLine}`;
      elementMap.set(key, nestjsEl);
    });
    
    return Array.from(elementMap.values());
  }
  
  /**
   * Extract NestJS-specific metadata
   */
  private extractNestJSMetadata(elements: CodeElement[]): any {
    const controllers = elements.filter(e => 
      e.type === CodeElementType.NestJSController ||
      e.metadata?.decorators?.some((d: any) => d.decoratorName === 'Controller')
    );
    
    const services = elements.filter(e => 
      e.type === CodeElementType.NestJSService ||
      e.metadata?.decorators?.some((d: any) => d.decoratorName === 'Injectable')
    );
    
    const modules = elements.filter(e => 
      e.type === CodeElementType.NestJSModule ||
      e.metadata?.decorators?.some((d: any) => d.decoratorName === 'Module')
    );
    
    const endpoints = elements.filter(e => 
      e.type === CodeElementType.NestJSEndpoint ||
      e.metadata?.decorators?.some((d: any) => 
        ['Get', 'Post', 'Put', 'Delete', 'Patch'].includes(d.decoratorName)
      )
    );
    
    return {
      architecture: {
        controllers: controllers.length,
        services: services.length,
        modules: modules.length,
        endpoints: endpoints.length
      },
      patterns: {
        usesGuards: elements.some(e => 
          e.metadata?.decorators?.some((d: any) => d.decoratorName === 'UseGuards')
        ),
        usesInterceptors: elements.some(e => 
          e.metadata?.decorators?.some((d: any) => d.decoratorName === 'UseInterceptors')
        ),
        usesPipes: elements.some(e => 
          e.metadata?.decorators?.some((d: any) => d.decoratorName === 'UsePipes')
        ),
        hasValidation: elements.some(e => 
          e.metadata?.decorators?.some((d: any) => 
            d.decoratorName?.startsWith('Is') || d.decoratorName === 'Body'
          )
        )
      },
      complexity: this.calculateNestJSComplexity(elements)
    };
  }
  
  /**
   * Calculate NestJS-specific complexity score
   */
  private calculateNestJSComplexity(elements: CodeElement[]): 'low' | 'medium' | 'high' {
    const totalElements = elements.length;
    const decoratorCount = elements.filter(e => e.metadata?.hasDecorators).length;
    const complexityScore = totalElements + (decoratorCount * 2);
    
    if (complexityScore < 20) return 'low';
    if (complexityScore < 100) return 'medium';
    return 'high';
  }
}
