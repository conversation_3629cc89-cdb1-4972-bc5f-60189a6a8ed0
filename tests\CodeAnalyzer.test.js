import path from 'path';
import fs from 'fs/promises';
describe('CodeAnalyzer', () => {
    let analyzer;
    const testProjectRoot = path.resolve(__dirname, 'fixtures', 'sample-project');
    beforeAll(async () => {
        await fs.mkdir(path.join(testProjectRoot, 'src'), { recursive: true });
        await fs.writeFile(path.join(testProjectRoot, 'src', 'moduleA.js'), `
            import { utilFunc } from './utils/helper.js';
            import * as D from './data.ts';
            import defaultExport from './moduleB.ts';

            export function funcA() { console.log('FuncA'); utilFunc(); }
            export const varA = 10;

            funcA();
            defaultExport();
            console.log(D.dataValue);
        `);
        // ... rest of test setup ...
    });
    afterAll(async () => {
        await fs.rm(testProjectRoot, { recursive: true, force: true });
    });
    // ... test cases ...
});
//# sourceMappingURL=CodeAnalyzer.test.js.map