import Parser, { SyntaxNode, QueryCapture } from 'tree-sitter'; // Explicit imports

import fs from 'fs/promises';
import path from 'path';
import {
    CodeElement, CodeElementType, CodeLocation, FunctionElement, ClassElement, TypeElement, ImportElement, ExportElement, FileAnalysisResult
} from '../types/CodeElement.js';

// Helper to find a specific child node by type
function findChildByType(node: SyntaxNode | null, type: string): SyntaxNode | undefined {
    if (!node) return undefined;
    return node.children.find(child => child.type === type);
}
// Helper to find multiple children by type
function findChildrenByType(node: SyntaxNode | null, type: string): SyntaxNode[] {
    if (!node) return [];
    return node.children.filter(child => child.type === type);
}
// Helper to get node text safely
function getNodeText(node: SyntaxNode | null | undefined, code: string): string {
    return node ? code.substring(node.startIndex, node.endIndex) : '';
}
// Helper to check for modifiers (like async, export, abstract)
function hasModifier(node: SyntaxNode | null, kind: string): boolean {
    return node?.children.some(child => child.type === 'modifier' && child.text === kind) ?? false;
}
function hasKeyword(node: SyntaxNode | null, keyword: string): boolean {
     if (!node) return false;
     // Check direct children or specific structure based on language grammar
     return node.children.some(child => child.text === keyword); // Simplified check
}

export class TreeSitterParser {
    private parser: Parser;
    private languages: Map<string, any> = new Map(); // Use 'any' for language objects to avoid type errors
    private queries: Map<string, Map<string, Parser.Query>> = new Map(); // lang -> queryName -> Query

    constructor() {
        this.parser = new Parser();
    }

    async initQueries(queryDir: string = 'queries') {
        // Dynamically load languages to handle CJS/ESM interop
        if (this.languages.size === 0) {
            try {
                const jsLangModule = await import('tree-sitter-javascript');
                this.languages.set('javascript', jsLangModule.default);
                const tsLangModule = await import('tree-sitter-typescript');
                this.languages.set('typescript', tsLangModule.default);
                console.log('Successfully loaded tree-sitter languages.');
            } catch (error) {
                console.error('Failed to dynamically load tree-sitter languages:', error);
                return; // Stop if languages can't be loaded
            }
        }

        // Try to read from provided queryDir first, fallback to default relative path
        let effectiveQueryDir = queryDir;
        try {
            await fs.access(effectiveQueryDir);
        } catch {
             console.warn(`Query directory "${queryDir}" not found or accessible, falling back to default relative path 'queries'.`);
             effectiveQueryDir = 'queries'; // Default relative path
             try {
                 await fs.access(effectiveQueryDir); // Check fallback
             } catch {
                 console.error(`Default query directory "${effectiveQueryDir}" also not found or accessible. No queries will be loaded.`);
                 return; // Stop if no queries can be loaded
             }
        }

        const languages = [
            { name: 'javascript', lang: JavaScript },
            { name: 'typescript', lang: TypeScript }
        ];
        for (const { name, lang } of languages) {
            const queriesForLang = new Map<string, Parser.Query>();
            const queryFile = path.join(effectiveQueryDir, `${name}-tags.scm`);
            try {
                const queryString = await fs.readFile(queryFile, 'utf8');
                const query = new Parser.Query(lang, queryString);
                queriesForLang.set('tags', query);
            } catch (e) {
                console.warn(`Failed to read or compile query file ${queryFile}:`, e);
            }
            this.queries.set(name, queriesForLang);
        }
    }

    getLanguage(filePath: string): any | undefined {
        const extension = path.extname(filePath).toLowerCase();
        switch (extension) {
            case '.js':
            case '.jsx':
            case '.mjs':
            case '.cjs':
                return this.languages.get('javascript');
            case '.ts':
            case '.tsx':
            case '.mts':
            case '.cts':
                return this.languages.get('typescript');
            default:
                // console.log(`Unsupported extension: ${extension} for file ${filePath}`); // More verbose logging
                return undefined;
        }
    }

    parse(code: string, filePath: string): FileAnalysisResult {
        const language = this.getLanguage(filePath);
        const langName = language ? this.getLangName(language) : undefined;

        if (!language || !langName) {
            return { filePath, elements: [], errors: [`Unsupported language extension: ${path.extname(filePath)}`] };
        }

        this.parser.setLanguage(language);
        let tree: Parser.Tree;
        try {
            tree = this.parser.parse(code);
        } catch (parseError: any) {
             return { filePath, elements: [], errors: [`Failed to parse file: ${parseError.message}`], language: langName };
        }

        const elements: CodeElement[] = [];
        const errors: string[] = [];
        const query = this.queries.get(langName)?.get('tags');

        if (query) {
            try {
                const matches = query.matches(tree.rootNode);
                for (const match of matches) {
                    elements.push(...this.processCaptures(match.captures, code, filePath, language));
                }
            } catch (e: any) {
                errors.push(`Error executing query: ${e.message}`);
                console.error(`Query error processing ${filePath}:`, e);
            }
        } else {
            errors.push(`No 'tags' query found for language: ${langName}`);
            // Consider falling back to a basic AST walk here if desired
        }

        return { filePath, elements, errors, language: langName };
    }

    private getLangName(language: any): string | undefined {
        for (const [name, lang] of this.languages.entries()) {
            if (lang === language) {
                return name;
            }
        }
        return undefined;
    }

     private getNodeLocation(node: SyntaxNode | null): CodeLocation {
        if (!node) {
            // Return a default or invalid location
            return { startLine: 0, startCol: 0, endLine: 0, endCol: 0, startPos: -1, endPos: -1 };
        }
        return {
            startLine: node.startPosition.row + 1, // Tree-sitter is 0-based
            startCol: node.startPosition.column + 1,
            endLine: node.endPosition.row + 1,
            endCol: node.endPosition.column + 1,
            startPos: node.startIndex,
            endPos: node.endIndex,
        };
    }

    // Find the most relevant parent node representing the entire definition
    private findDefinitionNode(captureNode: SyntaxNode, captureName: string): SyntaxNode {
        // Heuristic: walk up until we find a node type typically representing a declaration/definition
         const definitionTypes = [
            'function_declaration', 'generator_function_declaration', 'method_definition',
            'class_declaration', 'lexical_declaration', 'variable_declaration',
            'interface_declaration', 'type_alias_declaration', 'enum_declaration',
            'import_statement', 'export_statement', 'pair' // For object methods
        ];
        let current: SyntaxNode | null = captureNode;
        while (current) {
            // If the capture is the definition itself (e.g., @definition.method)
             if (captureName.startsWith('definition.') && current === captureNode) {
                 return current;
             }
            // If the capture is the name, check the parent first
            if (captureName.startsWith('name.') && current.parent && definitionTypes.includes(current.parent.type)) {
                 return current.parent;
             }
            // Generic fallback if name capture is used without specific definition capture
             if (definitionTypes.includes(current.type)) {
                return current;
            }
            current = current.parent;
        }
        // Fallback to the captured node itself if no suitable parent is found
        return captureNode;
    }


    private processCaptures(captures: QueryCapture[], code: string, filePath: string, language: any): CodeElement[] {
        const elementsMap: Map<number, CodeElement> = new Map(); // Use Map to update elements if captured multiple times
        const processedNodeIds = new Set<number>(); // Avoid processing the same *definition* node multiple times

        for (const capture of captures) {
            const { name: captureName, node } = capture;

            // --- Handle @name captures (from modern queries) ---
            if (captureName === 'name') {
                const definitionNode = this.findDefinitionNode(node, captureName);
                if (processedNodeIds.has(definitionNode.id)) {
                    continue;
                }
                // Use the node's text as the name
                const nameText = getNodeText(node, code);
                // Create a base element with the definition node and name
                let element = this.createBaseElement(definitionNode, code, filePath, nameText);
                if (element) {
                    this.refineElement(element, capture, definitionNode, code);
                    if (element.type !== CodeElementType.Unknown) {
                        elementsMap.set(definitionNode.id, element);
                    }
                    processedNodeIds.add(definitionNode.id);
                }
                continue;
            }

            // --- Existing logic for other captures ---
            const definitionNode = this.findDefinitionNode(node, captureName);
            if (processedNodeIds.has(definitionNode.id)) {
                continue;
            }
            let element = this.createBaseElement(definitionNode, code, filePath);
            let isNewElement = false;
            if (!element) {
                continue;
            }
            if (!elementsMap.has(definitionNode.id)) {
                isNewElement = true;
            } else {
                element = elementsMap.get(definitionNode.id)!;
            }

            // Refine element based on capture type
            this.refineElement(element, capture, definitionNode, code);

            // Add to map if valid element was created or refined
            if (element.type !== CodeElementType.Unknown) {
                if (isNewElement) {
                    elementsMap.set(definitionNode.id, element);
                }
                processedNodeIds.add(definitionNode.id);
            }
        }

        return Array.from(elementsMap.values());
    }

    // Update createBaseElement to accept optional nameText
    private createBaseElement(definitionNode: SyntaxNode, code: string, filePath: string, nameText?: string): CodeElement | null {
        // --- Improve Logging for import/export statements ---
        if (definitionNode.type === 'import_statement' || definitionNode.type === 'export_statement') {
            console.error('[DEBUG] createBaseElement: Handling node type:', definitionNode.type, 'at', filePath, 'Raw node:', JSON.stringify(definitionNode.toString()), '\nCode:', getNodeText(definitionNode, code));
        }

        // Special handling for import_statement: always create an ImportElement, even without a name
        if (definitionNode.type === 'import_statement') {
            return {
                type: CodeElementType.Import,
                name: 'import', // Placeholder name
                location: this.getNodeLocation(definitionNode),
                filePath: filePath,
                isExported: false,
                importedNames: [],
                source: '',
                isDefault: false,
                isNamespace: false
            } as ImportElement;
        }

        // If nameText is provided, use it directly
        if (nameText) {
            // Guess type by node type
            let type: CodeElementType = CodeElementType.Unknown;
            switch (definitionNode.type) {
                case 'function_declaration':
                case 'generator_function_declaration':
                    type = CodeElementType.Function;
                    break;
                case 'method_definition':
                    type = CodeElementType.Method;
                    break;
                case 'class':
                case 'class_declaration':
                    type = CodeElementType.Class;
                    break;
                case 'variable_declaration':
                case 'lexical_declaration':
                    type = CodeElementType.Variable;
                    break;
                default:
                    type = CodeElementType.Unknown;
            }
            return {
                type,
                name: nameText,
                location: this.getNodeLocation(definitionNode),
                filePath: filePath,
                isExported: definitionNode.parent?.type === 'export_statement',
                isAsync: false,
                isGenerator: false,
                isArrow: false
            } as CodeElement;
        }
        // Fallback to original logic
        const nameNode = findChildByType(definitionNode, 'identifier')
                      || findChildByType(definitionNode, 'property_identifier')
                      || findChildByType(definitionNode, 'type_identifier');
        if (!nameNode && (definitionNode.type === 'lexical_declaration' || definitionNode.type === 'variable_declaration')) {
            // Try to find variable declarator name
            const declarator = findChildByType(definitionNode, 'variable_declarator');
            if (declarator) {
                const varName = findChildByType(declarator, 'identifier');
                if (varName) {
                    return {
                        type: CodeElementType.Variable,
                        name: getNodeText(varName, code),
                        location: this.getNodeLocation(definitionNode),
                        filePath: filePath,
                        isExported: definitionNode.parent?.type === 'export_statement',
                        isAsync: false,
                        isGenerator: false,
                        isArrow: false
                    } as CodeElement;
                }
            }
        }
        if (!nameNode) {
            console.warn(`[DEBUG] Skipping element: Could not determine name for node type ${definitionNode.type} at ${filePath}:${this.getNodeLocation(definitionNode).startLine}. Children:`, definitionNode.namedChildren.map(child => child.type));
            return null;
        }
        return {
            type: CodeElementType.Unknown,
            name: getNodeText(nameNode, code),
            location: this.getNodeLocation(definitionNode),
            filePath: filePath,
            isExported: definitionNode.parent?.type === 'export_statement',
            isAsync: false,
            isGenerator: false,
            isArrow: false
        } as CodeElement;
    }

    private refineElement(element: CodeElement, capture: QueryCapture, definitionNode: SyntaxNode, code: string) {
        const { name: captureName, node } = capture;

        // --- Refine based on definition captures ---
        if (captureName.startsWith('definition.')) {
            const typeStr = captureName.split('.').pop();
            switch (typeStr) {
                case 'function':
                    element.type = CodeElementType.Function;
                    this.extractFunctionDetails(element as FunctionElement, definitionNode, code);
                    break;
                case 'method':
                    element.type = CodeElementType.Method;
                     this.extractFunctionDetails(element as FunctionElement, definitionNode, code); // Methods are functions too
                    break;
                case 'class':
                    element.type = CodeElementType.Class;
                     this.extractClassDetails(element as ClassElement, definitionNode, code);
                    break;
                 case 'interface': element.type = CodeElementType.Interface; break;
                 case 'type': element.type = CodeElementType.Type; break;
                 case 'enum': element.type = CodeElementType.Enum; break;
                 case 'variable': element.type = CodeElementType.Variable; break;
                 case 'module': element.type = CodeElementType.Module; break;
                 case 'import':
                    element.type = CodeElementType.Import;
                    const importElement = element as ImportElement;
                    importElement.source = '';
                    importElement.importedNames = [];
                    importElement.isDefault = false;
                    importElement.isNamespace = false;
                    const sourceNode = definitionNode.childForFieldName('source');
                    if (sourceNode) {
                        importElement.source = getNodeText(sourceNode, code).slice(1, -1); // Remove quotes
                    }
                    const clauseNode = definitionNode.childForFieldName('import_clause');
                    if (clauseNode) {
                        clauseNode.namedChildren.forEach(child => {
                            if (child.type === 'identifier') {
                                // Default import: import foo from 'mod'
                                importElement.importedNames.push({ name: getNodeText(child, code) });
                                importElement.isDefault = true;
                            } else if (child.type === 'namespace_import') {
                                // Namespace import: import * as ns from 'mod'
                                const ns = child.childForFieldName('name') || child.namedChildren[0];
                                importElement.importedNames.push({ name: ns ? getNodeText(ns, code) : '*' });
                                importElement.isNamespace = true;
                            } else if (child.type === 'named_imports') {
                                // Defensive: walk all descendants, not just namedChildren
                                const specifiers = child.descendantsOfType
                                    ? child.descendantsOfType('import_specifier')
                                    : child.namedChildren.filter(n => n.type === 'import_specifier');
                                specifiers.forEach(spec => {
                                    const identifiers = spec.namedChildren.filter(n => n.type === 'identifier');
                                    const name = identifiers[0] ? getNodeText(identifiers[0], code) : 'unknown';
                                    const alias = identifiers[1] ? getNodeText(identifiers[1], code) : undefined;
                                    importElement.importedNames.push({ name, alias });
                                    // Debug log
                                    console.error('[DEBUG] Defensive import_specifier:', { name, alias, spec: spec.toString() });
                                });
                            }
                        });
                    }
                    break;
            }
        }

        // --- Refine based on specific captures (imports/exports) ---
         if (captureName.startsWith('import.')) {
             if (element.type === CodeElementType.Import) {
                 const importElement = element as ImportElement;
                 const importType = captureName.split('.').pop();
                 switch(importType) {
                     case 'source': importElement.source = getNodeText(node, code).slice(1,-1); break; // Re-assign if needed
                     case 'named': importElement.importedNames.push({ name: getNodeText(node, code) }); break;
                     case 'default':
                         importElement.importedNames.push({ name: getNodeText(node, code) });
                         importElement.isDefault = true;
                         break;
                     case 'namespace':
                         importElement.importedNames.push({ name: getNodeText(node, code) });
                         importElement.isNamespace = true;
                         break;
                 }
             }
        }

        if (captureName.startsWith('export.')) {
             if (element.type !== CodeElementType.Unknown && element.type !== CodeElementType.Import) {
                 element.isExported = true; // Mark the defined element as exported
             }

             // Handle export lists and defaults separately if needed
             if (element.type === CodeElementType.Unknown && definitionNode.type === 'export_statement') {
                element.type = CodeElementType.Export; // Mark as an export statement itself
                const exportElement = element as ExportElement;
                exportElement.isExported = true; // Exports are always exported :)
                const exportType = captureName.split('.').pop();
                 switch(exportType) {
                     case 'named':
                         exportElement.exportedName = getNodeText(node, code);
                         exportElement.source = getNodeText(findChildByType(definitionNode,'string'), code).slice(1,-1) || undefined;
                         break;
                     case 'default':
                         exportElement.exportedName = 'default';
                         exportElement.isDefault = true;
                         // Try to find the type/name of the exported value
                         // This might require looking at the `value:` node captured by the query
                         break;
                     case 'source': // Captured on the list itself
                          exportElement.source = getNodeText(node, code).slice(1, -1);
                         break;

                 }

             }


        }

        // --- Refine based on modifier captures (e.g., @doc) ---
        if (captureName === 'doc') {
            // Add docstring to the element if needed
            // element.docstring = getNodeText(node, code);
        }

        // --- Final check for export status (might be missed by specific captures) ---
         if (element.type !== CodeElementType.Import && definitionNode.parent?.type === 'export_statement') {
            element.isExported = true;
        }


    }

     private extractFunctionDetails(funcElement: FunctionElement, definitionNode: SyntaxNode, code: string) {
        funcElement.isAsync = hasModifier(definitionNode, 'async') || hasKeyword(definitionNode,'async');
        // Generator status might require checking for '*' token depending on query/grammar
        funcElement.isGenerator = definitionNode.type.includes('generator'); // Heuristic

        // Extract parameters
        const paramsNode = findChildByType(definitionNode, 'formal_parameters') // TS/JS functions
                        || findChildByType(definitionNode, 'parameter_list'); // Arrow functions? Check grammar

        if (paramsNode) {
            funcElement.parameters = paramsNode.children
                .filter(p => ['required_parameter', 'optional_parameter', 'rest_parameter', 'identifier', 'object_pattern', 'array_pattern'].includes(p.type))
                .map(p => {
                    const nameNode = findChildByType(p, 'identifier') || p; // Basic name finding
                    const typeNode = findChildByType(p, 'type_annotation');
                    return {
                        name: getNodeText(nameNode, code),
                        type: typeNode ? getNodeText(typeNode, code).substring(1).trim() : undefined // Remove leading ':'
                    };
                });
        }

        // Extract return type (TS specific usually)
        const returnTypeNode = findChildByType(definitionNode, 'type_annotation') // Direct annotation
                            || findChildByType(definitionNode, 'return_type'); // Another possible node type

         if (returnTypeNode) {
            // Need to be careful not to grab parameter types; check node structure
            // This logic assumes return type annotation is a direct child or via 'return_type' node
            const actualTypeNode = findChildByType(returnTypeNode, 'type_identifier')
                                || findChildByType(returnTypeNode, 'predefined_type')
                                || returnTypeNode; // Fallback if structure is simpler
             // Check if it's actually the return type node, not nested within parameters
             if (returnTypeNode.startIndex > (paramsNode?.endIndex ?? -1)) {
                 funcElement.returnType = getNodeText(actualTypeNode, code);
             }

        }

         // Arrow function check (more reliable)
         funcElement.isArrow = definitionNode.type.includes('arrow_function') || definitionNode.descendantsOfType('arrow_function').length > 0;


    }

    private extractClassDetails(classElement: ClassElement, definitionNode: SyntaxNode, code: string) {
        classElement.isAbstract = hasModifier(definitionNode, 'abstract');

        const heritageNode = findChildByType(definitionNode, 'class_heritage');
        if (heritageNode) {
            const extendsClause = findChildByType(heritageNode, 'extends_clause');
            if (extendsClause && extendsClause.firstNamedChild) {
                classElement.extendsClass = getNodeText(extendsClause.firstNamedChild, code);
            }
             const implementsClause = findChildByType(heritageNode, 'implements_clause');
             if (implementsClause) {
                 classElement.implementsInterfaces = findChildrenByType(implementsClause, 'type_identifier') // Adjust based on actual grammar node for implements list
                     .map(n => getNodeText(n, code));
             }
        }


        const classBodyNode = findChildByType(definitionNode, 'class_body');
        if (classBodyNode) {
            classElement.methods = []; // Reset/initialize
            classElement.properties = [];

            for (const member of classBodyNode.children) {
                if (member.type === 'method_definition') {
                    const methodElement = this.createBaseElement(member, code, classElement.filePath) as FunctionElement | null;
                    if (methodElement) {
                         methodElement.type = CodeElementType.Method; // Ensure type is method
                         this.extractFunctionDetails(methodElement, member, code);
                         // Determine visibility (public, private, protected) - TS specific
                         const visibility = member.children.find(c => c.type === 'accessibility_modifier')?.text;
                         // Store visibility if needed (add property to FunctionElement or handle differently)
                         classElement.methods.push({
                             ...methodElement,
                             visibility: visibility as Visibility // Cast to Visibility type
                         });
                    }
                } else if (member.type === 'property_declaration' || member.type === 'public_field_definition') { // Check JS/TS grammar variations
                     const nameNode = findChildByType(member, 'property_identifier') || findChildByType(member, 'identifier');
                     const typeNode = findChildByType(member, 'type_annotation');
                     const visibility = member.children.find(c => c.type === 'accessibility_modifier')?.text;

                     if (nameNode) {
                         classElement.properties.push({
                             type: CodeElementType.Property,
                             name: getNodeText(nameNode, code),
                             propertyType: typeNode ? getNodeText(typeNode, code).substring(1).trim() : undefined,
                             visibility: visibility as Visibility, // Cast to Visibility type
                             location: {
                                startLine: nameNode?.startPosition.row ?? 0,
                                startCol: nameNode?.startPosition.column ?? 0,
                                endLine: nameNode?.endPosition.row ?? 0,
                                endCol: nameNode?.endPosition.column ?? 0,
                                startPos: nameNode?.startIndex ?? 0,
                                endPos: nameNode?.endIndex ?? 0
                            },
                            filePath: classElement.filePath
                         });
                     }
                }
                 // Add handling for constructors, getters, setters if needed
            }
        }

    }

}