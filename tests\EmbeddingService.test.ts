import { EmbeddingService } from '../src/services/EmbeddingService.js';
import { CodeElement, CodeElementType } from '../src/types/CodeElement.js';

describe('EmbeddingService', () => {
    let embeddingService: EmbeddingService;

    beforeEach(() => {
        embeddingService = new EmbeddingService();
    });

    describe('Initialization', () => {
        it('should initialize without throwing when API key is missing', () => {
            expect(() => new EmbeddingService()).not.toThrow();
        });

        it('should warn when API key is missing', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            new EmbeddingService();
            expect(consoleSpy).toHaveBeenCalledWith(
                'GEMINI_API_KEY not found in .env file. EmbeddingService will not be able to generate embeddings.'
            );
            consoleSpy.mockRestore();
        });
    });

    describe('generateEmbeddings', () => {
        it('should return empty array when API key is not configured', async () => {
            const mockElements: CodeElement[] = [
                {
                    name: 'testFunction',
                    type: CodeElementType.Function,
                    filePath: 'test.js',
                    location: { startLine: 1, endLine: 3, startCol: 0, endCol: 50, startPos: 0, endPos: 50 },
                    isExported: false,
                    fullText: 'function testFunction() { return 42; }'
                }
            ];

            const result = await embeddingService.generateEmbeddings(mockElements);
            expect(result).toEqual([]);
        });

        it('should skip elements without fullText', async () => {
            const mockElements: CodeElement[] = [
                {
                    name: 'testFunction',
                    type: CodeElementType.Function,
                    filePath: 'test.js',
                    location: { startLine: 1, endLine: 3, startCol: 0, endCol: 50, startPos: 0, endPos: 50 },
                    isExported: false
                    // No fullText property
                }
            ];

            const result = await embeddingService.generateEmbeddings(mockElements);
            expect(result).toEqual([]);
        });

        it('should skip elements with empty fullText', async () => {
            const mockElements: CodeElement[] = [
                {
                    name: 'testFunction',
                    type: CodeElementType.Function,
                    filePath: 'test.js',
                    location: { startLine: 1, endLine: 3, startCol: 0, endCol: 50, startPos: 0, endPos: 50 },
                    isExported: false,
                    fullText: '   ' // Only whitespace
                }
            ];

            const result = await embeddingService.generateEmbeddings(mockElements);
            expect(result).toEqual([]);
        });

        // Mock test for when API key is available
        it('should process elements with valid fullText when API key is available', async () => {
            // This test would require mocking the Google AI client
            // For now, we'll test the structure and error handling
            const mockElements: CodeElement[] = [
                {
                    name: 'testFunction',
                    type: CodeElementType.Function,
                    filePath: 'test.js',
                    location: { startLine: 1, endLine: 3, startCol: 0, endCol: 50, startPos: 0, endPos: 50 },
                    isExported: false,
                    fullText: 'function testFunction() { return 42; }'
                }
            ];

            // Since we don't have API key, this will return empty array
            const result = await embeddingService.generateEmbeddings(mockElements);
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('embedQuery', () => {
        it('should return null when API key is not configured', async () => {
            const result = await embeddingService.embedQuery('test query');
            expect(result).toBeNull();
        });

        it('should return null for empty query text', async () => {
            const result = await embeddingService.embedQuery('');
            expect(result).toBeNull();
        });

        it('should return null for whitespace-only query text', async () => {
            const result = await embeddingService.embedQuery('   ');
            expect(result).toBeNull();
        });

        it('should log error when API key is missing', async () => {
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            await embeddingService.embedQuery('test query');
            expect(consoleSpy).toHaveBeenCalledWith(
                'Cannot embed query: GEMINI_API_KEY is not configured.'
            );
            consoleSpy.mockRestore();
        });

        it('should log warning for empty query', async () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            await embeddingService.embedQuery('');
            expect(consoleSpy).toHaveBeenCalledWith(
                'Cannot embed empty query text.'
            );
            consoleSpy.mockRestore();
        });
    });

    describe('Model Configuration', () => {
        it('should use the latest embedding model', () => {
            // This is more of a documentation test to ensure we're using the right model
            // The actual model name is checked in the implementation
            expect(true).toBe(true); // Placeholder - model is configured in the service
        });
    });
});
