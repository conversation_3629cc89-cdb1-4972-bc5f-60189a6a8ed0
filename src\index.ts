#!/usr/bin/env node

export * from './types/CodeElement.js';
export * from './parsers/TreeSitterParser.js';
export * from './core/CodeAnalyzer.js';

import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { CodeAnalyzer } from './core/CodeAnalyzer.js';
import { AnalysisOptions, CodeElementType, MetadataFilters, SearchResult } from './types/CodeElement.js';
import path from 'path';

// Store the analyzer instance globally for simplicity in this version.
// For a more robust CLI, consider managing state or persisted indexes differently.
let lastIndexedAnalyzer: CodeAnalyzer | null = null;
let lastIndexedPath: string | null = null;

async function getAnalyzer(projectPath: string, forceReanalyze: boolean = false): Promise<CodeAnalyzer> {
    const absoluteProjectPath = path.resolve(projectPath);
    if (lastIndexedAnalyzer && lastIndexedPath === absoluteProjectPath && !forceReanalyze) {
        console.log(`Using cached analyzer for project: ${absoluteProjectPath}`);
        return lastIndexedAnalyzer;
    }
    console.log(`Initializing CodeAnalyzer for project: ${absoluteProjectPath}`);
    const analyzer = new CodeAnalyzer(absoluteProjectPath);
    await analyzer.initialize();
    lastIndexedAnalyzer = analyzer;
    lastIndexedPath = absoluteProjectPath;
    return analyzer;
}

yargs(hideBin(process.argv))
    .command(
        'index <projectPath>',
        'Analyze a project and index its code elements for semantic search.',
        (yargs) => {
            return yargs
                .positional('projectPath', {
                    describe: 'Path to the project directory to analyze and index.',
                    type: 'string',
                    demandOption: true,
                })
                .option('include-element-content', {
                    alias: 'c',
                    type: 'boolean',
                    default: true,
                    describe: 'Include full text of code elements in the analysis (required for indexing).'
                })
                .option('target-element-types', {
                    alias: 't',
                    type: 'array',
                    choices: Object.values(CodeElementType),
                    describe: 'Specific code element types to target for analysis and indexing.'
                })
                .option('output-json', {
                    alias: 'j',
                    type: 'string',
                    describe: 'Output analysis results to JSON file instead of console.'
                });
        },
        async (argv) => {
            console.log(`Starting indexing for project at: ${argv.projectPath}`);
            const analyzer = await getAnalyzer(argv.projectPath, true); // Force re-analyze for index command

            const options: AnalysisOptions = {
                includeElementContent: argv.includeElementContent,
                targetElementTypes: argv.targetElementTypes as CodeElementType[] | undefined,
                indexForSearch: true, // Key option for indexing
            };

            try {
                const results = await analyzer.analyzeProject(options);

                if (argv.outputJson) {
                    // Output to JSON file
                    const fs = await import('fs/promises');
                    const outputData = {
                        projectPath: argv.projectPath,
                        timestamp: new Date().toISOString(),
                        results: results
                    };
                    await fs.writeFile(argv.outputJson, JSON.stringify(outputData, null, 2));
                    console.log(`Analysis results saved to: ${argv.outputJson}`);
                } else {
                    // Console output
                    console.log(`Indexing complete. Analyzed ${results.files.length} files.`);
                    if (results.summary) {
                        console.log(`Summary: ${results.summary.totalFilesAnalyzed} files, ${results.summary.totalElements} elements found.`);
                    }
                }
                console.log(`Project '${argv.projectPath}' indexed. You can now use the 'search' command.`);
            } catch (error) {
                console.error('Error during indexing:', error);
                process.exit(1);
            }
        }
    )
    .command(
        'search <query>',
        'Perform a semantic search for a query string against an indexed project.',
        (yargs) => {
            return yargs
                .positional('query', {
                    describe: 'The query string to search for.',
                    type: 'string',
                    demandOption: true,
                })
                .option('project-path', {
                    alias: 'p',
                    type: 'string',
                    describe: 'Path to the project directory. If not provided, uses the last indexed project. If provided, it will re-index this project before searching.',
                })
                .option('top-k', {
                    alias: 'k',
                    type: 'number',
                    default: 5,
                    describe: 'Number of top results to return.'
                })
                .option('filter-file-path', {
                    type: 'string',
                    describe: 'Filter search results by file path (exact match).'
                })
                .option('filter-element-type', {
                    type: 'string',
                    choices: Object.values(CodeElementType),
                    describe: 'Filter search results by code element type.'
                })
                .option('min-similarity', {
                    type: 'number',
                    describe: 'Minimum similarity score for results (0.0 to 1.0).'
                });
        },
        async (argv) => {
            let analyzer: CodeAnalyzer;
            if (argv.projectPath) {
                console.log(`Project path provided: ${argv.projectPath}. Ensuring it is indexed...`);
                analyzer = await getAnalyzer(argv.projectPath, true); // Force re-analyze and index if path given
                const options: AnalysisOptions = { includeElementContent: true, indexForSearch: true };
                await analyzer.analyzeProject(options);
                console.log(`Project '${argv.projectPath}' re-indexed.`);
            } else if (lastIndexedAnalyzer) {
                analyzer = lastIndexedAnalyzer;
                console.log(`Searching in last indexed project: ${lastIndexedPath}`);
            } else {
                console.error('No project has been indexed yet, and no project-path provided. Please use the `index` command first or provide a --project-path.');
                process.exit(1);
                return; // For type safety, though process.exit will stop it
            }

            const filters: MetadataFilters = {};
            if (argv.filterFilePath) filters.filePath = argv.filterFilePath;
            if (argv.filterElementType) filters.elementType = argv.filterElementType as CodeElementType;
            if (argv.minSimilarity !== undefined) filters.minSimilarity = argv.minSimilarity;

            try {
                console.log(`Searching for: "${argv.query}" with topK=${argv.topK}`);
                if (Object.keys(filters).length > 0) {
                    console.log('Applying filters:', filters);
                }
                const searchResults: SearchResult[] = await analyzer.semanticSearch(argv.query, argv.topK, filters);
                if (searchResults.length > 0) {
                    console.log(`Found ${searchResults.length} results:`);
                    searchResults.forEach((result, i) => {
                        console.log(`  ${i + 1}. ${result.chunk.name} (${result.chunk.type} in ${result.chunk.filePath}) - Similarity: ${result.similarity.toFixed(4)}`);
                        // console.log(`     Path: ${result.chunk.filePath}:${result.chunk.location.startLine}`);
                        // console.log(`     Content: ${result.chunk.fullText?.substring(0,100)}...`); // Optional: show snippet
                    });
                } else {
                    console.log('No relevant code chunks found for your query.');
                }
            } catch (error) {
                console.error('Error during search:', error);
                process.exit(1);
            }
        }
    )
    .demandCommand(1, 'You need at least one command before moving on')
    .help()
    .alias('h', 'help')
    .strict() // Report errors for unknown options
    .parse();
