/**
 * Example: How the Plugin System Works in Practice
 * 
 * This demonstrates the complete workflow from plugin loading
 * to context extraction with framework-specific filtering.
 */

import { PluginRegistry, PluginAnalysisEngine } from './src/core/PluginSystem.js';
import { FilteringEngine, FilteringLevel } from './src/core/FilteringEngine.js';
import { JavaScriptPlugin, JavaScriptFilteringStrategy } from './src/plugins/JavaScriptPlugin.js';
import { TypeScriptPlugin, TypeScriptFilteringStrategy } from './src/plugins/TypeScriptPlugin.js';

// Example 1: Basic Plugin System Setup
async function setupPluginSystem() {
  const registry = new PluginRegistry();
  
  // Load available plugins (would be dynamic in real implementation)
  const jsPlugin = new JavaScriptPlugin();
  const tsPlugin = new TypeScriptPlugin();
  
  // Register plugins
  registry.registerPlugin(jsPlugin);
  registry.registerPlugin(tsPlugin);
  
  // Register filtering strategies
  registry.registerFilteringStrategy('javascript-core', new JavaScriptFilteringStrategy());
  registry.registerFilteringStrategy('typescript-enhanced', new TypeScriptFilteringStrategy());
  
  // Initialize all plugins
  await registry.initializeAll('./my-project');
  
  return { registry, engine: new PluginAnalysisEngine(registry) };
}

// Example 2: Analyzing a NestJS Controller
async function analyzeNestJSController() {
  const { registry, engine } = await setupPluginSystem();
  
  // Analyze a TypeScript file with NestJS decorators
  const context = {
    filePath: 'src/users/users.controller.ts',
    fileContent: `
      import { Controller, Get, Post, Body } from '@nestjs/common';
      import { UserService } from './user.service';
      import { CreateUserDto } from './dto/create-user.dto';
      
      @Controller('users')
      export class UsersController {
        constructor(private readonly userService: UserService) {}
        
        @Get()
        findAll() {
          return this.userService.findAll();
        }
        
        @Post()
        create(@Body() createUserDto: CreateUserDto) {
          return this.userService.create(createUserDto);
        }
      }
    `,
    language: 'typescript',
    framework: 'nestjs',
    projectRoot: './my-project',
    options: { includeElementContent: true, indexForSearch: false },
    sharedData: new Map()
  };
  
  // Analyze with all applicable plugins
  const result = await engine.analyzeFile(context);
  
  console.log('Analysis Result:');
  console.log(`- Found ${result.elements.length} elements`);
  console.log(`- Languages: ${result.language}`);
  console.log(`- Errors: ${result.errors?.length || 0}`);
  
  // Elements would include:
  // - NestJS decorators (@Controller, @Get, @Post)
  // - TypeScript class with type information
  // - Constructor injection analysis
  // - Method signatures with parameter types
  
  return result;
}

// Example 3: Framework-Specific Context Extraction
async function extractNestJSContext() {
  const { registry, engine } = await setupPluginSystem();
  
  // Simulate analysis results from multiple files
  const allResults = [
    {
      filePath: 'src/users/users.controller.ts',
      elements: [
        { name: 'UsersController', type: 'nestjs_controller', isExported: true },
        { name: 'findAll', type: 'nestjs_endpoint', metadata: { httpMethod: 'GET', path: '/' } },
        { name: 'create', type: 'nestjs_endpoint', metadata: { httpMethod: 'POST', path: '/' } }
      ],
      language: 'typescript'
    },
    {
      filePath: 'src/users/user.service.ts', 
      elements: [
        { name: 'UserService', type: 'nestjs_service', isExported: true },
        { name: 'findAll', type: 'method' },
        { name: 'create', type: 'method' }
      ],
      language: 'typescript'
    },
    {
      filePath: 'src/users/users.module.ts',
      elements: [
        { name: 'UsersModule', type: 'nestjs_module', isExported: true }
      ],
      language: 'typescript'
    }
  ];
  
  // Extract focused context for the controller
  const filteringEngine = new FilteringEngine();
  
  // Register NestJS-specific filtering strategy
  filteringEngine.registerPluginStrategy('nestjs', {
    extractRelevantContext: (focusFile, allResults, options) => {
      return {
        controllers: allResults.flatMap(f => f.elements.filter(e => e.type === 'nestjs_controller')),
        services: allResults.flatMap(f => f.elements.filter(e => e.type === 'nestjs_service')),
        endpoints: allResults.flatMap(f => f.elements.filter(e => e.type === 'nestjs_endpoint')),
        dependencyGraph: [
          { from: 'UsersController', to: 'UserService', type: 'injection' }
        ]
      };
    },
    scoreRelevance: (element, focusFile, context) => {
      if (element.type === 'nestjs_controller') return 0.9;
      if (element.type === 'nestjs_service') return 0.8;
      if (element.type === 'nestjs_endpoint') return 0.7;
      return 0.1;
    },
    generateSummary: (elements, context) => ({
      type: 'nestjs-summary',
      controllers: elements.filter(e => e.type === 'nestjs_controller').length,
      services: elements.filter(e => e.type === 'nestjs_service').length,
      endpoints: elements.filter(e => e.type === 'nestjs_endpoint').length
    })
  });
  
  const context = await filteringEngine.extractContext(
    'src/users/users.controller.ts',
    allResults,
    FilteringLevel.FOCUSED,
    { 
      maxTokens: 1000,
      includeTypes: true,
      includeDependencies: true,
      includeCallGraph: false,
      languageSpecific: {},
      frameworkSpecific: { nestjs: { includeDI: true, includeDecorators: true } }
    }
  );
  
  console.log('NestJS Context:');
  console.log(JSON.stringify(context, null, 2));
  
  // Result would be:
  // {
  //   "focusFile": "src/users/users.controller.ts",
  //   "relevantFiles": [
  //     {
  //       "file": "src/users/user.service.ts",
  //       "relevanceScore": 0.8,
  //       "relationship": "dependency_injection"
  //     }
  //   ],
  //   "summary": {
  //     "totalFiles": 2,
  //     "frameworks": ["nestjs"],
  //     "complexity": "medium"
  //   },
  //   "domainContext": {
  //     "nestjs": {
  //       "controllers": [{"name": "UsersController"}],
  //       "services": [{"name": "UserService"}],
  //       "endpoints": [
  //         {"method": "GET", "path": "/", "handler": "findAll"},
  //         {"method": "POST", "path": "/", "handler": "create"}
  //       ],
  //       "dependencyGraph": [
  //         {"from": "UsersController", "to": "UserService", "type": "injection"}
  //       ]
  //     }
  //   },
  //   "estimatedTokens": 450,
  //   "tokenBudget": 1000,
  //   "compressionRatio": 15.2
  // }
  
  return context;
}

// Example 4: React Component Analysis
async function analyzeReactComponent() {
  const { registry, engine } = await setupPluginSystem();
  
  const context = {
    filePath: 'src/components/UserProfile.tsx',
    fileContent: `
      import React, { useState, useEffect } from 'react';
      import { UserService } from '../services/UserService';
      
      interface UserProfileProps {
        userId: string;
      }
      
      export const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
        const [user, setUser] = useState(null);
        const [loading, setLoading] = useState(true);
        
        useEffect(() => {
          UserService.fetchUser(userId)
            .then(setUser)
            .finally(() => setLoading(false));
        }, [userId]);
        
        if (loading) return <div>Loading...</div>;
        
        return (
          <div className="user-profile">
            <h1>{user?.name}</h1>
            <p>{user?.email}</p>
          </div>
        );
      };
    `,
    language: 'typescript',
    framework: 'react',
    projectRoot: './my-project',
    options: { includeElementContent: true, indexForSearch: false },
    sharedData: new Map()
  };
  
  const result = await engine.analyzeFile(context);
  
  // Would extract:
  // - React component with props interface
  // - useState and useEffect hooks with dependencies
  // - JSX elements and structure
  // - Service dependencies
  
  console.log('React Analysis Result:');
  console.log(`- Component: ${result.elements.find(e => e.type === 'react_component')?.name}`);
  console.log(`- Hooks: ${result.elements.filter(e => e.type === 'react_hook').length}`);
  console.log(`- Props: ${result.elements.find(e => e.type === 'interface')?.name}`);
  
  return result;
}

// Example 5: CLI Integration
async function cliExample() {
  // This would be the CLI command implementation
  console.log('CLI: cyzer context src/users/users.controller.ts --framework nestjs --max-tokens 1000');
  
  const context = await extractNestJSContext();
  
  // Generate LLM-ready prompt
  const llmPrompt = `
You are analyzing a NestJS controller file: ${context.focusFile}

Context Summary:
- Framework: NestJS
- Controllers: ${context.domainContext.nestjs?.controllers?.length || 0}
- Services: ${context.domainContext.nestjs?.services?.length || 0}  
- API Endpoints: ${context.domainContext.nestjs?.endpoints?.length || 0}

Key Dependencies:
${context.domainContext.nestjs?.dependencyGraph?.map(dep => 
  `- ${dep.from} depends on ${dep.to} (${dep.type})`
).join('\n') || 'None'}

This controller exposes ${context.domainContext.nestjs?.endpoints?.length || 0} API endpoints and uses dependency injection to access services.

Relevant Files (${context.relevantFiles.length}):
${context.relevantFiles.map(f => 
  `- ${f.file} (relevance: ${f.relevanceScore.toFixed(2)}, relationship: ${f.relationship})`
).join('\n')}

Token Usage: ${context.estimatedTokens}/${context.tokenBudget} (${context.compressionRatio.toFixed(1)}x compression)
  `;
  
  console.log('Generated LLM Prompt:');
  console.log(llmPrompt);
  
  return llmPrompt;
}

// Run examples
async function runExamples() {
  console.log('=== Plugin System Examples ===\n');
  
  try {
    await analyzeNestJSController();
    console.log('\n---\n');
    
    await extractNestJSContext();
    console.log('\n---\n');
    
    await analyzeReactComponent();
    console.log('\n---\n');
    
    await cliExample();
    
  } catch (error) {
    console.error('Example failed:', error);
  }
}

// Export for testing
export {
  setupPluginSystem,
  analyzeNestJSController,
  extractNestJSContext,
  analyzeReactComponent,
  cliExample,
  runExamples
};
