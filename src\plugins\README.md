# 🔌 Plugin Architecture Structure

## 📁 **Plugin Organization**

```
src/plugins/
├── core/                           # Core language plugins
│   ├── javascript/
│   │   ├── JavaScriptPlugin.ts     # Main plugin
│   │   ├── queries/
│   │   │   └── javascript.scm      # Tree-sitter queries
│   │   └── types/
│   │       └── JavaScriptTypes.ts  # JS-specific types
│   │
│   ├── typescript/
│   │   ├── TypeScriptPlugin.ts     # Main plugin
│   │   ├── queries/
│   │   │   └── typescript.scm      # TS-specific queries
│   │   ├── compiler/
│   │   │   └── CompilerAPIService.ts # TypeScript Compiler API
│   │   └── types/
│   │       └── TypeScriptTypes.ts  # TS-specific types
│   │
│   └── python/                     # Future language support
│       ├── PythonPlugin.ts
│       └── queries/
│           └── python.scm
│
├── features/                       # Feature-specific plugins
│   ├── decorators/
│   │   ├── DecoratorsPlugin.ts     # Generic decorator analysis
│   │   ├── queries/
│   │   │   ├── decorators.scm      # Generic decorator patterns
│   │   │   ├── nestjs-decorators.scm
│   │   │   └── angular-decorators.scm
│   │   └── types/
│   │       └── DecoratorTypes.ts
│   │
│   ├── testing/
│   │   ├── TestingPlugin.ts        # Test analysis
│   │   ├── queries/
│   │   │   ├── jest.scm
│   │   │   ├── mocha.scm
│   │   │   └── vitest.scm
│   │   └── analyzers/
│   │       ├── JestAnalyzer.ts
│   │       └── MochaAnalyzer.ts
│   │
│   └── security/
│       ├── SecurityPlugin.ts       # Security pattern analysis
│       ├── queries/
│       │   └── security-patterns.scm
│       └── rules/
│           ├── XSSRules.ts
│           └── SQLInjectionRules.ts
│
├── frameworks/                     # Framework-specific plugins
│   ├── nestjs/
│   │   ├── NestJSPlugin.ts         # Main NestJS plugin
│   │   ├── analyzers/
│   │   │   ├── DIAnalyzer.ts       # Dependency injection
│   │   │   ├── ModuleAnalyzer.ts   # Module structure
│   │   │   └── ControllerAnalyzer.ts # Controller analysis
│   │   ├── queries/
│   │   │   └── nestjs-patterns.scm
│   │   └── types/
│   │       └── NestJSTypes.ts
│   │
│   ├── react/
│   │   ├── ReactPlugin.ts          # Main React plugin
│   │   ├── analyzers/
│   │   │   ├── ComponentAnalyzer.ts # Component analysis
│   │   │   ├── HookAnalyzer.ts     # Hook analysis
│   │   │   └── JSXAnalyzer.ts      # JSX analysis
│   │   ├── queries/
│   │   │   ├── react-components.scm
│   │   │   ├── react-hooks.scm
│   │   │   └── jsx-patterns.scm
│   │   └── types/
│   │       └── ReactTypes.ts
│   │
│   ├── express/
│   │   ├── ExpressPlugin.ts
│   │   ├── analyzers/
│   │   │   ├── RouteAnalyzer.ts
│   │   │   └── MiddlewareAnalyzer.ts
│   │   └── queries/
│   │       └── express-patterns.scm
│   │
│   └── nextjs/
│       ├── NextJSPlugin.ts
│       ├── analyzers/
│       │   ├── PageAnalyzer.ts
│       │   └── APIRouteAnalyzer.ts
│       └── queries/
│           └── nextjs-patterns.scm
│
└── filtering/                      # Filtering plugins (separate concern)
    ├── core/
    │   ├── CoreFilteringPlugin.ts  # Base filtering logic
    │   └── strategies/
    │       ├── RelevanceScoring.ts
    │       └── TokenBudgeting.ts
    │
    ├── language/
    │   ├── JavaScriptFilteringPlugin.ts
    │   ├── TypeScriptFilteringPlugin.ts
    │   └── PythonFilteringPlugin.ts
    │
    └── framework/
        ├── NestJSFilteringPlugin.ts
        ├── ReactFilteringPlugin.ts
        └── ExpressFilteringPlugin.ts
```

## 🎯 **Plugin Types**

### **1. Core Language Plugins**
- Handle basic syntax analysis for languages
- Provide foundation for framework plugins
- Examples: JavaScript, TypeScript, Python, Go

### **2. Feature Plugins**
- Handle specific language features across frameworks
- Reusable across different contexts
- Examples: Decorators, Testing, Security, Performance

### **3. Framework Plugins**
- Handle framework-specific patterns and analysis
- Build on core language + feature plugins
- Examples: NestJS, React, Express, Angular

### **4. Filtering Plugins**
- Handle context extraction and relevance scoring
- Separate from analysis plugins for clean separation
- Examples: Language-specific filtering, Framework-specific filtering

## 🔧 **Plugin Dependencies**

```
NestJS Plugin depends on:
├── TypeScript Plugin (core language)
├── Decorators Plugin (feature)
└── NestJS Filtering Plugin (filtering)

React Plugin depends on:
├── JavaScript/TypeScript Plugin (core language)
├── JSX Plugin (feature)
└── React Filtering Plugin (filtering)
```

## 🎯 **Benefits of This Structure**

1. **Granular Reusability** - Decorators plugin used by NestJS, Angular, etc.
2. **Clear Separation** - Analysis vs Filtering concerns
3. **Easy Testing** - Each plugin can be tested independently
4. **Maintainability** - Related files grouped together
5. **Extensibility** - Easy to add new languages/frameworks
