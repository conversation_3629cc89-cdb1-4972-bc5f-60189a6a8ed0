/**
 * Core Filtering Engine
 * 
 * Handles context extraction and filtering with plugin-specific strategies.
 * Different languages and frameworks can have different filtering approaches.
 */

import { CodeElement, FileAnalysisResult } from '../types/CodeElement.js';
import { PluginFilteringStrategy, FilteringOptions } from './PluginSystem.js';

/**
 * Relevance scoring factors
 */
export interface RelevanceFactors {
  // Direct relationships
  isDirectDependency: number;    // File directly imports/exports focus file
  isDirectDependent: number;     // Focus file imports/exports this file
  
  // Indirect relationships  
  isIndirectDependency: number;  // Connected through dependency chain
  isIndirectDependent: number;   // Connected through dependent chain
  
  // Code relationships
  sharesFunctions: number;       // Uses same functions/classes
  sharesTypes: number;          // Uses same types/interfaces
  sharesPatterns: number;       // Similar code patterns
  
  // Structural relationships
  sameDirectory: number;         // In same directory
  sameModule: number;           // In same module/package
  sameFramework: number;        // Uses same framework patterns
  
  // Temporal relationships
  recentlyModified: number;     // Recently changed files
  frequentlyModified: number;   // Frequently changed together
  
  // Domain-specific factors (varies by plugin)
  domainSpecific: { [key: string]: number };
}

/**
 * Context extraction result
 */
export interface ExtractedContext {
  focusFile: string;
  relevantFiles: Array<{
    file: string;
    relevanceScore: number;
    relationship: string;
    elements: CodeElement[];
  }>;
  
  // Aggregated information
  summary: {
    totalFiles: number;
    totalElements: number;
    languages: string[];
    frameworks: string[];
    complexity: 'low' | 'medium' | 'high';
  };
  
  // Domain-specific context (from plugins)
  domainContext: { [pluginName: string]: any };
  
  // Token management
  estimatedTokens: number;
  tokenBudget: number;
  compressionRatio: number;
}

/**
 * Multi-level filtering strategy
 */
export enum FilteringLevel {
  SUMMARY = 'summary',       // 50-100 tokens
  FOCUSED = 'focused',       // 500-1000 tokens  
  DETAILED = 'detailed',     // 2000-4000 tokens
  COMPLETE = 'complete'      // No limits
}

/**
 * Core filtering engine
 */
export class FilteringEngine {
  private relevanceFactors: RelevanceFactors;
  private pluginStrategies: Map<string, PluginFilteringStrategy> = new Map();
  
  constructor(relevanceFactors?: Partial<RelevanceFactors>) {
    this.relevanceFactors = {
      // Default weights - can be customized
      isDirectDependency: 1.0,
      isDirectDependent: 0.9,
      isIndirectDependency: 0.6,
      isIndirectDependent: 0.5,
      sharesFunctions: 0.7,
      sharesTypes: 0.8,
      sharesPatterns: 0.4,
      sameDirectory: 0.3,
      sameModule: 0.5,
      sameFramework: 0.6,
      recentlyModified: 0.4,
      frequentlyModified: 0.3,
      domainSpecific: {},
      ...relevanceFactors
    };
  }
  
  /**
   * Register a plugin-specific filtering strategy
   */
  registerPluginStrategy(pluginName: string, strategy: PluginFilteringStrategy): void {
    this.pluginStrategies.set(pluginName, strategy);
  }
  
  /**
   * Extract context at specified filtering level
   */
  async extractContext(
    focusFile: string,
    allResults: FileAnalysisResult[],
    level: FilteringLevel,
    options: FilteringOptions
  ): Promise<ExtractedContext> {
    
    // Step 1: Score relevance of all files
    const scoredFiles = await this.scoreFileRelevance(focusFile, allResults, options);
    
    // Step 2: Apply token budget based on filtering level
    const tokenBudget = this.getTokenBudgetForLevel(level);
    const filteredFiles = this.applyTokenBudget(scoredFiles, tokenBudget, options);
    
    // Step 3: Extract domain-specific context using plugin strategies
    const domainContext = await this.extractDomainContext(focusFile, filteredFiles, options);
    
    // Step 4: Generate summary
    const summary = this.generateSummary(filteredFiles);
    
    // Step 5: Estimate final token count
    const estimatedTokens = this.estimateTokenCount(filteredFiles, domainContext);
    
    return {
      focusFile,
      relevantFiles: filteredFiles,
      summary,
      domainContext,
      estimatedTokens,
      tokenBudget,
      compressionRatio: this.calculateCompressionRatio(allResults, filteredFiles)
    };
  }
  
  /**
   * Score relevance of each file to the focus file
   */
  private async scoreFileRelevance(
    focusFile: string,
    allResults: FileAnalysisResult[],
    options: FilteringOptions
  ): Promise<Array<{ file: string; relevanceScore: number; relationship: string; elements: CodeElement[]; }>> {
    
    const focusFileResult = allResults.find(f => f.filePath === focusFile);
    if (!focusFileResult) {
      throw new Error(`Focus file ${focusFile} not found in analysis results`);
    }
    
    const scoredFiles: Array<{ file: string; relevanceScore: number; relationship: string; elements: CodeElement[]; }> = [];
    
    for (const fileResult of allResults) {
      if (fileResult.filePath === focusFile) continue; // Skip focus file itself
      
      let score = 0;
      let relationship = 'unrelated';
      
      // Direct dependency analysis
      const directDep = this.analyzeDirectDependency(focusFileResult, fileResult);
      if (directDep.isDependency) {
        score += this.relevanceFactors.isDirectDependency;
        relationship = 'direct_dependency';
      }
      if (directDep.isDependent) {
        score += this.relevanceFactors.isDirectDependent;
        relationship = 'direct_dependent';
      }
      
      // Shared elements analysis
      const sharedAnalysis = this.analyzeSharedElements(focusFileResult, fileResult);
      score += sharedAnalysis.functionScore * this.relevanceFactors.sharesFunctions;
      score += sharedAnalysis.typeScore * this.relevanceFactors.sharesTypes;
      
      // Structural analysis
      const structuralScore = this.analyzeStructuralRelationship(focusFile, fileResult.filePath);
      score += structuralScore;
      
      // Apply plugin-specific scoring
      for (const [pluginName, strategy] of this.pluginStrategies) {
        try {
          const pluginScore = strategy.scoreRelevance(
            fileResult.elements[0], // Representative element
            focusFile,
            { focusFileResult, fileResult }
          );
          score += pluginScore * (this.relevanceFactors.domainSpecific[pluginName] || 0.5);
        } catch (error) {
          console.warn(`Plugin ${pluginName} scoring failed:`, error);
        }
      }
      
      if (score > 0.1) { // Only include files with meaningful relevance
        scoredFiles.push({
          file: fileResult.filePath,
          relevanceScore: score,
          relationship,
          elements: fileResult.elements
        });
      }
    }
    
    // Sort by relevance score (highest first)
    return scoredFiles.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }
  
  /**
   * Apply token budget constraints
   */
  private applyTokenBudget(
    scoredFiles: Array<{ file: string; relevanceScore: number; relationship: string; elements: CodeElement[]; }>,
    tokenBudget: number,
    options: FilteringOptions
  ): Array<{ file: string; relevanceScore: number; relationship: string; elements: CodeElement[]; }> {
    
    const result: Array<{ file: string; relevanceScore: number; relationship: string; elements: CodeElement[]; }> = [];
    let currentTokens = 0;
    
    for (const fileInfo of scoredFiles) {
      const fileTokens = this.estimateFileTokens(fileInfo.elements);
      
      if (currentTokens + fileTokens <= tokenBudget) {
        result.push(fileInfo);
        currentTokens += fileTokens;
      } else {
        // Try to include partial information for high-relevance files
        if (fileInfo.relevanceScore > 0.8 && currentTokens < tokenBudget * 0.9) {
          const partialElements = this.selectMostImportantElements(
            fileInfo.elements, 
            tokenBudget - currentTokens
          );
          
          if (partialElements.length > 0) {
            result.push({
              ...fileInfo,
              elements: partialElements
            });
            currentTokens += this.estimateFileTokens(partialElements);
          }
        }
        break; // Stop adding files
      }
    }
    
    return result;
  }
  
  /**
   * Extract domain-specific context using plugin strategies
   */
  private async extractDomainContext(
    focusFile: string,
    relevantFiles: Array<{ file: string; relevanceScore: number; relationship: string; elements: CodeElement[]; }>,
    options: FilteringOptions
  ): Promise<{ [pluginName: string]: any }> {
    
    const domainContext: { [pluginName: string]: any } = {};
    
    // Convert to FileAnalysisResult format for plugin strategies
    const fileResults: FileAnalysisResult[] = relevantFiles.map(rf => ({
      filePath: rf.file,
      elements: rf.elements,
      language: 'unknown' // Would need to be determined
    }));
    
    for (const [pluginName, strategy] of this.pluginStrategies) {
      try {
        const context = strategy.extractRelevantContext(focusFile, fileResults, options);
        domainContext[pluginName] = context;
      } catch (error) {
        console.warn(`Plugin ${pluginName} context extraction failed:`, error);
      }
    }
    
    return domainContext;
  }
  
  // Helper methods
  private getTokenBudgetForLevel(level: FilteringLevel): number {
    switch (level) {
      case FilteringLevel.SUMMARY: return 100;
      case FilteringLevel.FOCUSED: return 1000;
      case FilteringLevel.DETAILED: return 4000;
      case FilteringLevel.COMPLETE: return Number.MAX_SAFE_INTEGER;
      default: return 1000;
    }
  }
  
  private analyzeDirectDependency(focusFile: FileAnalysisResult, targetFile: FileAnalysisResult) {
    // Analyze import/export relationships
    // This would check if focusFile imports from targetFile or vice versa
    return { isDependency: false, isDependent: false };
  }
  
  private analyzeSharedElements(focusFile: FileAnalysisResult, targetFile: FileAnalysisResult) {
    // Analyze shared function calls, type usage, etc.
    return { functionScore: 0, typeScore: 0 };
  }
  
  private analyzeStructuralRelationship(focusFile: string, targetFile: string): number {
    // Analyze directory structure, module relationships
    return 0;
  }
  
  private estimateFileTokens(elements: CodeElement[]): number {
    // Rough estimation: element name + type + some metadata
    return elements.length * 10; // Very rough estimate
  }
  
  private estimateTokenCount(files: any[], domainContext: any): number {
    return 0; // Implementation needed
  }
  
  private calculateCompressionRatio(allResults: FileAnalysisResult[], filteredFiles: any[]): number {
    const totalElements = allResults.reduce((sum, f) => sum + f.elements.length, 0);
    const filteredElements = filteredFiles.reduce((sum, f) => sum + f.elements.length, 0);
    return totalElements / Math.max(filteredElements, 1);
  }
  
  private selectMostImportantElements(elements: CodeElement[], tokenBudget: number): CodeElement[] {
    // Select most important elements within token budget
    // Priority: exports > classes > functions > others
    return elements.slice(0, Math.floor(tokenBudget / 10));
  }
  
  private generateSummary(files: any[]) {
    return {
      totalFiles: files.length,
      totalElements: files.reduce((sum, f) => sum + f.elements.length, 0),
      languages: [],
      frameworks: [],
      complexity: 'medium' as const
    };
  }
}
