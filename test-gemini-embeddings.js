import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Configure environment variables from .env file
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, '.env') });

// Configuration
const CONFIG = {
    model: 'embedding-001', // Updated to the latest stable embedding model
    debug: process.env.DEBUG_MODE === 'true' || false,
    testTexts: [
        'What is the meaning of life?',
        'The quick brown fox jumps over the lazy dog.',
        'Artificial intelligence is transforming the world.',
        '42'
    ]
};

// Logging utility
class Logger {
    static log(message, data = null) {
        console.log(`[LOG] ${message}`);
        if (data && CONFIG.debug) {
            console.log(JSON.stringify(data, null, 2));
        }
    }

    static error(message, error = null) {
        console.error(`[ERROR] ${message}`);
        if (error) {
            console.error(error);
        }
    }

    static debug(message, data = null) {
        if (CONFIG.debug) {
            console.log(`[DEBUG] ${message}`);
            if (data) {
                console.log(JSON.stringify(data, null, 2));
            }
        }
    }
}

// Main function
async function testGeminiEmbeddings() {
    try {
        Logger.log('Starting Gemini Embeddings Test');
        Logger.debug('Configuration:', CONFIG);

        // Validate API Key
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey || apiKey === 'your_api_key_here') {
            throw new Error('Please set your GEMINI_API_KEY in the .env file');
        }

        // Initialize the Google Generative AI client
        Logger.log('Initializing Google Generative AI client...');
        const genAI = new GoogleGenerativeAI(apiKey);
        const model = genAI.getGenerativeModel({ model: CONFIG.model });

        // Test embedding generation
        Logger.log('\n--- Testing Embedding Generation ---');
        const results = [];
        
        for (const text of CONFIG.testTexts) {
            try {
                Logger.log(`\nProcessing text: "${text}"`);
                
                const startTime = Date.now();
                const result = await model.embedContent(text);
                const endTime = Date.now();
                
                const embedding = result.embedding;
                const stats = {
                    textLength: text.length,
                    embeddingSize: embedding.values.length,
                    dimensions: embedding.values.length,
                    processingTimeMs: endTime - startTime,
                    firstFewValues: embedding.values.slice(0, 5) // Show first 5 values
                };
                
                Logger.log(`Generated embedding for text (${text.length} chars)`);
                Logger.debug('Embedding stats:', stats);
                
                results.push({
                    text,
                    embedding: embedding,
                    stats
                });
                
            } catch (error) {
                Logger.error(`Error processing text: "${text}"`, error);
            }
        }

        // Calculate similarity between embeddings (cosine similarity)
        if (results.length > 1) {
            Logger.log('\n--- Calculating Similarity Between Embeddings ---');
            
            // Compare each text with every other text
            for (let i = 0; i < results.length; i++) {
                for (let j = i + 1; j < results.length; j++) {
                    const sim = cosineSimilarity(
                        results[i].embedding.values,
                        results[j].embedding.values
                    );
                    Logger.log(
                        `Similarity between "${results[i].text.substring(0, 20)}..." and "${results[j].text.substring(0, 20)}...": ${sim.toFixed(4)}`
                    );
                }
            }
        }


        Logger.log('\n--- Test Complete ---');
        Logger.log(`Successfully processed ${results.length} out of ${CONFIG.testTexts.length} texts`);
        
    } catch (error) {
        Logger.error('An error occurred during the test:', error);
        process.exit(1);
    }
}

// Helper function to calculate cosine similarity between two vectors
function cosineSimilarity(vecA, vecB) {
    if (vecA.length !== vecB.length) {
        throw new Error('Vectors must have the same length');
    }
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        normA += vecA[i] * vecA[i];
        normB += vecB[i] * vecB[i];
    }
    
    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);
    
    if (normA === 0 || normB === 0) return 0;
    return dotProduct / (normA * normB);
}

// Run the test
testGeminiEmbeddings();
