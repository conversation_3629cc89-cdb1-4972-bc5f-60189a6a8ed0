; Functions & Methods
(
  (comment)* @doc
  .
  (method_definition
    name: (property_identifier) @name.definition.method) @definition.method
  (#not-eq? @name.definition.method "constructor")
  ;(#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$") ; Strip comments - optional
  ;(#select-adjacent! @doc @definition.method) ; Select comments - optional
)

(
  (comment)* @doc
  .
  [
    (function
      name: (identifier) @name.definition.function)
    (function_declaration
      name: (identifier) @name.definition.function)
    (generator_function
      name: (identifier) @name.definition.function)
    (generator_function_declaration
      name: (identifier) @name.definition.function)
  ] @definition.function
)

(
  (comment)* @doc
  .
  (lexical_declaration
    (variable_declarator
      name: (identifier) @name.definition.function
      value: [(arrow_function) (function)]) @definition.function)
)

(
  (comment)* @doc
  .
  (variable_declaration
    (variable_declarator
      name: (identifier) @name.definition.function
      value: [(arrow_function) (function)]) @definition.function)
)

(assignment_expression
  left: [
    (identifier) @name.definition.function
    (member_expression
      property: (property_identifier) @name.definition.function) ; Treat assignments to properties as function defs too? Maybe method?
  ]
  right: [(arrow_function) (function)]
) @definition.function

(pair ; Methods in object literals
  key: (property_identifier) @name.definition.method
  value: [(arrow_function) (function)]) @definition.method


; Classes, Interfaces, Types, Enums
(
  (comment)* @doc
  .
  [
    (class
      name: (_) @name.definition.class)
    (class_declaration
      name: (_) @name.definition.class)
  ] @definition.class
)

(abstract_class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

(interface_declaration
  name: (type_identifier) @name.definition.interface) @definition.interface

(type_alias_declaration
  name: (type_identifier) @name.definition.type) @definition.type

(enum_declaration
  name: (identifier) @name.definition.enum) @definition.enum


; References (Basic examples)
(
  (call_expression
    function: (identifier) @name.reference.call) @reference.call
  (#not-match? @name.reference.call "^(require)$") ; Avoid common keywords if needed
)

(call_expression
  function: (member_expression
    property: (property_identifier) @name.reference.call)
  arguments: (_) @reference.call)

(new_expression ; Class instantiation
  constructor: (_) @name.reference.class) @reference.class

(type_annotation ; Type usage
  (type_identifier) @name.reference.type) @reference.type

; Potentially ambiguous definitions (signatures might also be definitions)
(function_signature
  name: (identifier) @name.definition.function) @definition.function

(method_signature
  name: (property_identifier) @name.definition.method) @definition.method

(abstract_method_signature
  name: (property_identifier) @name.definition.method) @definition.method

; Modules (basic)
(module
  name: (identifier) @name.definition.module) @definition.module

; Add imports/exports later if needed