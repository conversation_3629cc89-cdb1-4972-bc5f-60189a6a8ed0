/**
 * NestJS Plugin - Main Export
 * 
 * Self-contained NestJS framework analysis plugin.
 * Future: @cyzer/plugin-nestjs
 */

export { NestJSPlugin } from './NestJSPlugin.js';
export { NestJSFilteringStrategy } from './filtering/NestJSFiltering.js';
export * from './types/NestJSTypes.js';

// Plugin metadata for registry
export const PLUGIN_METADATA = {
  name: 'nestjs-plugin',
  version: '1.0.0',
  description: 'NestJS framework analysis with DI, controllers, services, and modules',
  languages: ['typescript'],
  frameworks: ['nestjs'],
  dependencies: ['typescript-plugin'], // Depends on TypeScript plugin
  capabilities: {
    syntaxAnalysis: true,
    semanticAnalysis: true,
    frameworkPatterns: true,
    decoratorAnalysis: true,
    dependencyTracking: true,
    componentAnalysis: true
  }
};

// Default export for easy importing
export default NestJSPlugin;
