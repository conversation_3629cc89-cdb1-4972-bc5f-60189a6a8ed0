import { Embedded<PERSON>ode<PERSON>hun<PERSON>, SearchResult, MetadataFilters, CodeElementType } from '../types/CodeElement.js';

export class VectorSearchService {
    private vectorIndex: EmbeddedCodeChunk[] = [];

    constructor() {}

    /**
     * Adds or updates embedded code chunks in the in-memory index.
     * If a chunk with the same ID already exists, it's replaced.
     * @param chunks - An array of EmbeddedCodeChunk objects to index.
     */
    async indexChunks(chunks: EmbeddedCodeChunk[]): Promise<void> {
        chunks.forEach(newChunk => {
            const existingIndex = this.vectorIndex.findIndex(c => c.filePath === newChunk.filePath && c.name === newChunk.name && c.location.startLine === newChunk.location.startLine);
            if (existingIndex !== -1) {
                this.vectorIndex[existingIndex] = newChunk; // Replace existing
            } else {
                this.vectorIndex.push(newChunk);
            }
        });
        // console.log(`Indexed ${chunks.length} chunks. Total chunks in index: ${this.vectorIndex.length}`);
    }

    /**
     * Clears all items from the in-memory vector index.
     */
    async clearIndex(): Promise<void> {
        this.vectorIndex = [];
        // console.log('In-memory vector index cleared.');
    }

    /**
     * Searches the index for code chunks similar to the query vector.
     * @param queryVector - The vector embedding of the search query.
     * @param topK - The maximum number of similar chunks to return.
     * @param filters - Optional metadata filters to apply to the search.
     * @returns A promise that resolves to an array of SearchResult objects.
     */
    async search(queryVector: number[], topK: number, filters?: MetadataFilters): Promise<SearchResult[]> {
        if (!queryVector || queryVector.length === 0) {
            console.warn('Query vector is empty. Cannot perform search.');
            return [];
        }

        const similarities: SearchResult[] = [];

        for (const chunk of this.vectorIndex) {
            if (!chunk.embedding || chunk.embedding.length === 0) {
                // console.warn(`Chunk ${chunk.name} in ${chunk.filePath} has no embedding. Skipping.`);
                continue;
            }

            // Apply filters before calculating similarity for efficiency
            if (filters) {
                if (filters.filePath && chunk.filePath !== filters.filePath) continue;
                if (filters.elementType && chunk.type !== filters.elementType) continue;
            }

            const similarity = this.cosineSimilarity(queryVector, chunk.embedding);

            if (filters && filters.minSimilarity && similarity < filters.minSimilarity) {
                continue;
            }

            similarities.push({
                chunk: chunk,
                similarity: similarity,
            });
        }

        // Sort by similarity in descending order and take topK
        return similarities
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, topK);
    }

    /**
     * Calculates the cosine similarity between two vectors.
     * @param vecA - The first vector.
     * @param vecB - The second vector.
     * @returns The cosine similarity score (between -1 and 1).
     */
    private cosineSimilarity(vecA: number[], vecB: number[]): number {
        if (vecA.length !== vecB.length) {
            // console.warn('Vectors have different lengths, cannot compute similarity.');
            return 0; // Or throw an error
        }

        let dotProduct = 0;
        let normA = 0;
        let normB = 0;

        for (let i = 0; i < vecA.length; i++) {
            dotProduct += vecA[i] * vecB[i];
            normA += vecA[i] * vecA[i];
            normB += vecB[i] * vecB[i];
        }

        normA = Math.sqrt(normA);
        normB = Math.sqrt(normB);

        if (normA === 0 || normB === 0) {
            return 0; // Avoid division by zero if one vector is all zeros
        }

        return dotProduct / (normA * normB);
    }

    /**
     * Gets the total number of chunks currently in the index.
     * @returns Number of indexed chunks.
     */
    getIndexSize(): number {
        return this.vectorIndex.length;
    }
}
