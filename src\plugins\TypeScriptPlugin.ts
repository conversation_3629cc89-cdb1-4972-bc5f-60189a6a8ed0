/**
 * TypeScript Plugin with Compiler API Integration
 * 
 * This plugin demonstrates how to integrate external dependencies (TypeScript Compiler API)
 * while building on the JavaScript plugin foundation.
 */

import { 
  AnalysisPlugin, 
  PluginMetadata, 
  PluginCapabilities, 
  PluginAnalysisContext, 
  PluginAnalysisResult,
  PluginFilteringStrategy,
  FilteringOptions
} from '../core/PluginSystem.js';
import { CodeElement, CodeElementType, FileAnalysisResult } from '../types/CodeElement.js';
import { JavaScriptPlugin } from './JavaScriptPlugin.js';
import * as ts from 'typescript'; // External dependency
import * as path from 'path';
import * as fs from 'fs';

export class TypeScriptPlugin implements AnalysisPlugin {
  metadata: PluginMetadata = {
    name: 'typescript-enhanced',
    version: '1.0.0',
    description: 'Enhanced TypeScript analysis with Compiler API for type information',
    author: 'Cyzer Team',
    
    languages: ['typescript'],
    frameworks: [], // Framework-agnostic
    fileExtensions: ['.ts', '.tsx', '.mts', '.cts'],
    
    capabilities: {
      syntaxAnalysis: true,
      semanticAnalysis: true,      // ✅ Can understand types and semantics
      crossFileAnalysis: true,
      typeInference: true,         // ✅ Can infer and resolve types
      dependencyTracking: true,
      callGraphGeneration: true,
      frameworkPatterns: false,
      decoratorAnalysis: true,     // ✅ Can analyze TypeScript decorators
      componentAnalysis: false,
      incrementalAnalysis: true,   // ✅ TypeScript supports incremental compilation
      largeCodebaseOptimized: true
    },
    
    priority: 200, // Higher priority than JavaScript plugin
    dependencies: ['javascript-core'], // Builds on JavaScript plugin
    
    requiresTypeScript: true,    // ✅ Needs TypeScript Compiler API
    requiresNodeModules: true,   // ✅ Needs access to type definitions
    memoryIntensive: true        // ✅ TypeScript compilation is memory-intensive
  };
  
  private jsPlugin: JavaScriptPlugin;
  private program: ts.Program | null = null;
  private typeChecker: ts.TypeChecker | null = null;
  private projectRoot: string = '';
  
  constructor() {
    this.jsPlugin = new JavaScriptPlugin();
  }
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    this.projectRoot = projectRoot;
    
    // Initialize the base JavaScript plugin
    await this.jsPlugin.initialize(projectRoot, options);
    
    // Initialize TypeScript Compiler API
    await this.initializeTypeScriptCompiler(projectRoot);
  }
  
  async cleanup(): Promise<void> {
    await this.jsPlugin.cleanup();
    this.program = null;
    this.typeChecker = null;
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    return language === 'typescript' && 
           this.metadata.fileExtensions.some(ext => filePath.endsWith(ext));
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    const startTime = Date.now();
    
    try {
      // First, get basic syntax analysis from JavaScript plugin
      const jsResult = await this.jsPlugin.analyze({
        ...context,
        language: 'javascript' // Treat as JavaScript for syntax
      });
      
      // Then enhance with TypeScript-specific analysis
      const tsEnhancements = await this.analyzeTypeScript(context);
      
      // Merge results
      const enhancedElements = this.mergeAnalysisResults(jsResult.elements, tsEnhancements);
      
      // Share TypeScript-specific data
      const sharedData = new Map(jsResult.sharedData || []);
      sharedData.set('typescript-program', this.program);
      sharedData.set('typescript-checker', this.typeChecker);
      sharedData.set('typescript-enhancements', tsEnhancements);
      
      return {
        elements: enhancedElements,
        errors: [...(jsResult.errors || []), ...(tsEnhancements.errors || [])],
        warnings: tsEnhancements.warnings || [],
        metadata: {
          ...jsResult.metadata,
          language: 'typescript',
          hasTypeInformation: this.typeChecker !== null,
          compilerVersion: ts.version
        },
        analysisTime: Date.now() - startTime,
        memoryUsed: jsResult.memoryUsed + (tsEnhancements.memoryUsed || 0),
        confidence: this.typeChecker ? 0.95 : 0.8, // Higher confidence with type checker
        sharedData
      };
      
    } catch (error) {
      return {
        elements: [],
        errors: [`TypeScript analysis failed: ${error}`],
        warnings: [],
        metadata: {},
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0,
      };
    }
  }
  
  /**
   * Initialize TypeScript Compiler API
   */
  private async initializeTypeScriptCompiler(projectRoot: string): Promise<void> {
    try {
      // Find tsconfig.json
      const tsconfigPath = this.findTsConfig(projectRoot);
      
      if (!tsconfigPath) {
        console.warn('No tsconfig.json found, TypeScript semantic analysis will be limited');
        return;
      }
      
      // Read and parse tsconfig.json
      const configFile = ts.readConfigFile(tsconfigPath, ts.sys.readFile);
      if (configFile.error) {
        console.warn('Error reading tsconfig.json:', configFile.error.messageText);
        return;
      }
      
      // Parse compiler options
      const parsedConfig = ts.parseJsonConfigFileContent(
        configFile.config,
        ts.sys,
        path.dirname(tsconfigPath)
      );
      
      if (parsedConfig.errors.length > 0) {
        console.warn('TypeScript config errors:', parsedConfig.errors);
      }
      
      // Create TypeScript program
      this.program = ts.createProgram({
        rootNames: parsedConfig.fileNames,
        options: parsedConfig.options,
        configFileParsingDiagnostics: parsedConfig.errors
      });
      
      // Get type checker
      this.typeChecker = this.program.getTypeChecker();
      
      console.log('TypeScript Compiler API initialized successfully');
      
    } catch (error) {
      console.warn('Failed to initialize TypeScript Compiler API:', error);
    }
  }
  
  /**
   * Perform TypeScript-specific analysis
   */
  private async analyzeTypeScript(context: PluginAnalysisContext): Promise<{
    elements: CodeElement[];
    errors: string[];
    warnings: string[];
    memoryUsed: number;
  }> {
    
    if (!this.program || !this.typeChecker) {
      return { elements: [], errors: [], warnings: ['TypeScript Compiler API not available'], memoryUsed: 0 };
    }
    
    const sourceFile = this.program.getSourceFile(context.filePath);
    if (!sourceFile) {
      return { elements: [], errors: [`File ${context.filePath} not found in TypeScript program`], warnings: [], memoryUsed: 0 };
    }
    
    const elements: CodeElement[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Extract type information
    const typeElements = this.extractTypeInformation(sourceFile);
    elements.push(...typeElements);
    
    // Extract decorator information
    const decoratorElements = this.extractDecoratorInformation(sourceFile);
    elements.push(...decoratorElements);
    
    // Extract interface/type alias information
    const interfaceElements = this.extractInterfaceInformation(sourceFile);
    elements.push(...interfaceElements);
    
    // Check for TypeScript-specific errors
    const diagnostics = this.program.getSemanticDiagnostics(sourceFile);
    for (const diagnostic of diagnostics) {
      if (diagnostic.file === sourceFile) {
        errors.push(ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n'));
      }
    }
    
    return { elements, errors, warnings, memoryUsed: 0 };
  }
  
  /**
   * Extract type information using TypeScript Compiler API
   */
  private extractTypeInformation(sourceFile: ts.SourceFile): CodeElement[] {
    const elements: CodeElement[] = [];
    
    const visit = (node: ts.Node) => {
      // Extract function signatures with type information
      if (ts.isFunctionDeclaration(node) && node.name) {
        const symbol = this.typeChecker!.getSymbolAtLocation(node.name);
        if (symbol) {
          const type = this.typeChecker!.getTypeOfSymbolAtLocation(symbol, node);
          const signature = this.typeChecker!.typeToString(type);
          
          // This would enhance the existing function element with type info
          // Implementation would merge with JavaScript plugin results
        }
      }
      
      // Extract class information with type details
      if (ts.isClassDeclaration(node) && node.name) {
        const symbol = this.typeChecker!.getSymbolAtLocation(node.name);
        if (symbol) {
          // Extract class type information, inheritance, etc.
        }
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    return elements;
  }
  
  /**
   * Extract decorator information
   */
  private extractDecoratorInformation(sourceFile: ts.SourceFile): CodeElement[] {
    const elements: CodeElement[] = [];
    
    const visit = (node: ts.Node) => {
      if (ts.getDecorators(node)) {
        const decorators = ts.getDecorators(node);
        for (const decorator of decorators || []) {
          // Extract decorator information
          // This would create NestJSDecoratorElement instances
        }
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    return elements;
  }
  
  /**
   * Extract interface and type alias information
   */
  private extractInterfaceInformation(sourceFile: ts.SourceFile): CodeElement[] {
    const elements: CodeElement[] = [];
    
    const visit = (node: ts.Node) => {
      if (ts.isInterfaceDeclaration(node)) {
        // Extract interface details with member types
      }
      
      if (ts.isTypeAliasDeclaration(node)) {
        // Extract type alias details
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    return elements;
  }
  
  /**
   * Merge JavaScript syntax analysis with TypeScript semantic analysis
   */
  private mergeAnalysisResults(jsElements: CodeElement[], tsEnhancements: any): CodeElement[] {
    // Merge the results, enhancing JavaScript elements with TypeScript type information
    return jsElements.map(element => {
      // Find corresponding TypeScript enhancement
      const enhancement = tsEnhancements.elements.find((e: CodeElement) => 
        e.name === element.name && e.location.startLine === element.location.startLine
      );
      
      if (enhancement) {
        return {
          ...element,
          metadata: {
            ...element.metadata,
            ...enhancement.metadata,
            hasTypeInformation: true
          }
        };
      }
      
      return element;
    });
  }
  
  /**
   * Find tsconfig.json in project
   */
  private findTsConfig(projectRoot: string): string | null {
    const tsconfigPath = path.join(projectRoot, 'tsconfig.json');
    if (fs.existsSync(tsconfigPath)) {
      return tsconfigPath;
    }
    
    // Look in parent directories
    let currentDir = projectRoot;
    while (currentDir !== path.dirname(currentDir)) {
      currentDir = path.dirname(currentDir);
      const parentTsconfig = path.join(currentDir, 'tsconfig.json');
      if (fs.existsSync(parentTsconfig)) {
        return parentTsconfig;
      }
    }
    
    return null;
  }
}

/**
 * TypeScript-specific filtering strategy
 */
export class TypeScriptFilteringStrategy implements PluginFilteringStrategy {
  
  extractRelevantContext(
    focusFile: string,
    allResults: FileAnalysisResult[],
    options: FilteringOptions
  ): any {
    // Extract TypeScript-specific context
    return {
      types: this.extractTypeDefinitions(allResults),
      interfaces: this.extractInterfaces(allResults),
      decorators: this.extractDecorators(allResults),
      generics: this.extractGenericUsage(allResults),
      inheritance: this.extractInheritanceChains(allResults)
    };
  }
  
  scoreRelevance(element: CodeElement, focusFile: string, context: any): number {
    let score = 0;
    
    // Higher relevance for type definitions
    if (element.type === CodeElementType.Interface || element.type === CodeElementType.Type) {
      score += 0.4;
    }
    
    // Higher relevance for decorated elements (likely framework-specific)
    if (element.metadata?.hasDecorators) {
      score += 0.3;
    }
    
    // Higher relevance for generic types
    if (element.metadata?.isGeneric) {
      score += 0.2;
    }
    
    return Math.min(score, 1.0);
  }
  
  generateSummary(relevantElements: CodeElement[], context: any): any {
    return {
      type: 'typescript-summary',
      interfaces: relevantElements.filter(e => e.type === CodeElementType.Interface).length,
      types: relevantElements.filter(e => e.type === CodeElementType.Type).length,
      decoratedElements: relevantElements.filter(e => e.metadata?.hasDecorators).length,
      hasTypeInformation: true
    };
  }
  
  private extractTypeDefinitions(allResults: FileAnalysisResult[]) {
    return allResults.flatMap(file => 
      file.elements.filter(e => e.type === CodeElementType.Type)
    );
  }
  
  private extractInterfaces(allResults: FileAnalysisResult[]) {
    return allResults.flatMap(file => 
      file.elements.filter(e => e.type === CodeElementType.Interface)
    );
  }
  
  private extractDecorators(allResults: FileAnalysisResult[]) {
    return allResults.flatMap(file => 
      file.elements.filter(e => e.metadata?.hasDecorators)
    );
  }
  
  private extractGenericUsage(allResults: FileAnalysisResult[]) {
    return allResults.flatMap(file => 
      file.elements.filter(e => e.metadata?.isGeneric)
    );
  }
  
  private extractInheritanceChains(allResults: FileAnalysisResult[]) {
    // Extract class inheritance and interface implementation chains
    return [];
  }
}
