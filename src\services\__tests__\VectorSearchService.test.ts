import { VectorSearchService } from '../VectorSearchService.js';
import { EmbeddedCodeChunk, SearchResult, CodeElementType, Location, MetadataFilters } from '../../types/CodeElement.js';

const createMockChunk = (
    id: string, 
    embedding: number[], 
    filePath: string = 'test.ts', 
    type: CodeElementType = CodeElementType.FUNCTION,
    name: string = 'testFunc',
    startLine: number = 1
): EmbeddedCodeChunk => ({
    name,
    type,
    filePath,
    location: { startLine, endLine: startLine + 2, startColumn: 0, endColumn: 10 },
    fullText: `mock code for ${id}`,
    embedding,
    embeddingModel: 'test-model',
    id: `id-${id}-${filePath}-${name}-${startLine}`, // Ensure unique ID for replacement tests
    summary: `Summary for ${id}`
});

describe('VectorSearchService', () => {
    let service: VectorSearchService;

    beforeEach(() => {
        service = new VectorSearchService();
    });

    const chunk1: EmbeddedCodeChunk = createMockChunk('chunk1', [1, 0, 0, 0], 'file1.ts', CodeElementType.FUNCTION, 'funcA', 10);
    const chunk2: EmbeddedCodeChunk = createMockChunk('chunk2', [0, 1, 0, 0], 'file1.ts', CodeElementType.CLASS, 'ClassB', 20);
    const chunk3: EmbeddedCodeChunk = createMockChunk('chunk3', [0.707, 0.707, 0, 0], 'file2.ts', CodeElementType.FUNCTION, 'funcC', 30); // Similar to [1,1,0,0]
    const chunk4: EmbeddedCodeChunk = createMockChunk('chunk4', [0, 0, 1, 0], 'file2.ts', CodeElementType.INTERFACE, 'InterfaceD', 40);
    const chunkWithNoEmbedding: EmbeddedCodeChunk = {
        name: 'noEmbedFunc',
        type: CodeElementType.FUNCTION,
        filePath: 'file3.ts',
        location: { startLine: 1, endLine: 3, startColumn: 0, endColumn: 10 },
        fullText: 'function noEmbedFunc() {}',
        embedding: [], // Empty embedding
        embeddingModel: 'test-model',
        id: 'id-noEmbedFunc-file3.ts-noEmbedFunc-1',
        summary: 'No embedding'
    };

    describe('indexChunks', () => {
        it('should add new chunks to the index', async () => {
            await service.indexChunks([chunk1, chunk2]);
            expect(service.getIndexSize()).toBe(2);
        });

        it('should replace existing chunks with the same unique identifier (filePath, name, startLine)', async () => {
            await service.indexChunks([chunk1]);
            const updatedChunk1: EmbeddedCodeChunk = createMockChunk('chunk1-updated', [1, 1, 1, 1], chunk1.filePath, chunk1.type, chunk1.name, chunk1.location.startLine);
            await service.indexChunks([updatedChunk1]);
            expect(service.getIndexSize()).toBe(1);
            const results = await service.search([1,1,1,1], 1);
            expect(results[0].chunk.embedding).toEqual([1,1,1,1]);
        });
    });

    describe('clearIndex', () => {
        it('should remove all chunks from the index', async () => {
            await service.indexChunks([chunk1, chunk2]);
            expect(service.getIndexSize()).toBe(2);
            await service.clearIndex();
            expect(service.getIndexSize()).toBe(0);
        });
    });

    describe('cosineSimilarity', () => {
        // Access private method for testing - not ideal, but useful for direct validation
        const cosineSimilarity = (service as any).cosineSimilarity.bind(service);

        it('should return 1 for identical vectors', () => {
            expect(cosineSimilarity([1, 2, 3], [1, 2, 3])).toBeCloseTo(1);
        });

        it('should return 0 for orthogonal vectors', () => {
            expect(cosineSimilarity([1, 0, 0], [0, 1, 0])).toBeCloseTo(0);
        });

        it('should return -1 for opposite vectors', () => {
            expect(cosineSimilarity([1, 2, 3], [-1, -2, -3])).toBeCloseTo(-1);
        });

        it('should calculate similarity correctly for known vectors', () => {
            expect(cosineSimilarity([1, 1], [1, 0])).toBeCloseTo(0.7071); // cos(45 deg)
        });

        it('should return 0 if one vector is all zeros', () => {
            expect(cosineSimilarity([1, 2, 3], [0, 0, 0])).toBe(0);
        });

        it('should return 0 if vectors have different lengths', () => {
            const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
            expect(cosineSimilarity([1, 2], [1, 2, 3])).toBe(0);
            // expect(consoleWarnSpy).toHaveBeenCalledWith('Vectors have different lengths, cannot compute similarity.');
            consoleWarnSpy.mockRestore();
        });
    });

    describe('search', () => {
        beforeEach(async () => {
            await service.indexChunks([chunk1, chunk2, chunk3, chunk4, chunkWithNoEmbedding]);
        });

        it('should return top K most similar chunks', async () => {
            const queryVector = [1, 0, 0, 0]; // Most similar to chunk1
            const results = await service.search(queryVector, 2);
            expect(results.length).toBe(2);
            expect(results[0].chunk.name).toBe(chunk1.name);
            expect(results[0].similarity).toBeCloseTo(1);
            expect(results[1].chunk.name).toBe(chunk3.name); // chunk3 is [0.707, 0.707, 0, 0]
            expect(results[1].similarity).toBeCloseTo(0.7071); 
        });

        it('should return empty array if index is empty', async () => {
            await service.clearIndex();
            const results = await service.search([1,0,0,0], 1);
            expect(results).toEqual([]);
        });

        it('should return empty array if query vector is empty', async () => {
            const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
            const results = await service.search([], 1);
            expect(results).toEqual([]);
            expect(consoleWarnSpy).toHaveBeenCalledWith('Query vector is empty. Cannot perform search.');
            consoleWarnSpy.mockRestore();
        });

        it('should filter by filePath', async () => {
            const queryVector = [0.5, 0.5, 0.5, 0.5]; // Generic query
            const filters: MetadataFilters = { filePath: 'file1.ts' };
            const results = await service.search(queryVector, 2, filters);
            expect(results.length).toBe(2);
            results.forEach(r => expect(r.chunk.filePath).toBe('file1.ts'));
            expect(results.map(r => r.chunk.name).sort()).toEqual([chunk1.name, chunk2.name].sort());
        });

        it('should filter by elementType', async () => {
            const queryVector = [0.5, 0.5, 0.5, 0.5];
            const filters: MetadataFilters = { elementType: CodeElementType.FUNCTION };
            const results = await service.search(queryVector, 2, filters);
            expect(results.length).toBe(2);
            results.forEach(r => expect(r.chunk.type).toBe(CodeElementType.FUNCTION));
            // chunk1 and chunk3 are functions
            const names = results.map(r => r.chunk.name).sort();
            expect(names).toContain(chunk1.name);
            expect(names).toContain(chunk3.name);
        });

        it('should filter by minSimilarity', async () => {
            const queryVector = [1, 0, 0, 0]; // Exact match for chunk1
            const filters: MetadataFilters = { minSimilarity: 0.9 };
            const results = await service.search(queryVector, 5, filters);
            expect(results.length).toBe(1);
            expect(results[0].chunk.name).toBe(chunk1.name);
            expect(results[0].similarity).toBeCloseTo(1);
        });

        it('should combine multiple filters', async () => {
            const queryVector = [0.7, 0.7, 0, 0]; // Similar to chunk3
            const filters: MetadataFilters = {
                filePath: 'file2.ts',
                elementType: CodeElementType.FUNCTION,
                minSimilarity: 0.95
            };
            const results = await service.search(queryVector, 1, filters);
            expect(results.length).toBe(1);
            expect(results[0].chunk.name).toBe(chunk3.name);
            expect(results[0].similarity).toBeCloseTo(1);
        });

        it('should skip chunks with no embeddings during search', async () => {
            const queryVector = [0,0,0,1]; // some query
            // chunkWithNoEmbedding has an empty embedding array
            const results = await service.search(queryVector, 5);
            // Ensure chunkWithNoEmbedding is not in results
            expect(results.find(r => r.chunk.name === chunkWithNoEmbedding.name)).toBeUndefined();
            expect(service.getIndexSize()).toBe(5); // It's still in the index
        });

        it('should return fewer than topK if not enough matches', async () => {
            await service.clearIndex();
            await service.indexChunks([chunk1]);
            const results = await service.search([1,0,0,0], 5);
            expect(results.length).toBe(1);
        });
    });

    describe('getIndexSize', () => {
        it('should return the correct number of indexed chunks', async () => {
            expect(service.getIndexSize()).toBe(0);
            await service.indexChunks([chunk1, chunk2]);
            expect(service.getIndexSize()).toBe(2);
            await service.indexChunks([chunk3]);
            expect(service.getIndexSize()).toBe(3);
            await service.clearIndex();
            expect(service.getIndexSize()).toBe(0);
        });
    });
});
