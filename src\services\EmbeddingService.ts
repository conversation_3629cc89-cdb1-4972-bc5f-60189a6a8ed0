import { GoogleGenerativeAI, GenerativeModel } from "@google/generative-ai";
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { CodeElement, EmbeddedCodeChunk, CodeElementType } from '../types/CodeElement.js';

// Configure environment variables for API Key
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, '..', '..', '.env') }); // Adjust path to .env at project root

const GEMINI_EMBEDDING_MODEL = 'embedding-001'; // Or the latest recommended model

export class EmbeddingService {
    private genAI: GoogleGenerativeAI;
    private model: GenerativeModel;
    private apiKey: string;

    constructor() {
        this.apiKey = process.env.GEMINI_API_KEY || '';
        if (!this.apiKey) {
            console.warn('GEMINI_API_KEY not found in .env file. EmbeddingService will not be able to generate embeddings.');
            // Optionally throw an error if API key is strictly required for initialization
            // throw new Error('GEMINI_API_KEY not found in .env file.');
        }
        this.genAI = new GoogleGenerativeAI(this.apiKey);
        this.model = this.genAI.getGenerativeModel({ model: GEMINI_EMBEDDING_MODEL });
    }

    /**
     * Generates embeddings for a list of code elements.
     * @param codeElements - An array of CodeElement objects.
     * @returns A promise that resolves to an array of EmbeddedCodeChunk objects.
     */
    async generateEmbeddings(codeElements: CodeElement[]): Promise<EmbeddedCodeChunk[]> {
        if (!this.apiKey) {
            console.error('Cannot generate embeddings: GEMINI_API_KEY is not configured.');
            return []; // Or throw error
        }

        const embeddedChunks: EmbeddedCodeChunk[] = [];

        for (const element of codeElements) {
            if (!element.fullText || element.fullText.trim() === '') {
                // console.warn(`Skipping element without fullText: ${element.name} in ${element.filePath}`);
                continue;
            }

            try {
                // TODO: Implement more sophisticated chunking for very long fullText if necessary.
                // For now, we embed the entire fullText of the CodeElement.
                const contentToEmbed = element.fullText;

                const result = await this.model.embedContent(contentToEmbed);
                const embedding = result.embedding.values;

                if (embedding && embedding.length > 0) {
                    embeddedChunks.push({
                        ...element, // Spread all properties from CodeElement
                        embedding: embedding,
                        embeddingModel: GEMINI_EMBEDDING_MODEL,
                    });
                } else {
                    console.warn(`Failed to generate embedding for element: ${element.name} in ${element.filePath}`);
                }
            } catch (error) {
                console.error(`Error generating embedding for element ${element.name} in ${element.filePath}:`, error);
                // Optionally, collect these errors and return them
            }
        }
        return embeddedChunks;
    }

    /**
     * Generates an embedding for a single query string.
     * @param queryText - The text to embed.
     * @returns A promise that resolves to an array of numbers (the embedding) or null if an error occurs.
     */
    async embedQuery(queryText: string): Promise<number[] | null> {
        if (!this.apiKey) {
            console.error('Cannot embed query: GEMINI_API_KEY is not configured.');
            return null;
        }
        if (!queryText || queryText.trim() === '') {
            console.warn('Cannot embed empty query text.');
            return null;
        }

        try {
            const result = await this.model.embedContent(queryText);
            return result.embedding.values;
        } catch (error) {
            console.error('Error generating embedding for query:', error);
            return null;
        }
    }
}
