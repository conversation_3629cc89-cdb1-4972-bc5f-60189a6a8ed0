/**
 * Decorators Feature Plugin
 * 
 * Generic decorator analysis that can be used by framework plugins.
 * Handles TypeScript/JavaScript decorators across different frameworks.
 */

import { 
  AnalysisPlugin, 
  PluginMetadata, 
  PluginCapabilities, 
  PluginAnalysisContext, 
  PluginAnalysisResult 
} from '../../core/PluginSystem.js';
import { CodeElement, CodeElementType } from '../../types/CodeElement.js';
import { DecoratorElement, DecoratorInfo } from './types/DecoratorTypes.js';
import { TreeSitterParser } from '../../parsers/TreeSitterParser.js';
import * as fs from 'fs/promises';
import * as path from 'path';

export class DecoratorsPlugin implements AnalysisPlugin {
  metadata: PluginMetadata = {
    name: 'decorators-feature',
    version: '1.0.0',
    description: 'Generic decorator analysis for TypeScript/JavaScript',
    author: 'Cyzer Team',
    
    languages: ['typescript', 'javascript'],
    frameworks: [], // Framework-agnostic feature
    fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],
    
    capabilities: {
      syntaxAnalysis: true,
      semanticAnalysis: false,
      crossFileAnalysis: false,
      typeInference: false,
      dependencyTracking: false,
      callGraphGeneration: false,
      frameworkPatterns: false,
      decoratorAnalysis: true,     // ✅ Primary capability
      componentAnalysis: false,
      incrementalAnalysis: false,
      largeCodebaseOptimized: true
    },
    
    priority: 150, // Medium priority - feature plugin
    dependencies: [], // No dependencies - can work standalone
    
    requiresTypeScript: false,
    requiresNodeModules: false,
    memoryIntensive: false
  };
  
  private parser: TreeSitterParser | null = null;
  private decoratorQueries: Map<string, string> = new Map();
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    this.parser = new TreeSitterParser();
    
    // Load decorator-specific queries
    await this.loadDecoratorQueries();
  }
  
  async cleanup(): Promise<void> {
    this.parser = null;
    this.decoratorQueries.clear();
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    // Handle any TypeScript/JavaScript file that might have decorators
    return (language === 'typescript' || language === 'javascript') &&
           this.metadata.fileExtensions.some(ext => filePath.endsWith(ext));
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    const startTime = Date.now();
    
    if (!this.parser) {
      throw new Error('Decorators plugin not initialized');
    }
    
    try {
      // Parse file for decorator patterns
      const decoratorElements = await this.extractDecorators(context);
      
      // Classify decorators by framework/purpose
      const classifiedDecorators = this.classifyDecorators(decoratorElements, context);
      
      // Share decorator information with other plugins
      const sharedData = new Map<string, any>();
      sharedData.set('decorators', classifiedDecorators);
      sharedData.set('decorator-metadata', this.extractDecoratorMetadata(classifiedDecorators));
      
      return {
        elements: classifiedDecorators,
        errors: [],
        warnings: [],
        metadata: {
          plugin: 'decorators-feature',
          decoratorCount: classifiedDecorators.length,
          frameworks: this.detectFrameworksFromDecorators(classifiedDecorators)
        },
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0.9,
        sharedData
      };
      
    } catch (error) {
      return {
        elements: [],
        errors: [`Decorator analysis failed: ${error}`],
        warnings: [],
        metadata: {},
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0,
      };
    }
  }
  
  /**
   * Load framework-specific decorator queries
   */
  private async loadDecoratorQueries(): Promise<void> {
    const queriesDir = path.join(__dirname, 'queries');
    
    try {
      // Load generic decorator query
      const genericQuery = await fs.readFile(
        path.join(queriesDir, 'decorators.scm'), 
        'utf-8'
      );
      this.decoratorQueries.set('generic', genericQuery);
      
      // Load framework-specific queries
      const frameworkQueries = ['nestjs-decorators.scm', 'angular-decorators.scm'];
      
      for (const queryFile of frameworkQueries) {
        try {
          const query = await fs.readFile(path.join(queriesDir, queryFile), 'utf-8');
          const framework = queryFile.replace('-decorators.scm', '');
          this.decoratorQueries.set(framework, query);
        } catch (error) {
          // Query file doesn't exist - that's okay
        }
      }
      
    } catch (error) {
      console.warn('Failed to load decorator queries:', error);
    }
  }
  
  /**
   * Extract decorators from the file
   */
  private async extractDecorators(context: PluginAnalysisContext): Promise<DecoratorElement[]> {
    const decorators: DecoratorElement[] = [];
    
    // Use tree-sitter to find decorator patterns
    const parseResult = this.parser!.parse(context.fileContent, context.filePath);
    
    // Filter for decorator elements
    const decoratorElements = parseResult.elements.filter(element => 
      element.name.startsWith('@') || 
      element.type.toString().includes('decorator')
    );
    
    // Convert to DecoratorElement format
    for (const element of decoratorElements) {
      const decoratorInfo = this.parseDecoratorInfo(element, context);
      
      const decoratorElement: DecoratorElement = {
        ...element,
        type: CodeElementType.NestJSDecorator, // Will be refined by classification
        decoratorName: decoratorInfo.name,
        arguments: decoratorInfo.arguments,
        target: decoratorInfo.target,
        framework: decoratorInfo.framework,
        metadata: {
          ...element.metadata,
          decoratorInfo
        }
      };
      
      decorators.push(decoratorElement);
    }
    
    return decorators;
  }
  
  /**
   * Parse decorator information from element
   */
  private parseDecoratorInfo(element: CodeElement, context: PluginAnalysisContext): DecoratorInfo {
    const decoratorName = element.name.replace('@', '');
    
    // Detect framework based on decorator name
    const framework = this.detectFrameworkFromDecorator(decoratorName);
    
    // Extract arguments (simplified - would need more sophisticated parsing)
    const arguments_ = this.extractDecoratorArguments(element);
    
    // Determine what the decorator is applied to
    const target = this.determineDecoratorTarget(element, context);
    
    return {
      name: decoratorName,
      framework,
      arguments: arguments_,
      target,
      isParameterDecorator: target === 'parameter',
      isMethodDecorator: target === 'method',
      isClassDecorator: target === 'class',
      isPropertyDecorator: target === 'property'
    };
  }
  
  /**
   * Classify decorators by framework and purpose
   */
  private classifyDecorators(decorators: DecoratorElement[], context: PluginAnalysisContext): DecoratorElement[] {
    return decorators.map(decorator => {
      const classified = { ...decorator };
      
      // Refine the element type based on framework
      if (decorator.framework === 'nestjs') {
        classified.type = this.getNestJSDecoratorType(decorator.decoratorName);
      } else if (decorator.framework === 'angular') {
        classified.type = this.getAngularDecoratorType(decorator.decoratorName);
      } else if (decorator.framework === 'react') {
        classified.type = this.getReactDecoratorType(decorator.decoratorName);
      }
      
      // Add classification metadata
      classified.metadata = {
        ...classified.metadata,
        classification: this.getDecoratorClassification(decorator.decoratorName),
        purpose: this.getDecoratorPurpose(decorator.decoratorName)
      };
      
      return classified;
    });
  }
  
  /**
   * Detect framework from decorator name
   */
  private detectFrameworkFromDecorator(decoratorName: string): string | undefined {
    // NestJS decorators
    const nestjsDecorators = [
      'Controller', 'Injectable', 'Module', 'Get', 'Post', 'Put', 'Delete',
      'UseGuards', 'UseInterceptors', 'UsePipes', 'Inject', 'Optional'
    ];
    
    // Angular decorators
    const angularDecorators = [
      'Component', 'Directive', 'Injectable', 'NgModule', 'Input', 'Output',
      'ViewChild', 'ContentChild', 'HostListener', 'HostBinding'
    ];
    
    // React decorators (less common, but exist)
    const reactDecorators = ['observer', 'inject', 'autobind'];
    
    if (nestjsDecorators.includes(decoratorName)) return 'nestjs';
    if (angularDecorators.includes(decoratorName)) return 'angular';
    if (reactDecorators.includes(decoratorName)) return 'react';
    
    return undefined; // Generic decorator
  }
  
  /**
   * Get NestJS-specific decorator type
   */
  private getNestJSDecoratorType(decoratorName: string): CodeElementType {
    const typeMap: { [key: string]: CodeElementType } = {
      'Controller': CodeElementType.NestJSController,
      'Injectable': CodeElementType.NestJSService,
      'Module': CodeElementType.NestJSModule,
      'Get': CodeElementType.NestJSEndpoint,
      'Post': CodeElementType.NestJSEndpoint,
      'Put': CodeElementType.NestJSEndpoint,
      'Delete': CodeElementType.NestJSEndpoint,
      'UseGuards': CodeElementType.NestJSGuard,
      'UseInterceptors': CodeElementType.NestJSInterceptor,
      'UsePipes': CodeElementType.NestJSPipe,
      'Inject': CodeElementType.NestJSInjection
    };
    
    return typeMap[decoratorName] || CodeElementType.NestJSDecorator;
  }
  
  /**
   * Get Angular-specific decorator type (for future use)
   */
  private getAngularDecoratorType(decoratorName: string): CodeElementType {
    // Would define Angular-specific types
    return CodeElementType.NestJSDecorator; // Placeholder
  }
  
  /**
   * Get React-specific decorator type (for future use)
   */
  private getReactDecoratorType(decoratorName: string): CodeElementType {
    // Would define React-specific types
    return CodeElementType.NestJSDecorator; // Placeholder
  }
  
  /**
   * Get decorator classification (HTTP, DI, Validation, etc.)
   */
  private getDecoratorClassification(decoratorName: string): string {
    const httpDecorators = ['Get', 'Post', 'Put', 'Delete', 'Patch', 'Options', 'Head'];
    const diDecorators = ['Injectable', 'Inject', 'Optional'];
    const validationDecorators = ['IsString', 'IsNumber', 'IsEmail', 'Min', 'Max'];
    const routingDecorators = ['Controller', 'Route'];
    
    if (httpDecorators.includes(decoratorName)) return 'http';
    if (diDecorators.includes(decoratorName)) return 'dependency-injection';
    if (validationDecorators.includes(decoratorName)) return 'validation';
    if (routingDecorators.includes(decoratorName)) return 'routing';
    
    return 'other';
  }
  
  /**
   * Get decorator purpose description
   */
  private getDecoratorPurpose(decoratorName: string): string {
    const purposes: { [key: string]: string } = {
      'Controller': 'Defines a REST API controller',
      'Injectable': 'Marks class as injectable service',
      'Module': 'Defines application module',
      'Get': 'Handles HTTP GET requests',
      'Post': 'Handles HTTP POST requests',
      'Inject': 'Injects dependency',
      'UseGuards': 'Applies authentication/authorization guards'
    };
    
    return purposes[decoratorName] || 'Generic decorator';
  }
  
  // Helper methods (simplified implementations)
  private extractDecoratorArguments(element: CodeElement): any[] {
    // Would parse decorator arguments from fullText
    return [];
  }
  
  private determineDecoratorTarget(element: CodeElement, context: PluginAnalysisContext): string {
    // Would analyze the AST to determine what the decorator is applied to
    return 'unknown';
  }
  
  private extractDecoratorMetadata(decorators: DecoratorElement[]): any {
    return {
      totalDecorators: decorators.length,
      byFramework: this.groupDecoratorsByFramework(decorators),
      byClassification: this.groupDecoratorsByClassification(decorators)
    };
  }
  
  private detectFrameworksFromDecorators(decorators: DecoratorElement[]): string[] {
    const frameworks = new Set<string>();
    decorators.forEach(d => {
      if (d.framework) frameworks.add(d.framework);
    });
    return Array.from(frameworks);
  }
  
  private groupDecoratorsByFramework(decorators: DecoratorElement[]): { [framework: string]: number } {
    const groups: { [framework: string]: number } = {};
    decorators.forEach(d => {
      const framework = d.framework || 'generic';
      groups[framework] = (groups[framework] || 0) + 1;
    });
    return groups;
  }
  
  private groupDecoratorsByClassification(decorators: DecoratorElement[]): { [classification: string]: number } {
    const groups: { [classification: string]: number } = {};
    decorators.forEach(d => {
      const classification = d.metadata?.classification || 'other';
      groups[classification] = (groups[classification] || 0) + 1;
    });
    return groups;
  }
}
