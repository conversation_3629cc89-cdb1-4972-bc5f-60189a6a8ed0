import { EmbeddingService } from '../EmbeddingService.js';
import { CodeElement, EmbeddedCodeChunk, CodeElementType, Location } from '../../types/CodeElement.js';
import { GoogleGenerativeAI, GenerativeModel } from '@google/generative-ai';

// Mock the @google/generative-ai module
const mockEmbedContent = jest.fn();
const mockGetGenerativeModel = jest.fn(() => ({
    embedContent: mockEmbedContent,
}));

jest.mock('@google/generative-ai', () => ({
    GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
        getGenerativeModel: mockGetGenerativeModel,
    })),
}));

// Mock dotenv to prevent it from trying to load .env during tests
// and to control environment variables like GEMINI_API_KEY
const mockDotenvConfig = jest.fn();
jest.mock('dotenv', () => ({
    config: mockDotenvConfig,
}));


describe('EmbeddingService', () => {
    let originalApiKey: string | undefined;
    let consoleWarnSpy: jest.SpyInstance;
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        // Store original API key and clear it for tests
        originalApiKey = process.env.GEMINI_API_KEY;
        // Reset mocks before each test
        mockEmbedContent.mockReset();
        mockGetGenerativeModel.mockClear();
        (GoogleGenerativeAI as jest.Mock).mockClear();
        mockDotenvConfig.mockClear();
        // Set a default mock API key for most tests
        process.env.GEMINI_API_KEY = 'test-api-key';

        consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
        consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
        // Restore original API key
        process.env.GEMINI_API_KEY = originalApiKey;
        consoleWarnSpy.mockRestore();
        consoleErrorSpy.mockRestore();
    });

    const sampleCodeElements: CodeElement[] = [
        {
            name: 'functionA',
            type: CodeElementType.FUNCTION,
            filePath: 'test.js',
            location: { startLine: 1, endLine: 3, startColumn: 0, endColumn: 10 },
            fullText: 'function functionA() { return 1; }',
            summary: 'A test function',
            parameters: [],
            returnType: 'number',
        },
        {
            name: 'classB',
            type: CodeElementType.CLASS,
            filePath: 'test.js',
            location: { startLine: 5, endLine: 10, startColumn: 0, endColumn: 10 },
            fullText: 'class classB { constructor() {} }',
            summary: 'A test class',
        },
        {
            name: 'emptyFunction',
            type: CodeElementType.FUNCTION,
            filePath: 'test.js',
            location: { startLine: 12, endLine: 12, startColumn: 0, endColumn: 20 },
            fullText: '', // Empty fullText
            summary: 'An empty function',
        },
    ];

    describe('Constructor', () => {
        it('should initialize GoogleGenerativeAI with API key', () => {
            new EmbeddingService();
            expect(GoogleGenerativeAI).toHaveBeenCalledWith('test-api-key');
            expect(mockGetGenerativeModel).toHaveBeenCalledWith({ model: 'embedding-001' });
        });

        it('should warn if GEMINI_API_KEY is not set', () => {
            delete process.env.GEMINI_API_KEY;
            new EmbeddingService(); // Constructor will try to access process.env.GEMINI_API_KEY
            expect(consoleWarnSpy).toHaveBeenCalledWith('GEMINI_API_KEY not found in .env file. EmbeddingService will not be able to generate embeddings.');
            // Even if API key is missing, the constructor for GoogleGenerativeAI might still be called with undefined or empty string
            expect(GoogleGenerativeAI).toHaveBeenCalledWith('');
        });
    });

    describe('generateEmbeddings', () => {
        it('should generate embeddings for valid code elements', async () => {
            const service = new EmbeddingService();
            const mockEmbedding1 = { embedding: { values: [0.1, 0.2] } };
            const mockEmbedding2 = { embedding: { values: [0.3, 0.4] } };
            mockEmbedContent.mockResolvedValueOnce(mockEmbedding1).mockResolvedValueOnce(mockEmbedding2);

            const validElements = sampleCodeElements.filter(el => el.fullText && el.fullText.trim() !== '');
            const results = await service.generateEmbeddings(validElements);

            expect(results.length).toBe(2);
            expect(mockEmbedContent).toHaveBeenCalledTimes(2);
            expect(mockEmbedContent).toHaveBeenCalledWith(validElements[0].fullText);
            expect(mockEmbedContent).toHaveBeenCalledWith(validElements[1].fullText);
            expect(results[0].embedding).toEqual(mockEmbedding1.embedding.values);
            expect(results[0].embeddingModel).toBe('embedding-001');
            expect(results[0].name).toBe(validElements[0].name);
            expect(results[1].embedding).toEqual(mockEmbedding2.embedding.values);
        });

        it('should skip elements with empty fullText', async () => {
            const service = new EmbeddingService();
            mockEmbedContent.mockResolvedValue({ embedding: { values: [0.1, 0.2] } }); // Only one call expected

            const results = await service.generateEmbeddings(sampleCodeElements); // Contains one empty element

            expect(results.length).toBe(2); // Only two non-empty elements
            expect(mockEmbedContent).toHaveBeenCalledTimes(2);
        });

        it('should handle API errors gracefully', async () => {
            const service = new EmbeddingService();
            mockEmbedContent.mockRejectedValueOnce(new Error('API Error'));
            mockEmbedContent.mockResolvedValueOnce({ embedding: { values: [0.3, 0.4] } }); // Second call succeeds

            const validElements = sampleCodeElements.filter(el => el.fullText && el.fullText.trim() !== '');
            const results = await service.generateEmbeddings(validElements);

            expect(results.length).toBe(1); // Only one succeeded
            expect(results[0].name).toBe(validElements[1].name);
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                `Error generating embedding for element ${validElements[0].name} in ${validElements[0].filePath}:`, 
                expect.any(Error)
            );
        });

        it('should return empty array and log error if API key is missing', async () => {
            delete process.env.GEMINI_API_KEY;
            const service = new EmbeddingService(); // Re-initialize to pick up missing key
            const results = await service.generateEmbeddings(sampleCodeElements);
            expect(results).toEqual([]);
            expect(consoleErrorSpy).toHaveBeenCalledWith('Cannot generate embeddings: GEMINI_API_KEY is not configured.');
        });
    });

    describe('embedQuery', () => {
        it('should embed a valid query string', async () => {
            const service = new EmbeddingService();
            const mockEmbedding = { embedding: { values: [0.5, 0.6] } };
            mockEmbedContent.mockResolvedValue(mockEmbedding);

            const query = 'test query';
            const result = await service.embedQuery(query);

            expect(result).toEqual(mockEmbedding.embedding.values);
            expect(mockEmbedContent).toHaveBeenCalledWith(query);
        });

        it('should return null for an empty query string', async () => {
            const service = new EmbeddingService();
            const result = await service.embedQuery('');
            expect(result).toBeNull();
            expect(consoleWarnSpy).toHaveBeenCalledWith('Cannot embed empty query text.');
            expect(mockEmbedContent).not.toHaveBeenCalled();
        });

        it('should handle API errors gracefully and return null', async () => {
            const service = new EmbeddingService();
            mockEmbedContent.mockRejectedValue(new Error('API Error for query'));

            const result = await service.embedQuery('some query');
            expect(result).toBeNull();
            expect(consoleErrorSpy).toHaveBeenCalledWith('Error generating embedding for query:', expect.any(Error));
        });

        it('should return null and log error if API key is missing', async () => {
            delete process.env.GEMINI_API_KEY;
            const service = new EmbeddingService(); // Re-initialize
            const result = await service.embedQuery('query');
            expect(result).toBeNull();
            expect(consoleErrorSpy).toHaveBeenCalledWith('Cannot embed query: GEMINI_API_KEY is not configured.');
        });
    });
});
