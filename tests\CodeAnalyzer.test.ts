import { CodeAnalyzer } from '../src/core/CodeAnalyzer';
import { CodeElementType } from '../src/types/CodeElement';
import path from 'path';
import fs from 'fs/promises';

describe('CodeAnalyzer', () => {
    let analyzer: CodeAnalyzer;
    const testProjectRoot = path.resolve(__dirname, 'fixtures', 'sample-project');

    beforeAll(async () => {
        await fs.mkdir(path.join(testProjectRoot, 'src'), { recursive: true });
        await fs.writeFile(path.join(testProjectRoot, 'src', 'moduleA.js'), `
            import { utilFunc } from './utils/helper.js';
            import * as D from './data.ts';
            import defaultExport from './moduleB.ts';

            export function funcA() { console.log('FuncA'); utilFunc(); }
            export const varA = 10;

            funcA();
            defaultExport();
            console.log(D.dataValue);
        `);
        // ... rest of test setup ...
    });

    afterAll(async () => {
        await fs.rm(testProjectRoot, { recursive: true, force: true });
    });

    it('trivial test: jest should run this', () => {
        console.log('Trivial test running');
        expect(1).toBe(1);
    });

    it('should detect exported functions in a simple JS file', async () => {
        console.log('Analyzer test starting');
        analyzer = new CodeAnalyzer(testProjectRoot);
        await analyzer.initialize();
        const results = await analyzer.analyzeProject();
        const allElements = results.files.flatMap(file => file.elements);

        // Log analyzed files
        console.log('Analyzed files:', results.files.map(f => f.filePath));
        // Log all detected elements for debugging
        console.log('Detected elements:', JSON.stringify(allElements, null, 2));
        // Check that funcA is detected as an exported function
        const funcA = allElements.find(e => e.type === CodeElementType.Function && e.name === 'funcA' && e.isExported);
        expect(funcA).toBeDefined();
    });

    it('should detect elements in advanced.js', async () => {
        analyzer = new CodeAnalyzer(path.resolve(__dirname, '../test-files'));
        await analyzer.initialize();
        const projectResult = await analyzer.analyzeProject();
        const fileResult = projectResult.files.find(f => f.filePath.endsWith('advanced.js'));
        expect(fileResult).toBeDefined();
        const elements = fileResult!.elements;
        console.error('advanced.js elements:', JSON.stringify(elements, null, 2));
        expect(elements.some((e: any) => e.name === 'exportedFunc' && e.type === CodeElementType.Function && e.isExported)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'localFunc' && e.type === CodeElementType.Function)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'arrowFunc' && e.type === CodeElementType.Function)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'ExportedClass' && e.type === CodeElementType.Class && e.isExported)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'LocalClass' && e.type === CodeElementType.Class)).toBeTruthy();
    });

    it('should detect elements in advanced.ts', async () => {
        analyzer = new CodeAnalyzer(path.resolve(__dirname, '../test-files'));
        await analyzer.initialize();
        const projectResult = await analyzer.analyzeProject();
        const fileResult = projectResult.files.find(f => f.filePath.endsWith('advanced.ts'));
        expect(fileResult).toBeDefined();
        const elements = fileResult!.elements;
        console.error('advanced.ts elements:', JSON.stringify(elements, null, 2));
        expect(elements.some((e: any) => e.name === 'MyClass' && e.type === CodeElementType.Class && e.isExported)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'MyInterface' && e.type === CodeElementType.Interface && e.isExported)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'MyType' && e.type === CodeElementType.Type && e.isExported)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'MyEnum' && e.type === CodeElementType.Enum && e.isExported)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'arrow' && e.type === CodeElementType.Function && e.isExported)).toBeTruthy();
        expect(elements.some((e: any) => e.name === 'generatorFunc' && e.type === CodeElementType.Function && e.isExported)).toBeTruthy();
    });

    it('should detect imports/exports and relations in mini-repo', async () => {
        analyzer = new CodeAnalyzer(path.resolve(__dirname, '../test-files/mini-repo'));
        await analyzer.initialize();
        const projectResult = await analyzer.analyzeProject();
        const files = projectResult.files;
        // Check that all files are analyzed
        expect(files.some(f => f.filePath.endsWith('a.js'))).toBeTruthy();
        expect(files.some(f => f.filePath.endsWith('b.js'))).toBeTruthy();
        expect(files.some(f => f.filePath.endsWith('c.js'))).toBeTruthy();
        expect(files.some(f => f.filePath.endsWith('index.js'))).toBeTruthy();
        // Check that imports/exports are detected
        const aFile = files.find(f => f.filePath.endsWith('a.js'))!;
        console.error('mini-repo a.js elements:', JSON.stringify(aFile.elements, null, 2));
        const importElements = aFile.elements.filter((e: any) => e.type === 'import');
        console.error('mini-repo a.js imports:', JSON.stringify(importElements, null, 2));
        expect(aFile.elements.some((e: any) => e.name === 'A' && e.isExported)).toBeTruthy();
        expect(aFile.elements.some((e: any) => e.name === 'aFunc' && e.isExported)).toBeTruthy();
        // Print all import elements in all files for better debugging
        files.forEach(f => {
            const importElements = f.elements.filter((e: any) => e.type === 'import');
            if (importElements.length > 0) {
                console.error(`mini-repo ${f.filePath} imports:`, JSON.stringify(importElements, null, 2));
            }
        });
        // Relations/edges: summary should exist in projectResult.relations
        expect(projectResult.relations).toBeDefined();
        console.error('mini-repo relations:', JSON.stringify(projectResult.relations, null, 2));
        if (projectResult.relations && Array.isArray(projectResult.relations.edges)) {
            const edge = projectResult.relations.edges.find((edge: any) => edge.source && edge.target && edge.source.includes('a.js') && edge.target.includes('b.js'));
            expect(edge).toBeDefined();
        } else {
            throw new Error('projectResult.relations.edges is not an array: ' + JSON.stringify(projectResult.relations));
        }
    });

    // ... rest of test cases ...
});
