{"name": "code-analyzer", "version": "1.0.0", "description": "## Project Overview", "main": "dist/index.js", "bin": {"cyzer": "dist/index.js"}, "directories": {"doc": "docs"}, "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest --detectOpenHandles", "test-embeddings": "node --experimental-modules --loader ts-node/esm test-gemini-embeddings.js", "test-code-embeddings": "node --experimental-modules --loader ts-node/esm test-code-embeddings.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.1.3", "dotenv": "^16.5.0", "repomix": "^0.3.1"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/yargs": "^17.0.32", "cross-env": "^7.0.3", "glob": "^11.0.1", "graphology": "^0.26.0", "graphology-graphml": "^0.5.2", "ignore": "^7.0.3", "jest": "^29.7.0", "tree-sitter": "^0.21.1", "tree-sitter-javascript": "^0.23.1", "tree-sitter-typescript": "^0.23.2", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "yargs": "^17.7.2"}}