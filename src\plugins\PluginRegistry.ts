/**
 * Plugin Registry - Manages all plugins and their dependencies
 * 
 * Demonstrates the granular plugin architecture with proper dependency management.
 */

import { AnalysisPlugin, PluginFilteringStrategy } from '../core/PluginSystem.js';

// Core language plugins
import { JavaScriptPlugin, JavaScriptFilteringStrategy } from './core/javascript/JavaScriptPlugin.js';
import { TypeScriptPlugin, TypeScriptFilteringStrategy } from './core/typescript/TypeScriptPlugin.js';

// Feature plugins
import { DecoratorsPlugin } from './features/decorators/DecoratorsPlugin.js';
import { TestingPlugin } from './features/testing/TestingPlugin.js';
import { SecurityPlugin } from './features/security/SecurityPlugin.js';

// Framework plugins
import { NestJSPlugin } from './frameworks/nestjs/NestJSPlugin.js';
import { ReactPlugin } from './frameworks/react/ReactPlugin.js';
import { ExpressPlugin } from './frameworks/express/ExpressPlugin.js';

// Filtering plugins
import { CoreFilteringPlugin } from './filtering/core/CoreFilteringPlugin.js';
import { NestJSFilteringPlugin } from './filtering/framework/NestJSFilteringPlugin.js';
import { ReactFilteringPlugin } from './filtering/framework/ReactFilteringPlugin.js';

/**
 * Plugin configuration for different project types
 */
export interface PluginConfiguration {
  name: string;
  description: string;
  plugins: string[];           // Plugin names to load
  filteringPlugins: string[];  // Filtering plugins to use
  priority: number;            // Configuration priority
}

/**
 * Predefined plugin configurations
 */
export const PLUGIN_CONFIGURATIONS: { [key: string]: PluginConfiguration } = {
  'nestjs-backend': {
    name: 'NestJS Backend',
    description: 'Full-stack NestJS backend analysis with DI, decorators, and API structure',
    plugins: [
      'javascript-core',      // Base JavaScript support
      'typescript-enhanced',  // TypeScript with Compiler API
      'decorators-feature',   // Decorator analysis
      'nestjs-framework',     // NestJS-specific analysis
      'testing-feature',      // Test analysis
      'security-feature'      // Security pattern analysis
    ],
    filteringPlugins: [
      'core-filtering',       // Base filtering
      'typescript-filtering', // TypeScript-specific filtering
      'nestjs-filtering'      // NestJS-specific filtering
    ],
    priority: 100
  },
  
  'react-frontend': {
    name: 'React Frontend',
    description: 'React application analysis with components, hooks, and state management',
    plugins: [
      'javascript-core',      // Base JavaScript support
      'typescript-enhanced',  // TypeScript support (optional)
      'react-framework',      // React-specific analysis
      'testing-feature'       // Test analysis
    ],
    filteringPlugins: [
      'core-filtering',       // Base filtering
      'react-filtering'       // React-specific filtering
    ],
    priority: 90
  },
  
  'fullstack-nestjs-react': {
    name: 'Full-Stack NestJS + React',
    description: 'Complete full-stack analysis for NestJS backend + React frontend',
    plugins: [
      'javascript-core',      // Base JavaScript support
      'typescript-enhanced',  // TypeScript with Compiler API
      'decorators-feature',   // Decorator analysis
      'nestjs-framework',     // NestJS backend analysis
      'react-framework',      // React frontend analysis
      'testing-feature',      // Test analysis
      'security-feature'      // Security analysis
    ],
    filteringPlugins: [
      'core-filtering',       // Base filtering
      'typescript-filtering', // TypeScript-specific filtering
      'nestjs-filtering',     // NestJS-specific filtering
      'react-filtering'       // React-specific filtering
    ],
    priority: 110
  },
  
  'express-api': {
    name: 'Express API',
    description: 'Express.js API analysis with routes, middleware, and security',
    plugins: [
      'javascript-core',      // Base JavaScript support
      'typescript-enhanced',  // TypeScript support (optional)
      'express-framework',    // Express-specific analysis
      'security-feature',     // Security analysis
      'testing-feature'       // Test analysis
    ],
    filteringPlugins: [
      'core-filtering',       // Base filtering
      'express-filtering'     // Express-specific filtering
    ],
    priority: 80
  },
  
  'basic-javascript': {
    name: 'Basic JavaScript',
    description: 'Basic JavaScript analysis without framework-specific features',
    plugins: [
      'javascript-core'       // Just basic JavaScript support
    ],
    filteringPlugins: [
      'core-filtering'        // Basic filtering only
    ],
    priority: 50
  }
};

/**
 * Enhanced Plugin Registry with configuration management
 */
export class EnhancedPluginRegistry {
  private plugins: Map<string, AnalysisPlugin> = new Map();
  private filteringStrategies: Map<string, PluginFilteringStrategy> = new Map();
  private pluginInstances: Map<string, any> = new Map();
  private currentConfiguration?: PluginConfiguration;
  
  /**
   * Load plugins based on configuration
   */
  async loadConfiguration(configName: string, projectRoot: string): Promise<void> {
    const config = PLUGIN_CONFIGURATIONS[configName];
    if (!config) {
      throw new Error(`Unknown plugin configuration: ${configName}`);
    }
    
    this.currentConfiguration = config;
    
    // Load analysis plugins
    for (const pluginName of config.plugins) {
      await this.loadPlugin(pluginName, projectRoot);
    }
    
    // Load filtering plugins
    for (const filteringPluginName of config.filteringPlugins) {
      await this.loadFilteringPlugin(filteringPluginName);
    }
    
    console.log(`Loaded configuration: ${config.name}`);
    console.log(`- Analysis plugins: ${config.plugins.length}`);
    console.log(`- Filtering plugins: ${config.filteringPlugins.length}`);
  }
  
  /**
   * Auto-detect and load appropriate configuration
   */
  async autoDetectConfiguration(projectRoot: string): Promise<string> {
    const detectedConfig = await this.detectProjectType(projectRoot);
    await this.loadConfiguration(detectedConfig, projectRoot);
    return detectedConfig;
  }
  
  /**
   * Load individual plugin
   */
  private async loadPlugin(pluginName: string, projectRoot: string): Promise<void> {
    try {
      let plugin: AnalysisPlugin;
      
      // Create plugin instances based on name
      switch (pluginName) {
        case 'javascript-core':
          plugin = new JavaScriptPlugin();
          break;
        case 'typescript-enhanced':
          plugin = new TypeScriptPlugin();
          break;
        case 'decorators-feature':
          plugin = new DecoratorsPlugin();
          break;
        case 'nestjs-framework':
          plugin = new NestJSPlugin();
          break;
        case 'react-framework':
          plugin = new ReactPlugin();
          break;
        case 'express-framework':
          plugin = new ExpressPlugin();
          break;
        case 'testing-feature':
          plugin = new TestingPlugin();
          break;
        case 'security-feature':
          plugin = new SecurityPlugin();
          break;
        default:
          console.warn(`Unknown plugin: ${pluginName}`);
          return;
      }
      
      // Initialize plugin
      await plugin.initialize(projectRoot);
      
      // Register plugin
      this.plugins.set(pluginName, plugin);
      this.pluginInstances.set(pluginName, plugin);
      
      console.log(`✅ Loaded plugin: ${pluginName}`);
      
    } catch (error) {
      console.warn(`❌ Failed to load plugin ${pluginName}:`, error);
    }
  }
  
  /**
   * Load filtering plugin
   */
  private async loadFilteringPlugin(filteringPluginName: string): Promise<void> {
    try {
      let filteringStrategy: PluginFilteringStrategy;
      
      // Create filtering strategy instances based on name
      switch (filteringPluginName) {
        case 'core-filtering':
          filteringStrategy = new CoreFilteringPlugin();
          break;
        case 'javascript-filtering':
          filteringStrategy = new JavaScriptFilteringStrategy();
          break;
        case 'typescript-filtering':
          filteringStrategy = new TypeScriptFilteringStrategy();
          break;
        case 'nestjs-filtering':
          filteringStrategy = new NestJSFilteringPlugin();
          break;
        case 'react-filtering':
          filteringStrategy = new ReactFilteringPlugin();
          break;
        default:
          console.warn(`Unknown filtering plugin: ${filteringPluginName}`);
          return;
      }
      
      // Register filtering strategy
      this.filteringStrategies.set(filteringPluginName, filteringStrategy);
      
      console.log(`✅ Loaded filtering plugin: ${filteringPluginName}`);
      
    } catch (error) {
      console.warn(`❌ Failed to load filtering plugin ${filteringPluginName}:`, error);
    }
  }
  
  /**
   * Detect project type based on files and dependencies
   */
  private async detectProjectType(projectRoot: string): Promise<string> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      // Check for package.json
      const packageJsonPath = path.join(projectRoot, 'package.json');
      let packageJson: any = {};
      
      try {
        const packageContent = await fs.readFile(packageJsonPath, 'utf-8');
        packageJson = JSON.parse(packageContent);
      } catch (error) {
        // No package.json found
      }
      
      const dependencies = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };
      
      // Detect NestJS
      if (dependencies['@nestjs/core'] || dependencies['@nestjs/common']) {
        // Check if also has React (full-stack)
        if (dependencies['react'] || dependencies['@types/react']) {
          return 'fullstack-nestjs-react';
        }
        return 'nestjs-backend';
      }
      
      // Detect React
      if (dependencies['react'] || dependencies['@types/react']) {
        return 'react-frontend';
      }
      
      // Detect Express
      if (dependencies['express'] || dependencies['@types/express']) {
        return 'express-api';
      }
      
      // Check for TypeScript
      if (dependencies['typescript'] || await this.fileExists(path.join(projectRoot, 'tsconfig.json'))) {
        return 'basic-typescript';
      }
      
      // Default to basic JavaScript
      return 'basic-javascript';
      
    } catch (error) {
      console.warn('Failed to detect project type:', error);
      return 'basic-javascript';
    }
  }
  
  /**
   * Get plugins for specific file
   */
  getPluginsForFile(filePath: string, language: string, framework?: string): AnalysisPlugin[] {
    return Array.from(this.plugins.values())
      .filter(plugin => plugin.canHandle(filePath, language, framework))
      .sort((a, b) => b.metadata.priority - a.metadata.priority);
  }
  
  /**
   * Get filtering strategies
   */
  getFilteringStrategies(): Map<string, PluginFilteringStrategy> {
    return this.filteringStrategies;
  }
  
  /**
   * Get current configuration
   */
  getCurrentConfiguration(): PluginConfiguration | undefined {
    return this.currentConfiguration;
  }
  
  /**
   * List available configurations
   */
  getAvailableConfigurations(): PluginConfiguration[] {
    return Object.values(PLUGIN_CONFIGURATIONS);
  }
  
  /**
   * Cleanup all plugins
   */
  async cleanup(): Promise<void> {
    const cleanupPromises = Array.from(this.plugins.values()).map(plugin => 
      plugin.cleanup().catch(error => {
        console.warn(`Failed to cleanup plugin ${plugin.metadata.name}:`, error);
      })
    );
    
    await Promise.all(cleanupPromises);
    
    this.plugins.clear();
    this.filteringStrategies.clear();
    this.pluginInstances.clear();
  }
  
  // Helper methods
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      const fs = await import('fs/promises');
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Create registry with auto-detection
 */
export async function createPluginRegistry(projectRoot: string): Promise<EnhancedPluginRegistry> {
  const registry = new EnhancedPluginRegistry();
  await registry.autoDetectConfiguration(projectRoot);
  return registry;
}

/**
 * Create registry with specific configuration
 */
export async function createPluginRegistryWithConfig(
  configName: string, 
  projectRoot: string
): Promise<EnhancedPluginRegistry> {
  const registry = new EnhancedPluginRegistry();
  await registry.loadConfiguration(configName, projectRoot);
  return registry;
}
