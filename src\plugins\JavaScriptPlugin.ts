/**
 * Basic JavaScript Plugin
 * 
 * Handles JavaScript syntax analysis using tree-sitter.
 * This is a foundational plugin that other plugins can build upon.
 */

import { 
  AnalysisPlugin, 
  PluginMetadata, 
  PluginCapabilities, 
  PluginAnalysisContext, 
  PluginAnalysisResult,
  PluginFilteringStrategy,
  FilteringOptions
} from '../core/PluginSystem.js';
import { CodeElement, CodeElementType, FileAnalysisResult } from '../types/CodeElement.js';
import { TreeSitterParser } from '../parsers/TreeSitterParser.js';

export class JavaScriptPlugin implements AnalysisPlugin {
  metadata: PluginMetadata = {
    name: 'javascript-core',
    version: '1.0.0',
    description: 'Core JavaScript syntax analysis using tree-sitter',
    author: 'Cyzer Team',
    
    languages: ['javascript'],
    frameworks: [], // Framework-agnostic
    fileExtensions: ['.js', '.mjs', '.cjs'],
    
    capabilities: {
      syntaxAnalysis: true,
      semanticAnalysis: false,
      crossFileAnalysis: true,
      typeInference: false,
      dependencyTracking: true,
      callGraphGeneration: true,
      frameworkPatterns: false,
      decoratorAnalysis: false,
      componentAnalysis: false,
      incrementalAnalysis: false,
      largeCodebaseOptimized: true
    },
    
    priority: 100, // Base priority for core language support
    dependencies: [],
    
    requiresTypeScript: false,
    requiresNodeModules: false,
    memoryIntensive: false
  };
  
  private parser: TreeSitterParser | null = null;
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    this.parser = new TreeSitterParser();
    await this.parser.initQueries(); // Initialize with default queries
  }
  
  async cleanup(): Promise<void> {
    this.parser = null;
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    return language === 'javascript' && 
           this.metadata.fileExtensions.some(ext => filePath.endsWith(ext));
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    const startTime = Date.now();
    
    if (!this.parser) {
      throw new Error('JavaScript plugin not initialized');
    }
    
    try {
      // Use tree-sitter to parse the file
      const parseResult = this.parser.parse(context.fileContent, context.filePath);
      
      // Enhance elements with JavaScript-specific information
      const enhancedElements = this.enhanceElements(parseResult.elements, context);
      
      // Share basic syntax information with other plugins
      const sharedData = new Map<string, any>();
      sharedData.set('javascript-ast', parseResult);
      sharedData.set('javascript-elements', enhancedElements);
      
      return {
        elements: enhancedElements,
        errors: parseResult.errors || [],
        warnings: [],
        metadata: {
          language: 'javascript',
          parser: 'tree-sitter',
          queryVersion: '1.0'
        },
        analysisTime: Date.now() - startTime,
        memoryUsed: 0, // Would need actual measurement
        confidence: 0.9, // High confidence for syntax analysis
        sharedData
      };
      
    } catch (error) {
      return {
        elements: [],
        errors: [`JavaScript analysis failed: ${error}`],
        warnings: [],
        metadata: {},
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0,
      };
    }
  }
  
  /**
   * Enhance elements with JavaScript-specific information
   */
  private enhanceElements(elements: CodeElement[], context: PluginAnalysisContext): CodeElement[] {
    return elements.map(element => {
      // Add JavaScript-specific metadata
      const enhanced = { ...element };
      
      // Detect common JavaScript patterns
      if (element.type === CodeElementType.Function) {
        enhanced.metadata = {
          ...enhanced.metadata,
          isCallback: this.isCallbackFunction(element, context),
          isEventHandler: this.isEventHandler(element, context),
          isAsyncFunction: this.isAsyncFunction(element, context)
        };
      }
      
      if (element.type === CodeElementType.Variable) {
        enhanced.metadata = {
          ...enhanced.metadata,
          isConstant: this.isConstant(element, context),
          isGlobal: this.isGlobal(element, context)
        };
      }
      
      return enhanced;
    });
  }
  
  // Pattern detection methods
  private isCallbackFunction(element: CodeElement, context: PluginAnalysisContext): boolean {
    // Check if function is used as callback (passed to other functions)
    // This would require call graph analysis
    return false; // Implementation needed
  }
  
  private isEventHandler(element: CodeElement, context: PluginAnalysisContext): boolean {
    // Check if function handles events (addEventListener, onClick, etc.)
    return element.name.startsWith('on') || element.name.includes('Handler');
  }
  
  private isAsyncFunction(element: CodeElement, context: PluginAnalysisContext): boolean {
    // Check if function is async or returns Promise
    return element.fullText?.includes('async ') || false;
  }
  
  private isConstant(element: CodeElement, context: PluginAnalysisContext): boolean {
    // Check if variable is declared with const
    return element.fullText?.startsWith('const ') || false;
  }
  
  private isGlobal(element: CodeElement, context: PluginAnalysisContext): boolean {
    // Check if variable is in global scope
    return element.location.startLine === 1; // Simplified check
  }
}

/**
 * JavaScript-specific filtering strategy
 */
export class JavaScriptFilteringStrategy implements PluginFilteringStrategy {
  
  extractRelevantContext(
    focusFile: string,
    allResults: FileAnalysisResult[],
    options: FilteringOptions
  ): any {
    const focusFileResult = allResults.find(f => f.filePath === focusFile);
    if (!focusFileResult) return {};
    
    // Extract JavaScript-specific context
    const jsContext = {
      // Function relationships
      functions: this.extractFunctionInfo(focusFileResult),
      
      // Module patterns
      modulePattern: this.detectModulePattern(focusFileResult),
      
      // Common JavaScript patterns
      patterns: this.detectJavaScriptPatterns(focusFileResult),
      
      // Dependencies
      dependencies: this.extractDependencies(focusFileResult),
    };
    
    return jsContext;
  }
  
  scoreRelevance(
    element: CodeElement,
    focusFile: string,
    context: any
  ): number {
    let score = 0;
    
    // Higher relevance for exported functions/classes
    if (element.isExported) {
      score += 0.3;
    }
    
    // Higher relevance for functions that are called
    if (element.type === CodeElementType.Function) {
      score += 0.2;
    }
    
    // Higher relevance for imports/exports
    if (element.type === CodeElementType.Import || element.type === CodeElementType.Export) {
      score += 0.4;
    }
    
    return Math.min(score, 1.0);
  }
  
  generateSummary(relevantElements: CodeElement[], context: any): any {
    const functions = relevantElements.filter(e => e.type === CodeElementType.Function);
    const classes = relevantElements.filter(e => e.type === CodeElementType.Class);
    const imports = relevantElements.filter(e => e.type === CodeElementType.Import);
    
    return {
      type: 'javascript-summary',
      functions: functions.length,
      classes: classes.length,
      imports: imports.length,
      moduleType: context.modulePattern || 'unknown',
      complexity: this.calculateComplexity(relevantElements)
    };
  }
  
  private extractFunctionInfo(fileResult: FileAnalysisResult) {
    return fileResult.elements
      .filter(e => e.type === CodeElementType.Function)
      .map(f => ({
        name: f.name,
        isExported: f.isExported,
        isAsync: f.metadata?.isAsyncFunction || false
      }));
  }
  
  private detectModulePattern(fileResult: FileAnalysisResult): string {
    const hasImports = fileResult.elements.some(e => e.type === CodeElementType.Import);
    const hasExports = fileResult.elements.some(e => e.type === CodeElementType.Export);
    
    if (hasImports && hasExports) return 'es6-module';
    if (hasExports) return 'es6-export-only';
    if (hasImports) return 'es6-import-only';
    
    // Check for CommonJS patterns
    const hasRequire = fileResult.elements.some(e => 
      e.fullText?.includes('require(') || false
    );
    const hasModuleExports = fileResult.elements.some(e => 
      e.fullText?.includes('module.exports') || false
    );
    
    if (hasRequire && hasModuleExports) return 'commonjs';
    if (hasModuleExports) return 'commonjs-export-only';
    if (hasRequire) return 'commonjs-import-only';
    
    return 'script';
  }
  
  private detectJavaScriptPatterns(fileResult: FileAnalysisResult): string[] {
    const patterns: string[] = [];
    
    // Check for common patterns
    const hasPromises = fileResult.elements.some(e => 
      e.fullText?.includes('Promise') || e.fullText?.includes('.then(') || false
    );
    if (hasPromises) patterns.push('promises');
    
    const hasAsyncAwait = fileResult.elements.some(e => 
      e.fullText?.includes('async ') || e.fullText?.includes('await ') || false
    );
    if (hasAsyncAwait) patterns.push('async-await');
    
    const hasArrowFunctions = fileResult.elements.some(e => 
      e.fullText?.includes('=>') || false
    );
    if (hasArrowFunctions) patterns.push('arrow-functions');
    
    return patterns;
  }
  
  private extractDependencies(fileResult: FileAnalysisResult) {
    return fileResult.elements
      .filter(e => e.type === CodeElementType.Import)
      .map(imp => ({
        source: (imp as any).source,
        names: (imp as any).importedNames || []
      }));
  }
  
  private calculateComplexity(elements: CodeElement[]): 'low' | 'medium' | 'high' {
    const totalElements = elements.length;
    if (totalElements < 10) return 'low';
    if (totalElements < 50) return 'medium';
    return 'high';
  }
}
