# Code Analyzer

A library for analyzing source code structure and relationships, primarily targeting TypeScript and JavaScript projects using Tree-sitter. It extracts code elements (functions, classes, imports, etc.) and builds a basic import dependency graph between files.

## Features

*   Parses JS/TS code using Tree-sitter.
*   Identifies code elements like functions, classes, methods, types, enums, imports, exports.
*   Extracts metadata (location, name, parameters, export status).
*   Uses Tree-sitter queries (`queries/*.scm`) for element identification.
*   Resolves relative imports to build a file-level dependency graph.
*   Provides analysis results including elements per file, errors, and a summary.

## Getting Started

1.  **Install:** `npm install`
2.  **Build:** `npm run build`
3.  **Run Analysis (Example):**
    Create a file `run.ts`:
    ```typescript
    // run.ts
    import { CodeAnalyzer, AnalysisOptions } from './dist/index.js'; // Use compiled output
    import path from 'path';
    import fs from 'fs/promises'; // For writing results

    // Get project path from command line or default to current dir
    const projectPath = path.resolve(process.argv[2] || '.');

    async function run() {
        console.log(`Starting analysis for project at: ${projectPath}`);
        const analyzer = new CodeAnalyzer(projectPath);

        try {
            await analyzer.initialize(); // Initialize parser and queries

            const options: AnalysisOptions = {
                // includeElementContent: true // Uncomment to include full text of elements
            };
            const results = await analyzer.analyzeProject(options);

            console.log(`Analyzed ${results.files.length} files.`);
            console.log("Summary:", results.summary);

            // Optionally write detailed results to a file
            // Be careful, this can be large!
             // await fs.writeFile('analysis-results.json', JSON.stringify(results, null, 2));
             // console.log("Detailed results written to analysis-results.json");

            // Optionally write graph to a file (e.g., in GraphML format)
            if (results.relations) {
                // Requires installing graphology-graphml: npm install graphology-graphml --save-dev
                // import { write } from 'graphology-graphml';
                // const graphml = write(results.relations);
                // await fs.writeFile('dependency-graph.graphml', graphml);
                // console.log("Dependency graph written to dependency-graph.graphml");
            }


        } catch (err) {
            console.error('Analysis failed:', err);
            process.exit(1);
        }
    }

    run();
    ```
    Install `ts-node` if you haven't: `npm install ts-node --save-dev`
    Run the script: `npx ts-node run.ts [optional_path_to_project]`

## Project Structure

*   `src/`: Source code
    *   `core/`: Main analysis logic (`CodeAnalyzer.ts`)
    *   `parsers/`: Code parsing implementation (`TreeSitterParser.ts`)
    *   `types/`: TypeScript type definitions (`CodeElement.ts`)
    *   `index.ts`: Main library entry point.
*   `queries/`: Tree-sitter query files (`.scm`).
*   `dist/`: Compiled JavaScript output.
*   `node_modules/`: Project dependencies.
*   `.gitignore`: Specifies intentionally untracked files.
*   `package.json`: Project manifest.
*   `tsconfig.json`: TypeScript compiler configuration.

## TODO / Next Steps

*   **Refine Element Details:** Improve extraction of function parameters/returns, class properties/methods with visibility, etc., within `TreeSitterParser.ts`.
*   **Enhance Relationship Graph:**
    *   Add nodes for specific code elements (functions, classes) not just files.
    *   Add edges for function calls, inheritance, implementations.
    *   Handle `node_modules` and path aliases in import resolution.
*   **Add More Language Support:** Integrate Tree-sitter parsers and queries for other languages (Python, Java, Go, etc.).
*   **Improve Query Processing:** Make `processCaptures` more robust and less reliant on specific capture naming conventions if possible, potentially using node types more directly.
*   **Add Tests:** Implement unit and integration tests for parsing and analysis logic.
*   **.gitignore Parsing:** Use a library to parse `.gitignore` accurately instead of basic string matching.
*   **Performance Optimization:** Profile and optimize for large codebases.
*   **Serialization Options:** Provide options for serializing the results, especially the graph (JSON, GraphML, etc.).