/**
 * TypeScript Plugin with Integrated Decorator Analysis
 * 
 * Self-contained TypeScript plugin that includes:
 * - TypeScript Compiler API integration
 * - Decorator analysis (generic decorators)
 * - Type inference and semantic analysis
 * - Interface and type alias analysis
 */

import { 
  AnalysisPlugin, 
  PluginMetadata, 
  PluginCapabilities, 
  PluginAnalysisContext, 
  PluginAnalysisResult 
} from '../core/PluginSystem.js';
import { CodeElement, CodeElementType } from '../types/CodeElement.js';
import { JavaScriptPlugin } from '../javascript-plugin/JavaScriptPlugin.js';
import { CompilerService } from './compiler/CompilerService.js';
import { DecoratorAnalyzer } from './analyzers/DecoratorAnalyzer.js';
import { TypeAnalyzer } from './analyzers/TypeAnalyzer.js';
import { InterfaceAnalyzer } from './analyzers/InterfaceAnalyzer.js';
import { DecoratorElement, TypeScriptTypes } from './types/index.js';

export class TypeScriptPlugin implements AnalysisPlugin {
  metadata: PluginMetadata = {
    name: 'typescript-plugin',
    version: '1.0.0',
    description: 'TypeScript analysis with decorators, types, and Compiler API',
    author: 'Cyzer Team',
    
    languages: ['typescript'],
    frameworks: [], // Language plugin, not framework-specific
    fileExtensions: ['.ts', '.tsx', '.mts', '.cts'],
    
    capabilities: {
      syntaxAnalysis: true,
      semanticAnalysis: true,      // ✅ TypeScript Compiler API
      crossFileAnalysis: true,
      typeInference: true,         // ✅ Full type information
      dependencyTracking: true,
      callGraphGeneration: true,
      frameworkPatterns: false,
      decoratorAnalysis: true,     // ✅ Decorators included in TS plugin
      componentAnalysis: false,
      incrementalAnalysis: true,   // ✅ TS supports incremental compilation
      largeCodebaseOptimized: true
    },
    
    priority: 200, // Higher than JavaScript plugin
    dependencies: ['javascript-plugin'], // Builds on JavaScript plugin
    
    requiresTypeScript: true,    // ✅ Uses TypeScript Compiler API
    requiresNodeModules: true,   // ✅ Needs type definitions
    memoryIntensive: true        // ✅ Compiler API is memory-intensive
  };
  
  // Dependencies
  private jsPlugin: JavaScriptPlugin;
  
  // TypeScript-specific services
  private compilerService: CompilerService;
  private decoratorAnalyzer: DecoratorAnalyzer;
  private typeAnalyzer: TypeAnalyzer;
  private interfaceAnalyzer: InterfaceAnalyzer;
  
  constructor() {
    this.jsPlugin = new JavaScriptPlugin();
    this.compilerService = new CompilerService();
    this.decoratorAnalyzer = new DecoratorAnalyzer();
    this.typeAnalyzer = new TypeAnalyzer();
    this.interfaceAnalyzer = new InterfaceAnalyzer();
  }
  
  async initialize(projectRoot: string, options?: any): Promise<void> {
    // Initialize JavaScript plugin first
    await this.jsPlugin.initialize(projectRoot, options);
    
    // Initialize TypeScript Compiler API
    await this.compilerService.initialize(projectRoot);
    
    // Initialize analyzers
    await this.decoratorAnalyzer.initialize(this.compilerService);
    await this.typeAnalyzer.initialize(this.compilerService);
    await this.interfaceAnalyzer.initialize(this.compilerService);
  }
  
  async cleanup(): Promise<void> {
    await this.jsPlugin.cleanup();
    await this.compilerService.cleanup();
  }
  
  canHandle(filePath: string, language: string, framework?: string): boolean {
    return language === 'typescript' && 
           this.metadata.fileExtensions.some(ext => filePath.endsWith(ext));
  }
  
  async analyze(context: PluginAnalysisContext): Promise<PluginAnalysisResult> {
    const startTime = Date.now();
    
    try {
      // 1. Get JavaScript syntax analysis as foundation
      const jsResult = await this.jsPlugin.analyze({
        ...context,
        language: 'javascript' // Treat as JS for syntax
      });
      
      // 2. TypeScript-specific analysis
      const tsAnalysis = await this.analyzeTypeScript(context);
      
      // 3. Decorator analysis (integrated)
      const decoratorAnalysis = await this.decoratorAnalyzer.analyze(context);
      
      // 4. Type analysis
      const typeAnalysis = await this.typeAnalyzer.analyze(context);
      
      // 5. Interface analysis
      const interfaceAnalysis = await this.interfaceAnalyzer.analyze(context);
      
      // 6. Merge all results
      const mergedElements = this.mergeAnalysisResults([
        jsResult.elements,
        tsAnalysis.elements,
        decoratorAnalysis.elements,
        typeAnalysis.elements,
        interfaceAnalysis.elements
      ]);
      
      // 7. Share TypeScript data with other plugins
      const sharedData = new Map([
        ...(jsResult.sharedData || []),
        ['typescript-compiler', this.compilerService],
        ['typescript-decorators', decoratorAnalysis.elements],
        ['typescript-types', typeAnalysis.elements],
        ['typescript-interfaces', interfaceAnalysis.elements]
      ]);
      
      return {
        elements: mergedElements,
        errors: [
          ...(jsResult.errors || []),
          ...(tsAnalysis.errors || []),
          ...(decoratorAnalysis.errors || [])
        ],
        warnings: tsAnalysis.warnings || [],
        metadata: {
          language: 'typescript',
          hasTypeInformation: this.compilerService.isAvailable(),
          hasDecorators: decoratorAnalysis.elements.length > 0,
          compilerVersion: this.compilerService.getVersion(),
          totalTypes: typeAnalysis.elements.length,
          totalInterfaces: interfaceAnalysis.elements.length
        },
        analysisTime: Date.now() - startTime,
        memoryUsed: this.compilerService.getMemoryUsage(),
        confidence: this.compilerService.isAvailable() ? 0.95 : 0.8,
        sharedData
      };
      
    } catch (error) {
      return {
        elements: [],
        errors: [`TypeScript analysis failed: ${error}`],
        warnings: [],
        metadata: {},
        analysisTime: Date.now() - startTime,
        memoryUsed: 0,
        confidence: 0,
      };
    }
  }
  
  /**
   * TypeScript-specific analysis
   */
  private async analyzeTypeScript(context: PluginAnalysisContext): Promise<{
    elements: CodeElement[];
    errors: string[];
    warnings: string[];
  }> {
    
    if (!this.compilerService.isAvailable()) {
      return { 
        elements: [], 
        errors: [], 
        warnings: ['TypeScript Compiler API not available - limited analysis'] 
      };
    }
    
    const elements: CodeElement[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];
    
    try {
      // Get TypeScript source file
      const sourceFile = this.compilerService.getSourceFile(context.filePath);
      if (!sourceFile) {
        errors.push(`File ${context.filePath} not found in TypeScript program`);
        return { elements, errors, warnings };
      }
      
      // Extract TypeScript-specific elements
      const tsElements = await this.extractTypeScriptElements(sourceFile, context);
      elements.push(...tsElements);
      
      // Check for TypeScript errors
      const diagnostics = this.compilerService.getDiagnostics(sourceFile);
      errors.push(...diagnostics);
      
    } catch (error) {
      errors.push(`TypeScript analysis error: ${error}`);
    }
    
    return { elements, errors, warnings };
  }
  
  /**
   * Extract TypeScript-specific elements
   */
  private async extractTypeScriptElements(sourceFile: any, context: PluginAnalysisContext): Promise<CodeElement[]> {
    const elements: CodeElement[] = [];
    
    // This would use the TypeScript Compiler API to extract:
    // - Function signatures with full type information
    // - Class definitions with inheritance and generics
    // - Interface definitions
    // - Type aliases
    // - Enum definitions
    // - Module declarations
    
    // For now, return empty array - full implementation would be extensive
    return elements;
  }
  
  /**
   * Merge analysis results from different analyzers
   */
  private mergeAnalysisResults(elementArrays: CodeElement[][]): CodeElement[] {
    const elementMap = new Map<string, CodeElement>();
    
    // Flatten all elements
    const allElements = elementArrays.flat();
    
    // Merge elements with same name and location
    for (const element of allElements) {
      const key = `${element.name}-${element.location.startLine}-${element.location.startCol}`;
      const existing = elementMap.get(key);
      
      if (existing) {
        // Merge metadata
        elementMap.set(key, {
          ...existing,
          metadata: {
            ...existing.metadata,
            ...element.metadata
          }
        });
      } else {
        elementMap.set(key, element);
      }
    }
    
    return Array.from(elementMap.values());
  }
  
  /**
   * Get TypeScript Compiler Service (for other plugins to use)
   */
  getCompilerService(): CompilerService {
    return this.compilerService;
  }
  
  /**
   * Get decorator elements (for framework plugins)
   */
  getDecoratorElements(result: PluginAnalysisResult): DecoratorElement[] {
    return result.elements.filter((el): el is DecoratorElement => 
      el.type.toString().includes('decorator') || 
      el.metadata?.isDecorator === true
    );
  }
}
