; --- Method Definitions (exclude constructors) ---
(
  (method_definition
    name: (property_identifier) @name) @definition.method
  (#not-eq? @name "constructor")
)

; --- Class Declarations ---
(
  [
    (class
      name: (_) @name)
    (class_declaration
      name: (_) @name)
    (abstract_class_declaration
      name: (_) @name)
  ] @definition.class
)

; --- Interface Declarations ---
(
  (interface_declaration
    name: (type_identifier) @name) @definition.interface
)

; --- Type Alias Declarations ---
(
  (type_alias_declaration
    name: (type_identifier) @name) @definition.type
)

; --- Enum Declarations ---
(
  (enum_declaration
    name: (identifier) @name) @definition.enum
)

; --- Named Function Declarations (including exported) ---
(
  (function_declaration
    name: (identifier) @name) @definition.function
)

(
  (export_statement
    (function_declaration
      name: (identifier) @name) @definition.function)
)

; --- Generator function declarations (including exported) ---
(generator_function_declaration
  name: (identifier) @name) @definition.function

(export_statement
  (generator_function_declaration
    name: (identifier) @name) @definition.function)

; --- Function Expressions and Arrow Functions assigned to variables ---
(
  (lexical_declaration
    (variable_declarator
      name: (identifier) @name
      value: [(arrow_function) (function_expression)]) @definition.function)
)

(
  (variable_declaration
    (variable_declarator
      name: (identifier) @name
      value: [(arrow_function) (function_expression)]) @definition.function)
)

; --- Exported variable declarations (const/let/var) ---
(export_statement
  declaration: [
    (lexical_declaration (variable_declarator name: (identifier) @name))
    (variable_declaration (variable_declarator name: (identifier) @name))
  ]
) @definition.variable

; --- Assignment of function/arrow to identifier or property ---
(assignment_expression
  left: [
    (identifier) @name
    (member_expression
      property: (property_identifier) @name)
  ]
  right: [(arrow_function) (function_expression)]
) @definition.function

; --- Object literal methods ---
(pair
  key: (property_identifier) @name
  value: [(arrow_function) (function_expression)]) @definition.function

; --- Exported assignments (constants, etc) ---
(export_statement value: (assignment_expression left: (identifier) @name right: ([
 (number)
 (string)
 (identifier)
 (undefined)
 (null)
 (new_expression)
 (binary_expression)
 (call_expression)
]))) @definition.constant

; --- Import Statements ---
(import_statement
  source: (string) @import.source
  (import_clause)? @import.clause
) @definition.import

; --- Call references (skip require) ---
(
  (call_expression
    function: (identifier) @name) @reference.call
  (#not-match? @name "^(require)$")
)

(call_expression
  function: (member_expression
    property: (property_identifier) @name)
  arguments: (_) @reference.call)

; --- New expressions (class references) ---
(new_expression
  constructor: (_) @name) @reference.class
